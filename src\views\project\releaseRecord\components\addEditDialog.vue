<!-- 新增编辑弹窗 -->
<template>
  <div class="addEditDialog">
    <el-dialog :title="dialogTitle" :visible.sync="visible" width="810px" @close="closeDialog">
      <el-form ref="form" :model="formData" :rules="rules" label-width="120px" inline>
        <el-form-item label="成果标题" prop="resultTitle">
          <el-input v-model="formData.resultTitle" placeholder="成果标题" clearable style="width: 240px" />
        </el-form-item>
        <el-form-item label="成果类型" prop="resultType">
          <el-select v-model="formData.resultType" clearable filterable style="width: 240px">
            <el-option v-for="item in dict.type.project_outcome_types" :key="item.value" :value="item.value" :label="item.label"/>
          </el-select>
        </el-form-item>
        <el-form-item label="负责项目经理" prop="projectManager">
          <el-select v-model="formData.projectManager" clearable filterable style="width: 240px">
            <el-option v-for="item in dict.type.project_outcome_project_manager" :key="item.value" :value="item.value" :label="item.label"/>
          </el-select>
        </el-form-item>
        <el-form-item label="所属业务大类" prop="businessCategoryMajor">
          <el-select v-model="formData.businessCategoryMajor" clearable filterable style="width: 240px">
            <el-option v-for="item in dict.type.project_outcome_business_category_major" :key="item.value" :value="item.value" :label="item.label"/>
          </el-select>
        </el-form-item>
        <el-form-item label="所属业务小类" prop="businessCategoryMinor">
          <el-select v-model="formData.businessCategoryMinor" clearable filterable style="width: 240px">
            <el-option v-for="item in dict.type.project_outcome_business_category_minor" :key="item.value" :value="item.value" :label="item.label"/>
          </el-select>
        </el-form-item>
        <el-form-item label="耗时天数" prop="durationDays">
          <el-input v-model="formData.durationDays" placeholder="耗时天数" clearable style="width: 240px" />
        </el-form-item>
        <el-form-item label="耗费人力" prop="manpowerCost">
          <el-input v-model="formData.manpowerCost" placeholder="耗费人力" clearable style="width: 240px" />
        </el-form-item>
        <el-form-item label="主开发组" prop="devDepts">
          <el-select v-model="formData.devDepts" multiple collapse-tags clearable filterable style="width: 240px">
            <el-option v-for="item in dict.type.project_outcome_dev_dept" :key="item.value" :value="item.value" :label="item.label"/>
          </el-select>
        </el-form-item>
        <el-form-item label="测试组" prop="testDepts">
          <el-select v-model="formData.testDepts" multiple collapse-tags clearable filterable style="width: 240px">
            <el-option v-for="item in dict.type.project_outcome_test_dept" :key="item.value" :value="item.value" :label="item.label"/>
          </el-select>
        </el-form-item>
        <el-form-item label="其他组" prop="otherDepts">
          <el-select v-model="formData.otherDepts" multiple collapse-tags clearable filterable style="width: 240px">
            <el-option v-for="item in dict.type.project_outcome_other_dept" :key="item.value" :value="item.value" :label="item.label"/>
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间-结束时间" prop="startEndTimeRange" label-width="140px">
          <el-date-picker v-model="formData.startEndTimeRange" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
            :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item prop="resultSummary" class="introduction-item" label-width="100%">
          <template slot="label">
            <span>成果简介</span>
            <el-popover placement="bottom" width="800" trigger="click">
              <div style="white-space: break-spaces;">{{resultSummaryTemplate}}</div>
              <el-button type="text" slot="reference" style="margin-left: 20px;">查看示例模板</el-button>
            </el-popover>
          </template>
          <el-input v-model="formData.resultSummary" placeholder="成果简介" autocomplete="off" type="textarea" :rows="7" :maxlength="1500" show-word-limit></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm" :loading="btnLoading">保存</el-button>
        <el-button @click="closeDialog">取消</el-button>
      </span>
      </el-dialog>
  </div>
</template>
<script>
import { releaseRecordAdd, releaseRecordEdit } from "@/api/system/releaseRecord"
export default {
  dicts: [
    'project_outcome_types',
    'project_outcome_project_manager',
    'project_outcome_business_category_major',
    'project_outcome_business_category_minor',
    'project_outcome_dev_dept',
    'project_outcome_test_dept',
    'project_outcome_other_dept'
  ],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    dialogTitle: {
      type: String
    },
    dialogData: {
      type: Object
    }
  },
  data() {
    return {
      visible: this.dialogVisible,
      btnLoading: false,
      processerOptions: [], // 处理人下拉数据
      formData: {
        id: '',
        resultTitle: '',
        resultType: '',
        projectManager: '',
        businessCategoryMajor: '',
        businessCategoryMinor: '',
        durationDays: '',
        manpowerCost: '',
        devDepts: [],
        testDepts: [],
        otherDepts: [],
        startEndTimeRange: [],
        resultSummary: ''
      },
      rules: {
        resultTitle: [
          { required: true, message: '请输入成果标题', trigger: 'blur' }
        ],
        resultType: [
          { required: true, message: '请选择成果类型', trigger: 'change' }
        ],
        projectManager: [
          { required: true, message: '请选择负责项目经理', trigger: 'change' }
        ],
        businessCategoryMajor: [
          { required: true, message: '请选择所属业务大类', trigger: 'change' }
        ],
        businessCategoryMinor: [
          { required: true, message: '请选择所属业务小类', trigger: 'change' }
        ],
        durationDays: [
          { required: true, message: '请输入耗时天数', trigger: 'blur' }
        ],
        manpowerCost: [
          { required: true, message: '请输入耗费人力', trigger: 'blur' }
        ],
        startEndTimeRange: [
          { required: true, message: '请选择开始时间-结束时间', trigger: 'change' }
        ]
      },
      resultSummaryTemplate: `格式：背景+人员+时间+工作内容+成果\n示例：为加速部门自研测试环境管理平台落地应用，2025 年 5 月 20 日，我组织了全体测试人员开展专项培训。培训内容主要为工单流程介绍、环境组合方式、测试单使用及环境构建。培训过程中，大家积极性高涨，结合实际工作场景踊跃提问，就权限分配、操作优化等方面提出建设性建议，现场互动频繁。经过此次培训，让测试人员全面掌握平台使用方法，更通过答疑与交流，消除使用疑虑，显著提升大家对平台的熟悉度与操作熟练度。未来，测试环境管理平台将成为团队提效利器，助力测试工作标准化、自动化推进，为保障项目质量与交付效率注入新动能。`
    }
  },
  watch: {
    dialogVisible (val) {
      this.visible = val
      if (this.visible && this.dialogData.id) {
        this.formData = Object.assign(this.formData, {
          id: this.dialogData.id,
          resultTitle: this.dialogData.resultTitle,
          resultType: this.dialogData.resultType,
          projectManager: this.dialogData.projectManager,
          businessCategoryMajor: this.dialogData.businessCategoryMajor,
          businessCategoryMinor: this.dialogData.businessCategoryMinor,
          durationDays: this.dialogData.durationDays,
          manpowerCost: this.dialogData.manpowerCost,
          devDepts: !this.dialogData.devDepts ? [] : this.dialogData.devDepts.split(','),
          testDepts: !this.dialogData.testDepts ? [] : this.dialogData.testDepts.split(','),
          otherDepts: !this.dialogData.otherDepts ? [] : this.dialogData.otherDepts.split(','),
          startEndTimeRange: [this.dialogData.startTime, this.dialogData.endTime],
          resultSummary: this.dialogData.resultSummary
        })
      }
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    /** 取消 */
    closeDialog () {
      this.formData = {
        id: '',
        resultTitle: '',
        resultType: '',
        projectManager: '',
        businessCategoryMajor: '',
        businessCategoryMinor: '',
        durationDays: '',
        manpowerCost: '',
        devDepts: [],
        testDepts: [],
        otherDepts: [],
        startEndTimeRange: [],
        resultSummary: ''
      }
      this.resetForm('form')
      this.$emit('update:dialogVisible', false)
    },
    /** 保存 */
    confirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let params = Object.assign({}, this.formData)
          params.devDepts = params.devDepts.join(',')
          params.testDepts = params.testDepts.join(',')
          params.otherDepts = params.otherDepts.join(',')
          params.startTime = this.formData.startEndTimeRange[0]
          params.endTime = this.formData.startEndTimeRange[1]
          delete params.startEndTimeRange
          let apiName = releaseRecordAdd
          if (this.formData.id) {
            apiName = releaseRecordEdit
          }
          this.btnLoading = true
          apiName(params).then(res => {
            this.btnLoading = false
            if (res.code === 200) {
              this.$message.success(res.msg)
              this.closeDialog()
              this.$emit('callback')
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.introduction-item {
  ::v-deep .el-form-item__label {
    text-align: left;
    margin-left: 20px;
  }
  .el-textarea {
    width: 720px;
    margin-left: 20px;
  }
}
.addEditDialog ::v-deep .el-input__count {
  bottom: -16px;
  line-height: 12px;
}
</style>
