<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts'

export default {
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '450px'
    },
    chartData: {
      type: Object,
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    xAxis: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.setOptions(this.chartData)
    },
    setOptions(data) {
      const series = []
      const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666']
      const names = ['开发', '测试', '项管', '组长']
      
      // 计算每个月份的总和
      const monthTotals = this.xAxis.map((_, index) => {
        return [1, 2, 3, 4].reduce((sum, type) => {
          return sum + (data[type]?.[index] || 0)
        }, 0)
      })
      
      // 为每种类型创建堆叠系列
      for (let i = 1; i <= 4; i++) {
        series.push({
          name: names[i-1],
          type: 'bar',
          stack: 'total',
          emphasis: {
            focus: 'series'
          },
          data: data[i] || [],
          itemStyle: {
            color: colors[i-1]
          },
          label: {
            show: true,
            position: 'inside',
            formatter: function(params) {
              return params.value || ''
            }
          }
        })
      }

      this.chart.setOption({
        title: {
          text: this.title,
          left: 'center',
          top: 10
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: names,
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.xAxis,
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: {
          type: 'value',
          name: '人数'
        },
        series: series
      })
    }
  }
}
</script>