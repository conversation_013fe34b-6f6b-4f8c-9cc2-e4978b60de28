<template>
  <div class="app-container">
    <el-form :model="queryForm" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="审批编号" prop="processInstanceId">
        <el-input v-model="queryForm.processInstanceId" clearable @keyup.enter.native="handleCurrentChange(1)" />
      </el-form-item>
      <el-form-item label="业务大类" prop="businessCategory">
        <el-select v-model="queryForm.businessCategory" clearable>
          <el-option v-for="item in handleSelectData('业务大类')"
                     :key="item"
                     :value="item"
                     :label="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="看板类型" prop="boardType">
        <el-select v-model="queryForm.boardType" clearable>
          <el-option v-for="item in handleSelectData('看板类型')"
                     :key="item"
                     :value="item"
                     :label="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="需求优先度" prop="priorityLevel">
        <el-select v-model="queryForm.priorityLevel" clearable>
          <el-option v-for="item in handleSelectData('需求优先度')"
                     :key="item"
                     :value="item"
                     :label="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="当前审批结果" prop="result">
        <el-select v-model="queryForm.result" clearable>
          <el-option v-for="item in handleSelectData('当前审批结果')"
                     :key="item"
                     :value="item"
                     :label="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="当前审批状态" prop="status">
        <el-select v-model="queryForm.status" clearable>
          <el-option v-for="item in handleSelectData('当前审批状态')"
                     :key="item"
                     :value="item"
                     :label="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="当前负责人" prop="currentResponsiblePerson">
        <el-input v-model="queryForm.currentResponsiblePerson" clearable @keyup.enter.native="handleCurrentChange(1)" />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="queryForm.originatorTimeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateChange">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleCurrentChange(1)">查询</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" sizi="mini" style="width: 100%" v-loading="loading" stripe ref="assistanceTable"
              :default-sort="{ prop: 'originatorTime', order: 'descending' }"
              height="calc(100vh - 280px)"
              @sort-change="handleSortChange" >
      <el-table-column prop="processInstanceId" label="审批编号" align="center"></el-table-column>
      <el-table-column prop="businessCategory" label="业务大类" align="center"></el-table-column>
      <el-table-column prop="boardType" label="看板类型" align="center">
        <template v-slot="scope">
          {{JSON.parse(scope.row.boardType).join('，')}}
        </template>
      </el-table-column>
      <el-table-column prop="requirementTitle" label="需求标题" align="center"></el-table-column>
      <el-table-column prop="expectedCompletionDate" label="期望完成日期 " align="center" sortable="custom">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expectedCompletionDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="priorityLevel" label="需求优先度" align="center"></el-table-column>
      <el-table-column prop="result" label="当前审批结果" align="center" ></el-table-column>
      <el-table-column prop="status" label="当前审批状态" align="center" ></el-table-column>
      <el-table-column align="center" label="审批终态">
        <template slot-scope="scope">
          <span>{{  scope.row.status === '已结束' ? scope.row.result : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="currentNode" label="当前节点" align="center" ></el-table-column>
      <el-table-column prop="currentResponsiblePerson" label="当前负责人" align="center"></el-table-column>
      <el-table-column prop="lastActionTime" label="更新时间" align="center" sortable="custom"></el-table-column>
      <el-table-column prop="originatorUser" label="创建人" align="center"></el-table-column>
      <el-table-column prop="originatorTime" label="创建时间" align="center" sortable="custom"></el-table-column>
    </el-table>
    <div style="margin-top: 20px;text-align: center">
      <el-pagination
        background
        :total="total"
        :current-page="queryForm.pageNum"
        :page-size="queryForm.pageSize"
        :current-page.sync="queryForm.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { listProcessOperationsDashboard } from "@/api/process/operationsDashboard";
import { listDatas } from "@/api/system/dict/data";
export default {
  name: "MaintenanceAssistance",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      total: 0,
      // 查询参数
      queryForm: {
        pageNum: 1,
        pageSize: 50,
        orderByColumn: 'originatorTime',
        isAsc: 'desc',
        processInstanceId: '',
        businessCategory: '',
        boardType: '',
        priorityLevel: '',
        result: '',
        status: '',
        currentResponsiblePerson: '',
        beginDate: '',
        endDate: '',
        originatorTimeRange: '',
      },
      tableData: [],
      selectDate: []
    };
  },
  async created() {
    this.loadSelectData();
    this.initTime()
  },
  methods: {
    //加载下拉框数据
    async loadSelectData() {
      await listDatas({ dictType: 'process_operations_dashboard' }).then(res => {
        if (res.code === 200) {
          this.selectDate = res.data
        }
      })
    },
    // 处理下拉框数据
    handleSelectData(dictLabel) {
      const selectDate = this.selectDate.filter(item => item.dictLabel === dictLabel)
      if( selectDate && selectDate.length > 0){
        return selectDate[0].dictValue.split(',');
      }else {
        return []
      }
    },
    // 处理时间
    initTime() {
      // 获取当前日期
      const currentDate = new Date();
      // 获取前 7 天的日期
      const sevenDaysAgo = new Date(currentDate);
      sevenDaysAgo.setDate(currentDate.getDate() - 6);
      // 设置开始时间为当天的00:00:00
      sevenDaysAgo.setHours(0, 0, 0, 0);
      // 设置结束时间为当天的23:59:59
      const endOfDay = new Date(currentDate);
      endOfDay.setHours(23, 59, 59, 999);
      // 设置时间范围
      this.queryForm.originatorTimeRange = [
        sevenDaysAgo,
        endOfDay
      ];
      this.queryForm.beginDate = this.formatDate(sevenDaysAgo);
      this.queryForm.endDate = this.formatDate(endOfDay);
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    handleDateChange(){
      if (this.queryForm.originatorTimeRange && this.queryForm.originatorTimeRange.length === 2) {
        const endTime = new Date(this.queryForm.originatorTimeRange[1]);
        endTime.setHours(23, 59, 59, 999);
        this.queryForm.originatorTimeRange[1] = endTime;
        this.queryForm.beginDate = this.formatDate(this.queryForm.originatorTimeRange[0]);
        this.queryForm.endDate = this.formatDate(this.queryForm.originatorTimeRange[1]);
      }else{
        this.queryForm.beginDate = null;
        this.queryForm.endDate = null;
      }
    },
    // 排序
    handleSortChange (data) {
      this.queryForm.orderByColumn = data.prop
      if (data.order === 'ascending') {
        this.queryForm.isAsc = 'asc'
      } else if (data.order === 'descending') {
        this.queryForm.isAsc = 'desc'
      } else {
        this.queryForm.isAsc = ''
      }
      this.handleCurrentChange(1)
    },
    // 查询运营看板及报需求列表
    handleQuery () {
      this.loading = true
      listProcessOperationsDashboard(this.queryForm).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.total = res.total
          this.tableData = res.rows
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 改变每页显示数量
    handleSizeChange(val) {
      this.queryForm.pageSize = val
      this.handleQuery()
    },
    // 改变当前页码
    handleCurrentChange(val) {
      this.queryForm.pageNum = val
      this.handleQuery()
    },
    // 重置
    resetQuery (){
      this.queryForm = {
        pageNum: 1,
        pageSize: 50,
        orderByColumn: 'originatorTime',
        isAsc: 'desc',
        processInstanceId: '',
        businessCategory: '',
        boardType: '',
        priorityLevel: '',
        result: '',
        status: '',
        currentResponsiblePerson: '',
        beginDate: '',
        endDate: '',
        originatorTimeRange: '',
      }
      this.initTime()
      this.$refs.assistanceTable.clearSort();
      this.$refs.assistanceTable.sort('originatorTime', 'descending');
      this.handleQuery()
    },
    // 将分钟数转换为天-小时-分钟格式
    formatMinutes(minutes) {
      if (typeof minutes !== 'number' || minutes < 0) {
        return '';
      }
      const days = Math.floor(minutes / (24 * 60)); // 计算天数
      const hours = Math.floor((minutes % (24 * 60)) / 60); // 计算小时数
      const mins = minutes % 60; // 计算分钟数

      let result = '';
      if (minutes < 60) return `${mins}分钟`;
      if (days > 0) result += `${days}天`;
      result += `${hours}小时`;
      result += `${mins}分钟`;
      return result;
    }
  }
};
</script>
