<template>
  <div class="chart-root">
    <el-alert
      :title="updateTime"
      :closable="false"
      type="info">
    </el-alert>
    <StackedBarChartComponent :chart-data="chartData" :title="title" :x-axis="monthList" />
  </div>
</template>

<script>
import StackedBarChartComponent from './StackedBarChartComponent.vue'
import { getHomeTrend } from '@/api/system/homeTrend'

export default {
  components: { StackedBarChartComponent },
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      monthList: [],
      chartData: {},
      updateTime: ''
    }
  },
  created() {
    this.getAllData()
  },
  methods: {
    getAllData() {
      // 获取所有类型的数据
      const promises = [5, 6, 7, 11, 8, 12].map(type => getHomeTrend(type))
      Promise.all(promises).then(responses => {
        // 使用第一个响应的月份列表
        this.monthList = responses[0].data.monthList
        this.updateTime = '最后更新时间：' + responses[0].data.updateTime
        
        // 合并所有数据
        this.chartData = {
          5: responses[0].data.trendList,
          6: responses[1].data.trendList,
          7: responses[2].data.trendList,
          11: responses[3].data.trendList,
          8: responses[4].data.trendList,
          12: responses[5].data.trendList
        }
      })
    }
  }
}
</script>

<style scoped>
.chart-root {
  width: 100%;
  height: 100%;
  min-height: 0;
  display: flex;
  flex-direction: column;
}
.el-alert {
  margin-bottom: 10px;
}
</style> 