import request from '@/utils/request'

// 查询绩效统计列表
export function listPerfStat(query) {
  return request({
    url: '/business/imsPerformStat/getPerfStatList',
    method: 'get',
    params: query
  })
}

// 根据年月获取各组文档统计
export function listDocStat(query) {
  return request({
    url: '/business/imsPerformStat/getDocStatList',
    method: 'get',
    params: query
  })
}

// 根据年月获取各组文档统计
export function listDocByGroup(query) {
  return request({
    url: '/business/imsPerformStat/getDocListByGroup',
    method: 'get',
    params: query
  })
}

// 根据年月获取每个人的文档数
export function listDocCount(query) {
  return request({
    url: '/business/imsPerformStat/getDocCountList',
    method: 'get',
    params: query
  })
}

// 根据年月获取每月各组绩效合计
export function listWorkTotal(query) {
  return request({
    url: '/business/imsPerformStat/getWorkTotal',
    method: 'get',
    params: query
  })
}

// 获取文档库统计
export function listDocLibStatistics(query) {
  return request({
    url: '/business/imsPerformStat/getDocLibStatistics',
    method: 'get',
    params: query
  })
}


