<template>
  <div class="app-container">
    <div class="table">
      <div>
        <el-form :inline="true" @submit.native.prevent ref="queryForm" :model="queryForm" label-width="100px" size="small">
          <el-form-item label="所属产品：">
            <el-select clearable placeholder="请选择" v-model="queryForm.productId">
              <el-option :value='null' label="全部"></el-option>
              <el-option v-for="item in productData" :key="item.id" :value="item.id" :label="item.name"/>
            </el-select>
          </el-form-item>
          <el-form-item label="当前状态：">
            <el-select clearable placeholder="请选择" v-model="queryForm.status">
              <el-option v-for="item in optionsData" :key="item.value" :value="item.value" :label="item.name"/>
            </el-select>
          </el-form-item>
          <el-form-item label="时间:">
            <el-date-picker v-model="queryForm.beginDate" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd 00:00:00" />
            <span>&nbsp;&nbsp;&nbsp;&nbsp;至&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
            <el-date-picker v-model="queryForm.endDate" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd 23:59:59" />
          </el-form-item>
          <el-form-item label="需求标题:">
            <el-input v-model="queryForm.title" placeholder="请输入需求标题" clearable></el-input>
          </el-form-item>
          <el-form-item label="成果编码:">
            <el-input v-model="queryForm.resultCode" placeholder="请输入成果编码" clearable></el-input>
          </el-form-item>
          <el-form-item label="禅道需求id:">
            <el-input v-model="queryForm.id" placeholder="请输入禅道需求id" clearable></el-input>
          </el-form-item>
          <el-form-item label="跟进人:">
            <el-select clearable placeholder="请选择" v-model="queryForm.assignedTo">
              <el-option value="" label="全部"></el-option>
              <el-option v-for="item in dict.type.project_outcome_project_manager" :key="item.value" :value="item.label" :label="item.label"/>
            </el-select>
          </el-form-item>
          <el-form-item label="评审人:">
            <el-select clearable placeholder="请选择" v-model="queryForm.reviewedBy">
              <el-option value="" label="全部"></el-option>
              <el-option v-for="item in dict.type.project_outcome_project_manager" :key="item.value" :value="item.value" :label="item.label"/>
            </el-select>
          </el-form-item>
          <el-form-item style="margin-left: 50px">
            <el-checkbox v-model="queryForm.noJoinResult">仅展示未加入成果的需求</el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="handleQuery" size="mini">查询</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery" size="mini">重置</el-button>
          </el-form-item>
          <el-form-item label="提示：">
            <span>双击某行可打开禅道的对应明细页面</span>
          </el-form-item>
        </el-form>
      </div>
      <!-- 操作按钮 -->
      <div style="margin-bottom: 10px;">
        <el-button type="primary" size="mini" v-hasPermi="['system:projectResult:generate']" @click="handleGenerateResults" :disabled="!selectedRows.length">生成成果</el-button>
        <el-button type="primary" size="mini" v-hasPermi="['system:projectResult:join']" @click="handleJoinResults" :disabled="!selectedRows.length">加入成果</el-button>
        <el-button type="danger" size="mini" v-hasPermi="['system:projectResult:cannel']" @click="handleCancelJoinResults" :disabled="!selectedRows.length">取消成果关联</el-button>
      </div>

      <el-table v-loading="listLoading" border stripe :data="tableData" style="width: 100%"
                @selection-change="handleSelectionChange" @sort-change="onSortChange"
                @row-dblclick="handleTableRow" height="calc(100vh - 360px)" ref="tableList">
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column label="所属产品" width="180" prop="productName" />
        <el-table-column label="成果编码" width="150" prop="resultCode" />
        <el-table-column label="需求标题" width="200" prop="title" />
        <el-table-column label="禅道需求ID" width="120" prop="id" sortable />
        <el-table-column label="当前状态" prop="status" width="100" />
        <el-table-column label="所处阶段" width="100" prop="stage" />
        <el-table-column label="创建人" width="100" prop="openedBy" />
        <el-table-column label="创建时间" width="100" prop="openedDate" sortable />
        <el-table-column label="跟进人" width="100" prop="assignedTo" />
        <el-table-column label="跟进时间" width="100" prop="assignedDate" sortable />
        <el-table-column label="评审人" width="100" prop="reviewer" />
        <el-table-column label="评审时间" width="100" prop="reviewedDate" sortable />
        <el-table-column label="关闭人" width="100" prop="closedBy" />
        <el-table-column label="关闭时间" width="100" prop="closedDate" sortable />
        <el-table-column label="关闭原因" width="100" prop="closedReason" />
        <el-table-column label="用例总量" width="100" prop="caseCount" sortable />
        <el-table-column label="bug总量" width="100" prop="bugCount" sortable />
        <el-table-column label="任务数" width="100" prop="taskCount" sortable />
        <el-table-column label="发布数" width="100" prop="releaseCount" sortable />
      </el-table>
    </div>
    <div class="pagination-container">
      <pagination
        :total="total"
        :page.sync="queryForm.pageNum"
        :limit.sync="queryForm.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 生成成果弹窗 -->
    <generate-result-dialog
      :visible.sync="generateDialog.show"
      :selectedStoryIds="selectedStoryIds"
      @confirm="handleGenerateConfirm"/>
    <!-- 加入成果弹窗 -->
    <join-result-dialog :visible.sync="joinDialog.show" @confirm="handleJoinConfirm"/>

  </div>
</template>

<script>
import { getDemandPage } from "@/api/demandQuery";
import { storyResultList } from "@/api/project/storyResult";
import { productList } from "@/api/commonBiz";
import { cannelJoinResults } from "@/api/project/projectResult";
import GenerateResultDialog from "./components/GenerateResultDialog.vue";
import JoinResultDialog from "./components/JoinResultDialog.vue";

export default {
  name: "Demand",
  dicts: [
    'project_outcome_project_manager'
  ],
  components: {
    GenerateResultDialog,
    JoinResultDialog
  },
  data() {
    return {
      tableData: [], // 表格数据列表
      listLoading: false,
      total:0,
      queryForm: {
        productId: null,
        status: "all",
        beginDate: "",
        endDate: "",
        pageSize: 50,
        pageNum: 1,
        title: "",
        id: null,
        assignedTo: "",
        reviewedBy: "",
        noJoinResult: true,
        resultCode: "",
      },
      productData: [],
      optionsData: [
        { value: "all", name: "全部",},
        { value: "draft", name: "草稿",},
        { value: "active", name: "活跃",},
        { value: "changed", name: "变更",},
        { value: "closed", name: "关闭",},
      ],
      // 选中的行数据
      selectedRows: [],
      // 生成成果弹窗
      generateDialog: {
        show: false
      },
      // 加入成果弹窗
      joinDialog: {
        show: false
      }
    };
  },
  computed: {
    // 获取选中的需求ID列表
    selectedStoryIds() {
      return this.selectedRows.map(row => row.id);
    }
  },
  watch: {
    "$route.query": {
      handler(newVal) {
        // 检查是否路由到当前组件
        if (this.$route.name !== "Demand") {
          return; // 如果不是需求列表组件则直接返回
        }
        // 处理从项目成果页面传递过来的成果编码参数
        if (this.$route.query.resultCode) {
          this.queryForm = {
            productId: null,
            status: "all",
            beginDate: "",
            endDate: "",
            pageSize: 50,
            pageNum: 1,
            title: "",
            id: null,
            assignedTo: "",
            reviewedBy: "",
            noJoinResult: false,
            resultCode: this.$route.query.resultCode,
          }
          // 自动执行查询
          this.$nextTick(() => {
            this.$refs.tableList.clearSort();
            this.handleQuery();
          });
          const newQuery = { ...this.$route.query };
          delete newQuery.resultCode;
          this.$router.replace({ query: newQuery });
        }
        // 处理原有的params参数
        if (this.$route.query.params) {
          console.log("路由参数", this.$route.query.params);
          let params = JSON.parse(this.$route.query.params);
          this.queryForm.productId = Number(params.productId);
          this.queryForm.status = params.status;
          setTimeout(() => {
            this.queryForm.beginDate = params.beginDate;
            this.queryForm.endDate = params.endDate;
            this.handleQuery();
          }, 1000);
          const newQuery = { ...this.$route.query };
          delete newQuery.params;
          // 将剩余的查询参数设置回路由
          this.$router.replace({ query: newQuery });
        }
      },
      // 一进页面就执行
      immediate: true,
      deep: true,
    },
  },
  methods: {
    // 点击排序
    onSortChange(column) {
        this.queryForm.orderByField = column.prop;
        this.queryForm.orderRule = column.order === "ascending" ? "asc" : "desc";
        // 获取后台列表数据
        this.getList();
    },
    // 查询
    handleQuery() {
      this.getList();
    },
    // 重置
    resetQuery() {
      this.$refs.queryForm.resetFields();
      this.$refs.tableList.clearSort();
      this.queryForm = {
        productId: null,
        status: "all",
        beginDate: "",
        endDate: "",
        pageSize: 50,
        pageNum: 1,
        title: "",
        id: null,
        assignedTo: "",
        reviewedBy: "",
        noJoinResult: true,
        resultCode: "",
      }
      this.initTime();
      this.getList();
    },
    // 下拉框选择
    handleSelectionChange(val) {
      this.selectedRows = val;
    },
    // 初始化列表
    async getList() {
      this.listLoading = true;
      await storyResultList(this.queryForm).then(resp => {
        if (resp.code === 200) {
          this.tableData = resp.rows;
          this.total = resp.total;
          this.listLoading = false;
        } else {
          this.listLoading = false;
          this.$message.error(resp.msg);
        }
      });
    },
    // 处理时间
    getFormatDate(date) {
      var month = date.getMonth() + 1;
      var strDate = date.getDate();
      if (month >= 1 && month <= 9) {
        month = "0" + month;
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
      }
      var currentDate =
        date.getFullYear() +
        "-" +
        month +
        "-" +
        strDate +
        " " +
        date.getHours() +
        ":" +
        date.getMinutes() +
        ":" +
        date.getSeconds();
      return currentDate;
    },
    // 初始化时间
    initTime() {
      const endDate = this.getFormatDate(new Date()).substr(0, 11) + "23:59:59";
      const beginDate = this.getFormatDate(new Date(new Date() - 3600 * 1000 * 24 * 29)).substr(0, 11) + "00:00:00";
      this.queryForm.beginDate = beginDate;
      this.queryForm.endDate = endDate;
    },
    // 双击table表行
    handleTableRow(row, event, column) {
      const url = "http://pm.qmqb.top/story-view-" + row.id + ".html/";
      window.open(url, "_blank");
    },
    // 获取产品下拉框列表
    getProductList() {
      productList().then((res) => {
        if (res.code === 200) {
          this.productData = res.data;
        } else {
          this.$message.error(res.msg);
        }
      });
    },


    // 生成成果弹窗确认
    handleGenerateConfirm(formData) {
      console.log('生成成果表单数据:', formData)
      // 弹窗已经在子组件中关闭，这里只需要刷新列表
      this.handleQuery()
    },
    // 加入成果弹窗确认
    handleJoinConfirm(selectedResults) {
      console.log('选中的成果:', selectedResults)
      // 弹窗已经在子组件中关闭，这里只需要刷新列表
      this.handleQuery()
    },
    // 打开生成成果弹窗
    handleGenerateResults() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要生成成果的需求')
        return
      }
      this.generateDialog.show = true
    },
    // 打开加入成果弹窗
    handleJoinResults() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要加入成果的需求')
        return
      }
      this.joinDialog.show = true
    },

    // 取消成果关联
    handleCancelJoinResults() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要取消关联的需求')
        return
      }
      // 显示确认弹窗
      this.$confirm('取消后需求不再关联成果，是否确定？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.callCancelJoinApi()
      }).catch(() => {
        // 用户取消操作
      })
    },

    // 调用取消成果关联接口
    async callCancelJoinApi() {
      try {
        this.listLoading = true
        const storyIds = this.selectedRows.map(row => row.id)
        const response = await cannelJoinResults(storyIds)
        if (response.code === 200) {
          this.$message.success('取消成果关联成功')
          this.handleQuery()
          this.selectedRows = []
        } else {
          this.$message.error(response.msg || '取消成果关联失败')
        }
      } catch (error) {
        console.error('取消成果关联失败:', error)
      } finally {
        this.listLoading = false
      }
    },
  },
  async created() {
    await this.getProductList();
    if (!this.$route.query.highlight) {
      await this.initTime();
      this.getList()
    }

  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
.table {
  background-color: #fff;
  .tiltle-cn {
    color: #2184d8;
  }
  .control-cell {
    display: flex;
    flex-direction: row;
    .control-cell-item {
      margin-left: 10px;
    }
  }
}
.pagination-container {
  text-align: center;
  padding: 0 !important;
}



</style>
