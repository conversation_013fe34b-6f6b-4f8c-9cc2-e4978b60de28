<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="API渠道基础配置ID" prop="apiBaseConfId">
        <el-input
          v-model="queryParams.apiBaseConfId"
          placeholder="请输入API渠道基础配置ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模板属性key" prop="templateParamKey">
        <el-input
          v-model="queryParams.templateParamKey"
          placeholder="请输入模板属性key"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="API参数属性key" prop="apiParamKey">
        <el-input
          v-model="queryParams.apiParamKey"
          placeholder="请输入API参数属性key"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="映射方式" prop="mappingType">
        <el-select v-model="queryParams.mappingType" placeholder="请选择映射方式" clearable>
          <el-option
            v-for="dict in dict.type.mapping_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:apiParamsMappingConf:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:apiParamsMappingConf:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:apiParamsMappingConf:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:apiParamsMappingConf:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="apiParamsMappingConfList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" v-if="false"/>
      <el-table-column label="API渠道基础配置ID" align="center" prop="apiBaseConfId" />
      <el-table-column label="模板属性key" align="center" prop="templateParamKey" />
      <el-table-column label="API参数属性key" align="center" prop="apiParamKey" />
      <el-table-column label="模板属性是否必填" align="center" prop="templateParamIsMust">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.yes_no" :value="scope.row.templateParamIsMust"/>
        </template>
      </el-table-column>
      <el-table-column label="API属性是否必填" align="center" prop="apiParamIsMust">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.yes_no" :value="scope.row.apiParamIsMust"/>
        </template>
      </el-table-column>
      <el-table-column label="API字段属性描述" align="center" prop="apiParamDescription" />
      <el-table-column label="API属性校验规则" align="center" prop="apiParamVerifyRule">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.api_param_verify_rule" :value="scope.row.apiParamVerifyRule"/>
        </template>
      </el-table-column>
      <el-table-column label="映射方式" align="center" prop="mappingType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.mapping_type" :value="scope.row.mappingType"/>
        </template>
      </el-table-column>
      <el-table-column label="枚举映射配置类型" align="center" prop="dictMappingType" />
      <el-table-column label="映射组件" align="center" prop="mappingComponent">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.mapping_component" :value="scope.row.mappingComponent"/>
        </template>
      </el-table-column>
      <el-table-column label="映射组件下的取值方式" align="center" prop="valueMethodForComponent" />
      <el-table-column label="扩展Json" align="center" prop="extend" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:apiParamsMappingConf:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:apiParamsMappingConf:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改API参数映射配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="API渠道基础配置ID" prop="apiBaseConfId">
          <el-input v-model="form.apiBaseConfId" placeholder="请输入API渠道基础配置ID" />
        </el-form-item>
        <el-form-item label="模板属性key" prop="templateParamKey">
          <el-input v-model="form.templateParamKey" placeholder="请输入模板属性key" />
        </el-form-item>
        <el-form-item label="API参数属性key" prop="apiParamKey">
          <el-input v-model="form.apiParamKey" placeholder="请输入API参数属性key" />
        </el-form-item>
        <el-form-item label="模板属性是否必填" prop="templateParamIsMust">
          <el-input v-model="form.templateParamIsMust" placeholder="请输入模板属性是否必填" />
        </el-form-item>
        <el-form-item label="API属性是否必填" prop="apiParamIsMust">
          <el-input v-model="form.apiParamIsMust" placeholder="请输入API属性是否必填" />
        </el-form-item>
        <el-form-item label="API字段属性描述" prop="apiParamDescription">
          <el-input v-model="form.apiParamDescription" placeholder="请输入API字段属性描述" />
        </el-form-item>
        <el-form-item label="API属性校验规则" prop="apiParamVerifyRule">
          <el-select v-model="form.apiParamVerifyRule" placeholder="请选择API属性校验规则">
            <el-option
              v-for="dict in dict.type.api_param_verify_rule"
              :key="dict.value"
              :label="dict.label"
:value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="映射方式" prop="mappingType">
          <el-select v-model="form.mappingType" placeholder="请选择映射方式">
            <el-option
              v-for="dict in dict.type.mapping_type"
              :key="dict.value"
              :label="dict.label"
:value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="映射组件" prop="mappingComponent">
          <el-select v-model="form.mappingComponent" placeholder="请选择映射组件">
            <el-option
              v-for="dict in dict.type.mapping_component"
              :key="dict.value"
              :label="dict.label"
:value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="映射组件下的取值方式" prop="valueMethodForComponent">
          <el-input v-model="form.valueMethodForComponent" placeholder="请输入映射组件下的取值方式" />
        </el-form-item>
        <el-form-item label="扩展Json" prop="extend">
          <el-input v-model="form.extend" placeholder="请输入扩展Json" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listApiParamsMappingConf, getApiParamsMappingConf, delApiParamsMappingConf, addApiParamsMappingConf, updateApiParamsMappingConf } from "@/api/system/apiParamsMappingConf";

export default {
  name: "ApiParamsMappingConf",
  dicts: ['mapping_type', 'mapping_component', 'api_param_verify_rule'],
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // API参数映射配置表格数据
      apiParamsMappingConfList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        apiBaseConfId: undefined,
        templateParamKey: undefined,
        apiParamKey: undefined,
        mappingType: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        apiBaseConfId: [
          { required: true, message: "API渠道基础配置ID不能为空", trigger: "blur" }
        ],
        templateParamKey: [
          { required: true, message: "模板属性key不能为空", trigger: "blur" }
        ],
        apiParamKey: [
          { required: true, message: "API参数属性key不能为空", trigger: "blur" }
        ],
        templateParamIsMust: [
          { required: true, message: "模板属性是否必填不能为空", trigger: "blur" }
        ],
        apiParamIsMust: [
          { required: true, message: "API属性是否必填不能为空", trigger: "blur" }
        ],
        apiParamDescription: [
          { required: true, message: "API字段属性描述不能为空", trigger: "blur" }
        ],
        apiParamVerifyRule: [
          { required: true, message: "API属性校验规则不能为空", trigger: "change" }
        ],
        mappingType: [
          { required: true, message: "映射方式不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询API参数映射配置列表 */
    getList() {
      this.loading = true;
      listApiParamsMappingConf(this.queryParams).then(response => {
        this.apiParamsMappingConfList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        apiBaseConfId: undefined,
        templateParamKey: undefined,
        apiParamKey: undefined,
        templateParamIsMust: undefined,
        apiParamIsMust: undefined,
        apiParamDescription: undefined,
        apiParamVerifyRule: undefined,
        mappingType: undefined,
        dictMappingType: undefined,
        mappingComponent: undefined,
        valueMethodForComponent: undefined,
        extend: undefined,
        remark: undefined,
        tenantId: undefined,
        revision: undefined,
        createdBy: undefined,
        createdTime: undefined,
        updatedBy: undefined,
        updatedTime: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加API参数映射配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getApiParamsMappingConf(id).then(response => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = "修改API参数映射配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          if (this.form.id != null) {
            updateApiParamsMappingConf(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addApiParamsMappingConf(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除API参数映射配置编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delApiParamsMappingConf(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/apiParamsMappingConf/export', {
        ...this.queryParams
      }, `apiParamsMappingConf_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
