import request from '@/utils/request'


export function getTaskPage(data) {
    return request({
      url: '/task/query/page',
      method: 'get',
      params: data,
    })
  }

export function listTaskByGroup(query) {
  return request({
    url: '/task/query/getTaskListByGroup',
    method: 'get',
    params: query
  })
}

export function statisticsNoDoing(query) {
  return request({
    url: '/task/statistics/noDoing',
    method: 'get',
    params: query
  })
}

export function getTechnologyTaskCount(query) {
  return request({
    url: '/system/taskStat/statByYear',
    method: 'get',
    params: query
  })
}
