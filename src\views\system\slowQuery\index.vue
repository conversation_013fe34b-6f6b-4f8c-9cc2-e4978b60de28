<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
             label-width="68px">
      <el-form-item label="库名" prop="dbName">
        <el-select
          v-model="queryParams.dbName"
          clearable filterable
          style="width: 240px"
        >
          <el-option :value="null" label="全部"/>
          <el-option
            v-for="item in systemOptions"
            :key="item.dbCode"
            :label="item.dbName"
            :value="item.dbCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="IP" prop="ip">
        <el-input
          v-model="queryParams.ip"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="严重程度" prop="warnLevel">
        <el-select
          v-model="queryParams.warnLevel"
          clearable
          style="width: 240px"
        >
          <el-option :value="null" label="全部"/>
          <el-option label="P0" value="P0"/>
          <el-option label="P1" value="P1"/>
          <el-option label="P2" value="P2"/>
        </el-select>
      </el-form-item>
      <el-form-item label="涉及规则" prop="slowRule">
        <el-select
          v-model="queryParams.slowRule"
          clearable
          style="width: 240px"
        >
          <el-option :value="null" label="全部"/>
          <el-option
            v-for="dict in rules"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="指派人" prop="assigner">
        <el-input v-model="queryParams.assigner" clearable/>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" clearable>
          <el-option v-for="item in dict.type.code_quality_file_status"
                     :key="item.value"
                     :value="item.value"
                     :label="item.label"
                     style="width: 240px"/>
        </el-select>
      </el-form-item>
      <el-form-item label="处理人" prop="processer">
        <el-input v-model="queryParams.processer" clearable/>
      </el-form-item>
      <el-form-item label="处理时间">
        <el-date-picker
          v-model="processDateRange"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="指派时间">
        <el-date-picker
          v-model="assignTimeDateRange"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="createTimeDateRange"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="执行年份:" prop="roleName" label-width="100px">
        <el-input
          v-model="queryParams.year"
          placeholder="请输入年份"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="执行月份:" prop="roleKey" label-width="100px">
        <el-input
          v-model="queryParams.month"
          placeholder="请输入月份"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row class="mb10">
      <el-button size="small" type="primary" @click="assignQuestions" v-hasPermi="['system:slowQuery:assign']"
                 :disabled="selectedIds.length === 0">指派/更换指派
      </el-button>
      <el-button size="small" type="primary" @click="handleQuestions" v-hasPermi="['处理']"
                 :disabled="selectedIds.length === 0">处理
      </el-button>
      <el-button size="small" type="primary" @click="toStatPage" v-hasPermi="['system:slowQuery:statistic']">
        慢SQL综合统计
      </el-button>
    </el-row>
    <el-table v-loading="loading" :data="slowSqlList" @selection-change="handleSelectionChange" @sort-change="handleSortChange" style="width: 100%" height="calc(100vh - 370px)" stripe border>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="库名" align="center" key="dbRemark" prop="dbRemark"/>
      <el-table-column label="IP" align="center" key="ip" prop="ip"/>
      <el-table-column label="最新执行时间" align="center" key="lastExecutionStartTime" width="180"
                       prop="lastExecutionStartTime" :show-overflow-tooltip="true" sortable="custom"/>
      <el-table-column label="严重程度" align="center" key="warnLevel" prop="warnLevel" width="80"
                       :show-overflow-tooltip="true" sortable="custom"/>
      <el-table-column label="涉及规则" align="center" key="slowRule" prop="slowRule" :show-overflow-tooltip="true"
                       :formatter="slowRuleFormatter"/>
      <el-table-column prop="status" label="状态" align="center" sortable="custom">
        <template slot-scope="scope">
          {{ getDictLabel(scope.row.status, dict.type.code_quality_file_status) }}
        </template>
      </el-table-column>
      <el-table-column label="处理人" align="center" key="processer" prop="processer" width="100"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="处理时间" align="center" key="processTime" prop="processTime" width="180"
                       :show-overflow-tooltip="true" sortable="custom"/>
      <el-table-column label="处理说明" align="center" key="remark" prop="remark" width="150"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="指派人" align="center" key="assigner" prop="assigner" width="100"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="指派时间" align="center" key="assignTime" prop="assignTime" width="180"
                       :show-overflow-tooltip="true" sortable="custom"/>
      <el-table-column label="总次数" align="center" key="totalQueryTimes" prop="totalQueryTimes" width="110"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="总时间（s）" align="center" key="totalSumTime" prop="totalSumTime" width="110"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="平均时间（s）" align="center" key="avgQueryTime" prop="avgQueryTime" width="110"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="创建时间" align="center" key="createTime" prop="createTime" width="180"
                       :show-overflow-tooltip="true"/>
      <el-table-column
        label="操作"
        align="center"
        width="160"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template v-slot:default="scope">
          <el-button
            size="mini"
            type="text"
            @click="showSqlContent(scope.row)"
          >SQL文本
          </el-button>
          <el-button
            size="mini"
            type="text"
            @click="showSqlDetailList(scope.row)"
          >运行历史
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- SQL文本对话框 -->
    <el-dialog
      title="SQL 语句"
      :visible.sync="sqlDialogVisible"
      :show-close="true"
      :show-footer="false"
      width="1000px"
    >
      <pre class="sql-code"><code v-html="highlightedCode(sqlContent)"></code></pre>

      <div class="ai-analysis-section">
        <div class="ai-analysis-btn-container">
          <el-button
            type="primary"
            size="mini"
            @click="handleAiAnalysis"
            :loading="aiAnalysisLoading"
            :disabled="!currentSqlHash"
          >调用AI分析
          </el-button>
        </div>

        <div class="ai-result-section">
          <div class="ai-result-label">AI分析结果：</div>
          <el-input
            type="textarea"
            :rows="30"
            placeholder="AI分析结果将显示在这里"
            v-model="aiAnalysisResult"
          ></el-input>
          <div class="ai-btn-container">
            <el-button type="primary" @click="applyAiAnalysis">关闭</el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <!--sql详细对话框 -->
    <el-dialog
      :visible.sync="sqlDetailDialogVisible"
      width="70%"
      @open="getDetailList"
    >
      <!-- 表格 -->
      <el-table
        :data="detail.list"
        v-loading="loading"
        style="width: 100%"
        height="400"
      >
        <el-table-column key="id" prop="id" label="序号" align="center"></el-table-column>
        <el-table-column key="executionStartTime" prop="executionStartTime" label="执行时间"
                         align="center"></el-table-column>
        <el-table-column key="queryTimeMs" prop="queryTimeMs" label="执行时长（s）" align="center"></el-table-column>
        <el-table-column key="returnRowCounts" prop="returnRowCounts" label="返回行数" align="center"></el-table-column>
        <el-table-column key="parseRowCounts" prop="parseRowCounts" label="解析行数" align="center"></el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="detail.total>0"
        :total="detail.total"
        :page.sync="detail.queryParams.pageNum"
        :limit.sync="detail.queryParams.pageSize"
        @pagination="getDetailList"
      />
    </el-dialog>
    <!--问题指派-->
    <assignDialog :dialogVisible.sync="assignDialog.show" :assignData="assignDialog.assignData"
                  @callback="handleQuery"></assignDialog>
    <!--处理弹窗-->
    <processDialog :dialogVisible.sync="processDialog.show" :processData="processDialog.processData"
                   @callback="handleQuery"></processDialog>
  </div>
</template>

<script>
import {
  slowQuerySqlPage,
  slowQuerySqlDetailPage,
  systemTreeSelect,
  analyzeSqlWithAI,
  processSql,
  getAnalyzeResult
} from "@/api/system/slowQuery";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import hljs from "highlight.js/lib/highlight";
import "highlight.js/styles/github-gist.css";
import assignDialog from './components/assignDialog'
import processDialog from './components/processDialog'
import {getDicts} from "../../../api/system/dict/data";

hljs.registerLanguage("sql", require("highlight.js/lib/languages/java"));

export default {
  name: "User",
  dicts: ['sys_normal_disable', 'sys_user_sex', 'code_quality_file_status'],
  components: {Treeselect, assignDialog, processDialog},
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 详细
      detail: {
        // 总条数
        total: 0,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          id: null,
          sqlHash: null
        },
        list: null
      },
      // 显示搜索条件
      showSearch: true,
      // 慢sql表格数据
      slowSqlList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      systemOptions: null,
      // sql对话框是否显示弹出层
      sqlDialogVisible: false,
      // sql详细对话框是否显示弹出层
      sqlDetailDialogVisible: false,
      // sql文本
      sqlContent: null,
      // 当前SQL的Hash值
      currentSqlHash: null,
      rules: null,
      resultStatus: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dbName: null,
        ip: null,
        warnLevel: null,
        status: null,
        slowRule: null,
        assigner: null,
        processer: null,
        processTimeStart: null,
        processTimeEnd: null,
        assignTimeStart: null,
        assignTimeEnd: null,
        createTimeStart: null,
        createTimeEnd: null,
        year: null,
        month: null
      },
      // AI分析结果
      aiAnalysisResult: '',
      // AI分析loading状态
      aiAnalysisLoading: false,
      processDateRange: [],
      assignTimeDateRange: [],
      createTimeDateRange: [],
      assignDialog: { // 问题指派弹窗
        show: false,
        assignData: {
          idList: []
        }
      },
      processDialog: { // 问题处理弹窗
        show: false,
        processData: {
          idList: []
        }
      },
      selectedIds: [] // 选中的行id
    };
  },
  computed: {},
  created() {
    this.init()
    this.getSystemTree()
    this.getNowDate()
    this.getList()
  },
  methods: {
    /** 高亮显示 */
    highlightedCode(code) {
      const result = hljs.highlight("sql", code || "", true);
      return result.value || '&nbsp;';
    },
    /** 查询sql列表 */
    getList() {
      this.loading = true;
      if (this.processDateRange != null && this.processDateRange.length !== 0) {
        this.queryParams.processTimeStart = this.processDateRange[0];
        this.queryParams.processTimeEnd = this.processDateRange[1];
      }
      if (this.assignTimeDateRange != null && this.assignTimeDateRange.length !== 0) {
        this.queryParams.assignTimeStart = this.assignTimeDateRange[0];
        this.queryParams.assignTimeEnd = this.assignTimeDateRange[1];
      }
      if (this.createTimeDateRange != null && this.createTimeDateRange.length !== 0) {
        this.queryParams.createTimeStart = this.createTimeDateRange[0];
        this.queryParams.createTimeEnd = this.createTimeDateRange[1];
      }
      slowQuerySqlPage(this.queryParams).then(response => {
          this.slowSqlList = response.data.rows;
          this.total = response.data.total;
          this.loading = false;
        }
      );
    },
    /** 查询sql详细列表 */
    getDetailList() {
      this.loading = true;
      slowQuerySqlDetailPage(this.detail.queryParams).then(response => {
          this.detail.list = response.data.rows;
          this.detail.total = response.data.total;
          this.loading = false;
        }
      );
    },
    /** 查询部门下拉树结构 */
    getSystemTree() {
      this.loading = true
      systemTreeSelect().then(response => {
        this.systemOptions = response.data;
      }).catch(error => {
        console.error("获取系统库列表失败:", error);
        this.loading = false;
      });
    },
    /** 展示sql文本 */
    showSqlContent(row) {
      this.sqlContent = null;
      this.aiAnalysisResult = '';
      this.aiAnalysisLoading = false; // 重置AI分析loading状态
      this.sqlDialogVisible = true;
      this.sqlContent = row.sqlText;
      this.currentSqlHash = row.sqlHash;

      // 先显示本地已有的分析结果
      this.aiAnalysisResult = row.analysisResult || '';

      // 调用服务端接口获取最新的分析结果
      if (row.sqlHash) {
        this.loadAnalyzeResult(row.sqlHash);
      }
    },
    /** 加载SQL分析结果 */
    loadAnalyzeResult(sqlHash) {
      this.aiAnalysisLoading = true;
      getAnalyzeResult(sqlHash)
        .then(response => {
          if (response.code === 200) {
            // 只有当前显示的SQL Hash匹配时才更新结果
            if (sqlHash === this.currentSqlHash) {
              this.aiAnalysisResult = response.data || '';
            }
          } else {
            console.warn("获取分析结果失败：" + (response.msg || '未知错误'));
          }
        })
        .catch(error => {
          console.error('获取分析结果失败:', error);
        })
        .finally(() => {
          // 只有当前sqlHash匹配时才重置loading状态
          if (sqlHash === this.currentSqlHash) {
            this.aiAnalysisLoading = false;
          }
        });
    },
    /** 展示sql详细 */
    showSqlDetailList(row) {
      this.detail.queryParams.id = this.queryParams.id;
      this.detail.queryParams.sqlHash = row.sqlHash
      this.detail.queryParams.pageNum = 1
      this.sqlDetailDialogVisible = true;
    },
    /** 搜索按钮操作 */
    handleQuery() {

      if (this.queryParams.year && !/^\d{4}$/.test(this.queryParams.year)) {
        console.log(this.queryParams.year)
        alert("请输入正确的年份");
        return;
      }
      if (!this.queryParams.year && this.queryParams.month) {
        console.log(this.queryParams.year)
        alert("请输入年份");
        return;
      }
      if (this.queryParams.month && !(/(^[1-9]\d*$)/.test(this.queryParams.month))) {
        console.log(this.queryParams.year)
        console.log(this.queryParams.month)
        alert("请输入正确的月份");
        return
      }
      if (this.queryParams.month && !(this.queryParams.month < 13)) {
        console.log(this.queryParams.month)
        alert("请输入正确的月份");
        return
      }
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleAiAnalysis() {
      if (!this.currentSqlHash) {
        this.msgError("SQL Hash不能为空");
        return;
      }

      if (this.aiAnalysisLoading) {
        return; // 防止重复点击
      }

      this.aiAnalysisLoading = true;
      const sqlHashToAnalyze = this.currentSqlHash; // 保存当前要分析的sqlHash

      analyzeSqlWithAI(sqlHashToAnalyze)
        .then(response => {
          // 确保响应是针对当前显示的SQL
          if (sqlHashToAnalyze === this.currentSqlHash) {
            if (response.code === 200) {
              this.aiAnalysisResult = response.data;
            } else {
              this.msgError("AI分析失败：" + (response.msg || '未知错误'));
            }
          }
        })
        .catch(error => {
          // 同样确保错误处理只针对当前SQL
          if (sqlHashToAnalyze === this.currentSqlHash) {
            console.error('AI分析接口调用失败:', error);
            this.msgError("AI分析请求失败，请稍后重试");
          }
        })
        .finally(() => {
          // 只有当前sqlHash匹配时才重置loading状态
          if (sqlHashToAnalyze === this.currentSqlHash) {
            this.aiAnalysisLoading = false;
          }
        });
    },
    applyAiAnalysis() {
      this.sqlDialogVisible = false;
    },
    // 跳转到慢SQL综合统计页面
    toStatPage() {
      this.$router.push({path: '/manage/slowQueryStat'});
    },
    // 初始化
    async init() {
      await getDicts("slow_query_rule").then(response => {
        this.rules = response.data
      })
    },
    getNowDate() {
      const timeOne = new Date()
      const year = timeOne.getFullYear()
      let month = timeOne.getMonth() + 1
      this.queryParams.year = year
      this.queryParams.month = month
    },
    // 重置参数
    resetParams() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        dbName: null,
        ip: null,
        warnLevel: null,
        status: null,
        slowRule: null,
        assigner: null,
        processer: null,
        processTimeStart: null,
        processTimeEnd: null,
        assignTimeStart: null,
        assignTimeEnd: null,
        createTimeStart: null,
        createTimeEnd: null
      }
      this.processDateRange = []
      this.assignTimeDateRange = []
      this.createTimeDateRange = []
    },
    // 重置查询
    resetQuery() {
      this.resetParams()
      this.getList();
    },
    slowRuleFormatter(row, column, cellValue) {
      for (let dict of this.rules) {
        if (cellValue === dict.dictValue) {
          return dict.dictLabel;
        }
      }
      return '';
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectedIds = selection.map(item => item.id)
    },
    // 指派/更换指派
    assignQuestions() {
      let selectedRows = this.slowSqlList.filter(item => this.selectedIds.indexOf(item.id) >= 0)
      this.assignDialog.assignData = {
        idList: this.selectedIds,
        dbNameList: selectedRows.map(item => item.dbName)
      }
      this.assignDialog.show = true
    },
    // 处理
    handleQuestions() {
      let selectedRows = this.slowSqlList.filter(item => this.selectedIds.indexOf(item.id) >= 0)
      let firstProcesserId = null
      for (const row of selectedRows) {
        if (!row.processerId) {
          this.$message.warning('处理人不能为空')
          return
        }
        if (firstProcesserId === null) {
          firstProcesserId = row.processerId
        } else if (firstProcesserId !== row.processerId) {
          this.$message.warning('请选择同一处理人')
          return
        }
      }
      this.processDialog.processData = {
        idList: this.selectedIds
      }
      this.processDialog.show = true
    },
    // 根据value获取字典label
    getDictLabel(value, dictData) {
      const item = dictData.find(item => item.value === value)
      return item ? item.label : value
    },
    // 排序
    handleSortChange(column, prop, order) {
      this.queryParams.orderByColumn = column.prop
      this.queryParams.isAsc = column.order
      this.handleQuery()
    }
  }
};
</script>
<style scoped lang="scss">
/* SQL 代码块样式 */
.sql-code {
  max-height: 60vh;
  overflow: auto;
  background: #f6f8fa;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  margin-bottom: 10px;
}

.ai-analysis-section {
  border-top: 1px solid #ebeef5;
  padding-top: 10px;
}

.ai-analysis-btn-container {
  margin-bottom: 10px;
  display: flex;
  justify-content: flex-start;
}

.ai-result-section {
  margin-top: 10px;
}

.ai-result-label {
  font-size: 14px;
  margin-bottom: 5px;
}

.ai-btn-container {
  margin-top: 10px;
  text-align: right;
}

.el-dialog .el-input.el-textarea textarea {
  border: 1px solid #dcdfe6;
  background-color: #fff;
  resize: none;
  width: 100%;
  padding: 5px 10px;
  box-sizing: border-box;
  height: auto;
}
</style>

