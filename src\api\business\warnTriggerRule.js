import request from '@/utils/request'

// 查询预警配置触发规则列表
export function listWarnTriggerRule(query) {
  return request({
    url: '/business/warnTriggerRule/list',
    method: 'get',
    params: query
  })
}

// 查询预警配置触发规则详细
export function getWarnTriggerRule(id) {
  return request({
    url: '/business/warnTriggerRule/' + id,
    method: 'get'
  })
}

// 新增预警配置触发规则
export function addWarnTriggerRule(data) {
  return request({
    url: '/business/warnTriggerRule',
    method: 'post',
    data: data
  })
}

// 修改预警配置触发规则
export function updateWarnTriggerRule(data) {
  return request({
    url: '/business/warnTriggerRule',
    method: 'put',
    data: data
  })
}

// 删除预警配置触发规则
export function delWarnTriggerRule(id) {
  return request({
    url: '/business/warnTriggerRule/' + id,
    method: 'delete'
  })
}
