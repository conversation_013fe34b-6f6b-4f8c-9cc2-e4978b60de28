<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="年份" prop="year">
        <el-select v-model="queryParams.year" placeholder="请选择年份" value-key="value">
          <el-option
            v-for="item in yearList"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="开发组" prop="deptId">
        <el-select v-model="queryParams.deptId" placeholder="请选择开发组">
          <el-option
            v-for="item in deptList"
            :key="item.deptId"
            :label="item.deptName"
            :value="item.deptId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择" prop="isNotInclude">
        <!-- `checked` 为 true 或 false -->
        <el-checkbox v-model="queryParams.isNotInclude">不含技术经理</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery(false)">组人员全年度代码统计</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery(true)">部门全年度代码统计</el-button>
      </el-form-item>
    </el-form>
    <!--为echarts准备一个具备大小的容器dom-->
    <div id="chart" style="width: 1650px;height: 600px;"></div>
  </div>

</template>

<script>
import {statistics} from "@/api/gitcode";
import {deptSelect} from "@/api/commonBiz";
import * as echarts from 'echarts/core';
import {GridComponent, LegendComponent, TitleComponent, ToolboxComponent, TooltipComponent} from 'echarts/components';
import {LineChart} from 'echarts/charts';
import {UniversalTransition} from 'echarts/features';
import {CanvasRenderer} from 'echarts/renderers';

echarts.use([
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  LineChart,
  CanvasRenderer,
  UniversalTransition
]);

var codeChart;

export default {
  name: "Yearline",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 查询参数
      queryParams: {
        year: null,
        deptId: null,
        isNotInclude: false,
        filter: "开发",
      },
      yearList: [],
      deptList: [],
    };
  },
  created() {
    this.getYearList();
    this.getDeptList();
  },
  methods: {
    /** 查询年份列表 */
    getYearList() {
      this.loading = true;
      var date = new Date();
      var year = date.getFullYear();
      //起始年份
      var startYear = year - 3;
      //结束年份
      var endYear = year;
      var years = [];
      for (var i = startYear; i <= endYear; i++) {
        var obj = {
          label: i,
          value: i,
        }
        years.push(obj);
      }
      this.yearList = years;
      this.queryParams.year = year;
      this.loading = false;
    },
    /** 查询部门列表 */
    getDeptList() {
      this.loading = true;
      var deptList = [];
      deptSelect(this.queryParams).then(response => {
        deptList = response.data;
        this.deptList = deptList;
        this.queryParams.deptId = deptList[0].deptId;
        this.getStatisticsLine()
        this.loading = false;
      });
    },
    /** 查询年度数据 */
    getStatisticsLine() {
      this.loading = true;
      statistics(this.queryParams).then(response => {
          this.loadChart(response.data);
          this.loading = false;
        }
      );
    },
    /** 搜索按钮操作 */
    handleQuery(isDept) {
      if (isDept) {
        this.queryParams.deptId = 101;
      }
      this.getStatisticsLine();
    },

    /** 加载折线图 */
    loadChart(data) {
      var chartDom = document.getElementById('chart');
      // 如果初始化过 echarts 实例，销毁。
      if (codeChart != null && codeChart != "" && codeChart != undefined) {
        codeChart.dispose();
      }
      // 基于准备好的 dom ，重新初始化 echarts 实例
      codeChart = echarts.init(chartDom);
      var option;
      var text = this.queryParams.year + '年度代码贡献度折线统计';
      var series = [];
      var legendData = [];
      data.forEach((element) => {
        var obj = {
          name: element.name,
        }
        legendData.push(obj);
      })
      data.forEach(element => {
        var obj = {
          label: {
            show: true,
            fontWeight: 'bold'
          },
          name: element.name,
          type: 'line',
          data: element.data
        }
        series.push(obj)
      })
      option = {
        title: {
          left: 'center',
          text: text
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          top: 'bottom',
          data: legendData
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          axisTick: {
            alignWithLabel: true
          },
          axisLine: {
            onZero: false,
          },
          axisPointer: {
            label: {
              show: true,
            }
          },
          data: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
        },
        yAxis: {
          name: '代码行数',
          type: 'value'
        },
        series: series
      };

      option && codeChart.setOption(option, true);
    }
  }
};
</script>
