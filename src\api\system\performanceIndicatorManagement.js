import request from '@/utils/request'


// 查询绩效指标原因列表
export function getPerformIndicatorResultList(query) {
  return request({
    url: '/performIndicatorResult/list',
    method: 'get',
    params: query
  })
}
// 新增绩效指标原因
export function addPerformIndicatorResult(data) {
  return request({
    url: '/performIndicatorResult/add',
    method: 'post',
    data: data
  })
}
// 修改绩效指标原因
export function editPerformIndicatorResult(data) {
  return request({
    url: '/performIndicatorResult/edit',
    method: 'post',
    data: data
  })
}
// 删除绩效指标原因
export function delPerformIndicatorResult(resultsIds) {
  return request({
    url: '/performIndicatorResult/remove/' + resultsIds,
    method: 'delete'
  })
}
