<template>
  <div class="app-container">
    <el-row class="tac">
      <el-col :span="4" >
        <div class="menu-col">
        <el-menu
          default-active="2"
          class="el-menu-vertical-demo">
          <el-submenu :index="String(dept.deptId)" v-for="dept in deptList" :key="dept.deptId">
            <template slot="title">
              <i class="el-icon-s-home"></i>
              <span>{{dept.deptName}}</span>
            </template>
            <el-menu-item :index="String(child.deptId)" @click="changeDept(child.deptId)" v-for="child in dept.children" :key="child.deptId">
              <span slot="title">{{child.deptName + '（' + child.leaveCount + '）'}}</span>
            </el-menu-item>
          </el-submenu>
        </el-menu>
        </div>
      </el-col>
      <el-col :span="20">
        <div class="show-chart" v-if="!noLeave">
          <div class="controls" v-if="areaChart">
            <el-radio-group v-model="tabPosition" style="margin-bottom: 30px;"  @change="changeMonth">
              <el-radio-button label="current" >本月</el-radio-button>
              <el-radio-button label="last" >上月</el-radio-button>
            </el-radio-group>
          </div>
          <div id="chart" style="width: 100%; height: 500px;" v-loading="loading"></div>
        </div>
        <div v-else class="no-leave">
          <h2>近两月无人请假</h2>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { statisticUserLeave, userLeaveCountByDept } from "@/api/system/userLeave";
import {getListDept} from "@/api/system/dept";
var currentDate;
var currentMonthOfFirstDay;
var lastMonthOfFirstDay;
export default {
  name: "UserLeave",
  dicts: ['biz_yes_no'],
  data() {
    return {
      //遮罩层
      loading: false,
      deptList: [],
      queryParams: {
        deptId: null,
        monthOfFirstDay: null,
      },
      leaveCountStat:{},
      areaChart: null,
      tabPosition: 'current',
      noLeave: false, // 新增属性
      chartDate: {
        currentSeriesData: [], //本月请假人数
        lastSeriesData: [], //上月请假人数
        text: '', //标题
        seriesData: [],//纵坐标
        xAxisData:[],//横坐标
      },
    };
  },
  mounted() {
    this.getDeptList();
  },
  methods: {
    //获取部门列表
    getDeptList(){
      getListDept(this.queryParams).then(response => {
        // 过滤出 parentId 为 0 的部门
        const deptList = response.data.filter(item => item.parentId != 0);

        userLeaveCountByDept().then(res => {
          if (res.code === 200) {
            deptList.forEach(item => {
              const leaveCounts = res.data[item.deptId];
              item.leaveCount = leaveCounts != undefined ? leaveCounts : 0;
            });
            // 根据 leaveCount 对 deptList 进行排序
            deptList.sort((a, b) => b.leaveCount - a.leaveCount);
            this.deptList = this.handleTree(deptList, "deptId");
          }
        })
      });
    },
    changeMonth(){
      this.getChartDate();
    },
    async changeDept(deptId){
      this.queryParams.deptId = deptId;
      await this.statisticUserLeave();
      this.getChartDate();
    },

    //获取本月和上个月的请假人数
    async statisticUserLeave() {
      this.noLeave = false;
      this.loading = true;

      // 获取当前日期
      currentDate = new Date();
      // 获取本月第一天
      currentMonthOfFirstDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 2);
      // 获取上个月第一天
      lastMonthOfFirstDay = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 2);

      // 获取本月各天请假人数
      this.queryParams.monthOfFirstDay = currentMonthOfFirstDay.toISOString().split('T')[0];
      await statisticUserLeave(this.queryParams).then(response => {
        if (response.code === 200) {
          this.chartDate.currentSeriesData = response.data.leaveCountList;
          this.chartDate.text = response.data.deptName + "请假人数统计图";
        }
      });
      // 获取上月各天请假人数
      this.queryParams.monthOfFirstDay = lastMonthOfFirstDay.toISOString().split('T')[0];
      await statisticUserLeave(this.queryParams).then(response => {
        if (response.code === 200) {
          this.chartDate.lastSeriesData = response.data.leaveCountList;
          this.chartDate.text = response.data.deptName + "请假人数统计图";
        }
      });
      if ((this.chartDate.currentSeriesData.length === 0 || this.chartDate.currentSeriesData.every(count => count === 0)) &&
        (this.chartDate.lastSeriesData.length === 0 || this.chartDate.lastSeriesData.every(count => count === 0))) {
        this.noLeave = true;
      }
      this.loading = false;
    },
    //封装横纵坐标参数
    getChartDate(){
      let daysInMonth;
      if (this.tabPosition === 'current') {
        // 获取本月的天数
        daysInMonth = new Date(currentMonthOfFirstDay.getFullYear(), currentMonthOfFirstDay.getMonth() + 1, 0).getDate();
        this.chartDate.seriesData = this.chartDate.currentSeriesData;
      } else {
        // 获取上月的天数
        daysInMonth = new Date(lastMonthOfFirstDay.getFullYear(), lastMonthOfFirstDay.getMonth() + 1, 0).getDate();
        this.chartDate.seriesData = this.chartDate.lastSeriesData;
      }
      this.chartDate.xAxisData = [];
      // 封装横坐标数组
      for (let i = 1; i <= daysInMonth; i++) {
        this.chartDate.xAxisData.push(i + '号');
      }
      console.log(this.chartDate.xAxisData);
      if (!this.noLeave) {
        this.loadChart(this.chartDate.xAxisData, this.chartDate.seriesData, this.chartDate.text);
      } else {
        this.areaChart = null; // 清除图表
      }
    },
    // 加载图表
    loadChart(xAxisData, seriesData, text){
      var chartDom = document.getElementById('chart');
      this.areaChart = echarts.init(chartDom);
      var option = {
        title: {
          left: 'center',
          text: text
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line'
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxisData
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: seriesData,
            type: 'line',
            areaStyle: {}
          }
        ]
      };
      this.areaChart.setOption(option);
    },

  }
};
</script>
<style lang="scss" scoped>
.app-container {
  height: calc(100vh - 85px);
  overflow: hidden; /* 关闭总页面的滚动条 */
}
.tac {
  height: 100%;
  display: flex;
}
.el-col {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.menu-col{
  height: 100%;
  border-right: 1px solid #e2e2e2;
  overflow-y: scroll;
}
.show-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}
.controls {
  margin-bottom: 10px;
  margin-left: 100px;
  width: 100%;
}
.no-leave{
  height: 100%;
  display: flex;
  flex-deirection: column;
  align-items: center;
  justify-content: center;
}
//标签按钮样式
::v-deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
  background-color: #343434 !important;
  box-shadow: none;
  border-color:  #343434;
}
::v-deep .el-radio-button__inner:hover {
  color: #a8a6a6;
}
</style>
