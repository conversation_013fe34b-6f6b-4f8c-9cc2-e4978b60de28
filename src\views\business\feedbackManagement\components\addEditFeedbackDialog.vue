<!-- 新增编辑反馈弹窗 -->
<template>
  <div class="addEditFeedbackDialog">
    <el-dialog :title="dialogTitle" :visible.sync="visible" width="950px" @close="cancel">
      <el-form ref="form" :model="formData" :rules="rules" label-width="110px" inline>
        <el-row>
          <el-col :span="13"><br/>
            <el-form-item label="事件标题" prop="eventTitle">
              <el-input v-model="formData.eventTitle" placeholder="事件标题" clearable style="width: 360px" />
            </el-form-item><br/><br/>
            <el-form-item label="事件发生时间" prop="eventTimeRange">
              <el-date-picker v-model="formData.eventTimeRange" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"  style="width: 360px"
                :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item><br/><br/>
            <el-form-item label="审核项目经理" prop="projectManagerAuditor">
              <el-select v-model="formData.projectManagerAuditor" clearable filterable style="width: 360px">
                <el-option v-for="item in dict.type.project_outcome_project_manager" :key="item.value" :value="item.value" :label="item.label"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item prop="eventDetail" class="introduction-item" label-width="100%">
              <template slot="label">
                <span>事件明细</span>
                <el-popover placement="bottom" width="800" trigger="click">
                  <div>
                    <div v-for="item in templateList" :key="item.name" style="white-space: break-spaces;">
                      <span style="font-weight: bold;">{{ item.name }}：</span><br/>{{ item.content }}
                    </div>
                  </div>
                  <el-button type="text" slot="reference" style="margin-left: 20px;">查看示例模板</el-button>
                </el-popover>
              </template>
              <el-input v-model="formData.eventDetail" placeholder="事件明细" autocomplete="off" type="textarea" :rows="8" :maxlength="1000" show-word-limit></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="推荐明细" prop="feedbackList" class="recommend-detail">
          <el-card v-for="(feedbackItem, index) in formData.feedbackList" :key="index">
            <el-button type="danger" class="remove-person" icon="el-icon-delete" size="mini" @click="removeRecommendPerson(index)">删除</el-button>
            <el-form-item :label="`#${index + 1} 人员`" :prop="`feedbackList[${index}].nickName`" :rules="{ required: true, message: '请选择人员', trigger: 'change' }" class="short-item">
              <el-select v-model="formData.feedbackList[index].nickName" clearable filterable style="width: 240px">
                <el-option v-for="item in userOptions" :key="item.userId" :value="item.nickName" :label="item.nickName"/>
              </el-select>
            </el-form-item>
            <el-form-item label="一类指标" :prop="`feedbackList[${index}].primaryIndicator`" :rules="{ required: true, message: '请选择一类指标', trigger: 'change' }" class="short-item">
              <el-select v-model="formData.feedbackList[index].primaryIndicator" clearable filterable style="width: 240px" @change="changePrimaryIndicator(formData.feedbackList[index])">
                <el-option v-for="item in primaryIndicatorOptions" :key="item.code" :value="item.code" :label="item.name"/>
              </el-select>
            </el-form-item><br/>
            <el-form-item label="二类指标" :prop="`feedbackList[${index}].secondaryIndicator`" :rules="{ required: true, message: '请选择二类指标', trigger: 'change' }" class="short-item mt18">
              <el-select v-model="formData.feedbackList[index].secondaryIndicator" clearable filterable style="width: 240px" :disabled="!formData.feedbackList[index].primaryIndicator" @change="changeSecondaryIndicator(formData.feedbackList[index])">
                <template v-for="item2 in secondaryIndicatorOptions">
                  <el-option v-if="item2.primaryIndicator === formData.feedbackList[index].primaryIndicator && !item2.systemGenerated" :key="item2.code" :value="item2.code" :label="item2.name"/>
                </template>
              </el-select>
            </el-form-item>
            <el-form-item label="推荐绩效" :prop="`feedbackList[${index}].recommendedLevel`" :rules="{ required: true, message: '请选择推荐绩效', trigger: 'change' }" class="short-item mt18">
              <el-select v-model="formData.feedbackList[index].recommendedLevel" clearable filterable style="width: 240px" :disabled="!formData.feedbackList[index].secondaryIndicator">
                <el-option v-for="item in dict.type.performance_level" :key="item.value" :value="item.value" :label="item.label" v-if="item.value !== 'B'"/>
              </el-select>
            </el-form-item><br/>
            <el-form-item label="推荐原因" class="mt18" v-if="formData.feedbackList[index].recommendedLevel">
              <template v-for="item in indicatorResultIdsOptions">
                <p :key="item.id" class="indicatorResultP"
                  v-if="item.firstIndecator === formData.feedbackList[index].primaryIndicator && // 一类指标
                        item.secondIndecator === formData.feedbackList[index].secondaryIndicator && // 二类指标
                        item.level === formData.feedbackList[index].recommendedLevel">
                  {{ item.result }}
                </p>
              </template>
            </el-form-item>
            <el-form-item label="推荐理由（请描述具体工作表现）" label-width="240px" class="subjective-reason" :prop="`feedbackList[${index}].recommendedReason`" :rules="{ required: true, message: '请输入推荐理由', trigger: 'blur' }">
              <el-input v-model="formData.feedbackList[index].recommendedReason" :placeholder="recommendedReasonPlaceholder" autocomplete="off" type="textarea" :rows="5" :maxlength="500" show-word-limit></el-input>
            </el-form-item>
          </el-card>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button icon="el-icon-plus" type="success" @click="addRecommendPerson">添加推荐</el-button>
        <el-button type="primary" @click="confirm" :loading="btnLoading">保存</el-button>
        <el-button :loading="saveAndSubmitLoading" type="primary" @click="saveAndSubmit">保存并提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </span>
      </el-dialog>
  </div>
</template>
<script>
import { performanceEventAdd, performanceEventEdit, indicatorList, performIndicatorResultList, performanceEventSaveAndSubmit } from "@/api/business/performanceFeedback"
import { listUserAll } from "@/api/system/user"
export default {
  dicts: [
    'project_outcome_project_manager',
    'performance_level'
  ],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    dialogTitle: {
      type: String
    },
    feedbackData: {
      type: Object
    }
  },
  data() {
    return {
      visible: this.dialogVisible,
      btnLoading: false,
      saveAndSubmitLoading: false, // 新增：保存并提交按钮的加载状态
      processerOptions: [], // 处理人下拉数据
      formData: {
        id: '',
        eventTitle: '',
        eventTimeRange: [],
        projectManagerAuditor: '',
        eventDetail: '',
        feedbackList: []
      },
      rules: {
        eventTitle: [ { required: true, message: '请输入事件标题', trigger: 'blur' } ],
        eventTimeRange: [ { required: true, message: '请选择事件发生时间', trigger: 'change' } ],
        projectManagerAuditor: [ { required: true, message: '请选择审核项目经理', trigger: 'change' } ],
        eventDetail: [ { required: true, message: '请输入事件明细', trigger: 'change' } ],
        feedbackList: [ { required: true, message: '请添加推荐人员', trigger: 'change' } ]
      },
      // 一类指标下拉数据
      primaryIndicatorOptions: [],
      // 二类指标下拉数据
      secondaryIndicatorOptions: [],
      // 全部用户下拉数据
      userOptions: [],
      // 推荐原因下拉数据
      indicatorResultIdsOptions: [],
      templateList: [
        { name: '事件明细（S级模板）', content: '格式：时间地点+人员+项目+表现+成果\n2025 年 6 月 20 日，在12楼技术部会议室，项目组就系统升级项目召开评审会。会上，团队成员对技术实现方式产生激烈冲突，一方坚持采用旧框架降低风险，另一方力推新框架追求性能突破，僵持不下。小王全程准时参会，面对争论始终保持理性克制，未与任何人发生冲突。他深入分析项目需求与团队技术能力，创新性地提出混合使用新旧框架的方案，既提升性能又规避风险；针对系统兼容性难题，小王查阅大量资料，反复测试。最终，他成功找到适配方案，保障项目顺利推进，获得各部门认可。'},
        { name: '事件明细（A级模板）', content: '格式：时间地点+人员+项目+表现+成果\n2025 年 7 月，小王承担重点项目-智能客服系统的开发与上线工作。面对多渠道接入、智能应答逻辑复杂等难题，他制定精细化开发计划，通过优化代码结构与并行开发，提前 4 天完成智能路由分配、语义理解核心模块。系统上线后，全程无回滚，稳定运行一周无生产故障。'},
        { name: '事件明细（C级模板）', content: '格式：时间地点+人员+项目+表现+成果\n2025 年 7 月，小王参与客服系统优化项目。项目初期，领导安排他负责客服机器人知识库搭建，他以任务繁琐为由拒绝，导致该模块进度滞后。中期团队需要他协助测试新上线的智能转接功能，小王再次推脱，称自己不熟悉测试流程，使得功能存在的漏洞未及时发现，幸测试团队发现问题，未造成生产问题。'},
        { name: '事件明细（D级模板）', content: '格式：时间地点+人员+项目+表现+成果\n2025 年 7 月，小王参与新客服系统迭代项目。工作时间内，他多次长时间使用办公电脑玩网络游戏，被同事和领导提醒后仍不改正。在一次项目讨论会上，因意见不合，小王与同事发生激烈争吵，甚至推搡对方，造成办公秩序混乱。这些行为不仅导致他负责的模块开发进度严重滞后，影响项目整体交付，还在团队内引发不良风气，损害公司内部和谐氛围。'},
      ],
      recommendedReasonPlaceholder: `推荐理由（S级模板）：小王严守会议纪律，沟通理性，提出创新方案并攻克技术难点，完全符合 S 绩效标准，值得推荐。\n推荐理由（A级模板）：小王按时超量完成开发任务，系统稳定可靠，完全符合 A 绩效标准，值得推荐。\n推荐理由（C级模板）：小王在项目中两次不服从工作任务安排，导致项目出现严重问题，符合 C 绩效评定标准，应评定为 C 绩效。\n推荐理由（D级模板）：小王在上班期间做无关事项，且与同事发生肢体冲突，严重违反工作制度，后果严重，符合 D 绩效评定标准，应评定为 D 绩效。`
    }
  },
  watch: {
    dialogVisible (val) {
      this.visible = val
      if (this.visible) {
        if (this.feedbackData.id) {
          let feedbackList = this.feedbackData.performanceFeedbackList || []
          this.formData = Object.assign(this.formData, {
            id: this.feedbackData.id, // 编辑需要
            feedbackCode: this.feedbackData.feedbackCode, // 编辑需要
            dataSource: this.feedbackData.dataSource, // 编辑需要
            month: this.feedbackData.month, // 编辑需要
            year: this.feedbackData.year, // 编辑需要
            primaryIndicator: this.feedbackData.primaryIndicator,
            secondaryIndicator: this.feedbackData.secondaryIndicator,
            eventTitle: this.feedbackData.eventTitle,
            eventTimeRange: [this.feedbackData.eventStartTime, this.feedbackData.eventEndTime],
            projectManagerAuditor: this.feedbackData.projectManagerAuditor,
            eventDetail: this.feedbackData.eventDetail,
            feedbackList: feedbackList.map(item => {
              return {
                id: item.id,
                nickName: item.nickName,
                recommendedLevel: item.recommendedLevel,
                recommendedReason: item.recommendedReason,
                primaryIndicator: item.primaryIndicator,
                secondaryIndicator: item.secondaryIndicator,
                mainFeedbackId: item.mainFeedbackId, // 编辑需要
                feedbackCode: item.feedbackCode // 编辑需要
              }
            })
          })
        }
        this.indicatorList()
        this.getIndicatorResultIdsOptions()
        this.listUserAll()
      }
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    /** 取消 */
    cancel () {
      this.formData = Object.assign(this.formData, {
        id: '',
        eventTitle: '',
        eventTimeRange: [],
        projectManagerAuditor: '',
        eventDetail: '',
        feedbackList: []
      })
      this.$nextTick(() => {
        this.$refs.form.clearValidate()
      })
      this.$emit('update:dialogVisible', false)
    },
    /** 保存 */
    confirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let apiName = performanceEventAdd
          if (this.formData.id) {
            apiName = performanceEventEdit
          }
          let params = {...this.formData}
          params.eventStartTime = params.eventTimeRange[0]
          params.eventEndTime = params.eventTimeRange[1]
          delete params.eventTimeRange
          this.btnLoading = true
          apiName(params).then(res => {
            this.btnLoading = false
            if (res.code === 200) {
              this.$message.success(res.msg)
              this.cancel()
              this.$emit('callback')
            } else {
              this.$message.error(res.msg)
            }
          }).catch(err => {
            this.btnLoading = false
          })
        }
      })
    },
    /** 新增反馈人员 */
    addRecommendPerson () {
      this.formData.feedbackList.push({
        nickName: '',
        primaryIndicator: '',
        secondaryIndicator: '',
        recommendedLevel: '',
        recommendedReason: ''
      })
      this.$refs.form.validateField('feedbackList')
    },
    /** 删除反馈人员 */
    removeRecommendPerson (index) {
      this.formData.feedbackList.splice(index, 1)
    },
         /** 保存并提交 */
     async saveAndSubmit () {
       this.$refs.form.validate(async valid => {
         if (valid) {
           let params = {...this.formData}
           params.eventStartTime = params.eventTimeRange[0]
           params.eventEndTime = params.eventTimeRange[1]
           delete params.eventTimeRange
           this.saveAndSubmitLoading = true

           try {
             // 调用保存并提交API
             const res = await performanceEventSaveAndSubmit(params)
             if (res.code === 200) {
               this.$message.success('保存并提交成功')
               this.cancel()
               this.$emit('callback')
             } else {
               this.$message.error(res.msg || '保存并提交失败')
             }
           } catch (err) {
             console.error('保存并提交失败:', err)
             this.$message.error('操作失败，请重试')
           } finally {
             this.saveAndSubmitLoading = false
           }
         }
       })
     },
    /** 一类指标、二类指标下拉数据 */
    indicatorList () {
      indicatorList().then(res => {
        if (res.code === 200) {
          // 只保留 secondaryIndicators 至少有一个 systemGenerated 为 false 的一类指标
          this.primaryIndicatorOptions = res.data.filter(item => Array.isArray(item.secondaryIndicators) && item.secondaryIndicators.some(second => !second.systemGenerated))
          let secondaryIndicatorOptions = []
          this.primaryIndicatorOptions.forEach(item => {
            secondaryIndicatorOptions.push(...item.secondaryIndicators.map(second => {
              return {
                code: second.code,
                name: second.name,
                primaryIndicator: item.code,
                systemGenerated: second.systemGenerated
              }
            }))
          })
          this.secondaryIndicatorOptions = secondaryIndicatorOptions
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    /** 一类指标变更 */
    changePrimaryIndicator (row) {
      this.$set(row, 'secondaryIndicator', '')
      this.$set(row, 'recommendedLevel', '')
    },
    /** 二类指标变更 */
    changeSecondaryIndicator (row) {
      this.$set(row, 'recommendedLevel', '')
    },
    /** 获取推荐理由下拉数据 */
    getIndicatorResultIdsOptions () {
      performIndicatorResultList({
        pageSize: 10000,
        pageNum: 1
      }).then(res => {
        if (res.code === 200) {
          this.indicatorResultIdsOptions = res.rows
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    /** 获取用户列表 */
    listUserAll () {
      listUserAll().then(res => {
        this.userOptions = res
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.introduction-item {
  ::v-deep .el-form-item__label {
    text-align: left;
    margin-left: 20px;
  }
  .el-textarea {
    width: 398px;
    margin-left: 20px;
  }
}
.recommend-detail {
  position: relative;
  width: 100%;
  ::v-deep .el-form-item__content {
    width: 100%;
    .el-card {
      position: relative;
      margin-top: 12px;
      .remove-person {
        position: absolute;
        right: 18px;
        top: 15px;
        cursor: pointer;
      }
      .el-form-item__content {
        width: fit-content;
        margin-right: 20px;
      }
      .el-form-item__label {
        text-align: left;
      }
      .short-item .el-form-item__label {
        width: 80px!important;
      }
      .mt18 {
        margin-top: 18px;
      }
      .subjective-reason {
        margin-top: 10px;
        .el-textarea {
          width: 870px;
        }
      }
    }
  }
  .indicatorResultP {
    margin: 0;
    line-height: 22px;
    margin-bottom: 6px;
    max-width: 700px;
    &:first-child {
      margin-top: 8px;
    }
    &:last-child {
      margin-bottom: -10px;
    }
  }
}
::v-deep .el-dialog__footer {
  text-align: center;
}
.addEditFeedbackDialog ::v-deep .el-input__count {
  bottom: -16px;
  line-height: 12px;
}
</style>
