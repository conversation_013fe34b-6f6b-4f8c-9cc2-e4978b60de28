import request from '@/utils/request'

// 查询项目管理列表
export function listProject(query) {
  return request({
    url: '/proejct/query/page',
    method: 'get',
    params: query
  })
}


// 查询项目分支列表
export function listBranch(query) {
  return request({
    url: '/branch/query/page',
    method: 'get',
    params: query
  })
}

// 编辑代码库信息
export function editProject(data) {
  return request({
    url: '/proejct/query/edit',
    method: 'post',
    data: data
  })
}

// 代码质量管理-列表
export function scanProjectList(data) {
  return request({
    url: '/system/scanProject/list',
    method: 'get',
    params: data
  })
}

// 代码质量管理-扫描报告数据（文件分组数据）
export function pageGroupingFile(data) {
  return request({
    url: '/system/scanProjectFile/pageGroupingFile',
    method: 'get',
    params: data
  })
}
// 代码质量管理-指派代码质量问题
export function scanProjectFileAssign(data) {
  return request({
    url: '/system/scanProjectFile/assign',
    method: 'post',
    data: data
  })
}
// 代码质量管理-处理代码质量问题
export function scanProjectFileProcess(data) {
  return request({
    url: '/system/scanProjectFile/process',
    method: 'post',
    data: data
  })
}
// 代码质量管理-扫描报告数据(文件数据)
export function scanProjectDetailList(data) {
  return request({
    url: '/system/scanProjectDetail/list',
    method: 'get',
    params: data
  })
}
