<template>
    <div class="container">
        <div style="padding-top: 20px; padding-left: 10px">
            <el-form :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item label="">
                <span>提交时间&nbsp;&nbsp;&nbsp;</span>
                <el-date-picker
                            v-model="queryForm.beginDate"
                            type="date"
                            @input="changeTime"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            >
                        </el-date-picker>
                        <span>&nbsp;&nbsp;&nbsp;&nbsp;至&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>  
            </el-form-item>
            <el-form-item label="">
                <el-date-picker
                            v-model="queryForm.endDate"
                            type="date"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            @input="changeTime">
                        </el-date-picker> 
            </el-form-item>
            <el-form-item >
                    <el-checkbox v-model="queryForm.managered">不含技术经理</el-checkbox>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" v-on:click="notify">查询</el-button>
            </el-form-item>
            </el-form>      
        <el-container>
        <el-header style="weight: 100%;height:500px;">
            <statis-header ref="header" :option="codeTeamData" />
        </el-header>
        <el-main style="height:450px;" >
            <statis-middle ref="middle" :leftData="leftTableData" :rightData ="rightTableData" :queryForm="queryForm"/>
        </el-main>
        <el-footer style="height:500px;">
            <statis-footer ref="footer" :option="codeFrontData" />
        </el-footer>
        </el-container>
    </div>
</div>
  </template>
  
  <script>
  
  import StatisHeader from './components/StatisHeader'
  import StatisFooter from './components/StatisFooter'
  import StatisMiddle from './components/StatisMiddle'
  import {listHistogram,listLeftInfoPage,listRightInfoPage} from '@/api/periodStatis'
  export default {
    name: 'StatisticsIndex',
    props: {},
    components: {
        StatisHeader,
        StatisFooter,
        StatisMiddle
    },
    data () {
      return {
            // 团队数据
            codeTeamData: [],
            // 贡献人数
            codeFrontData: [],
            // 左边项目表格列表
            leftTableData: {},
            // 右边人数表格列表
            rightTableData: {},
            queryForm: {
            beginDate: '',
            endDate: '',
            managered: false ,
            groupBy: '',
            pageSize:20,
            pageNo:1
            },
            formInline: {
          user: '',
          region: ''
        }    
      }
    },
    watch: {},
    computed: {},
    methods: {

    async getTableLeftList(){
        this.queryForm.groupBy = 'project'
        console.log('queryForm = ' ,this.queryForm)
        listLeftInfoPage(this.queryForm).then(response => {
        console.log('LeftList = ',response)
        this.leftTableData = response
        // this.$refs.middle.parentMsg(this.leftTableData);
        });
        },
        
    async getTableRightList(){
        this.queryForm.groupBy = 'projectAndAuthor'
        listRightInfoPage(this.queryForm).then(response => {
        this.rightTableData = response
        // this.$refs.middle.parentMsg(this.rightTableData);
        });
        },
    async getList(){
        console.log('queryForm = ' ,this.queryForm)
        listHistogram(this.queryForm).then(response => {

        this.codeTeamData = response.data.codeTeamContributions
        this.codeFrontData = response.data.codeFrontContributors

        this.$refs.header.parentMsg(this.codeTeamData);
        this.$refs.footer.parentMsg(this.codeFrontData);
      });
      },
        /** 搜索按钮操作 */
    notify: function (){
        console.log('beginDate = ',this.queryForm.beginDate,'endDate = ',this.queryForm.endDate,
        'managered',this.queryForm.managered);
            if(this.queryForm){
                this.getList()
                this.getTableLeftList()
                this.getTableRightList()
            }
        },
    // 获取当前年份月
    getDate(){
      const nowDate = new Date();
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
      };
      this.queryForm.finishYear = date.year;
      this.queryForm.finishMonth = date.month > 10 ? date.month : date.month;
    },
    changeTime(e){
      this.$forceUpdate()
    },
    getDay(day){
        var today = new Date()
        var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day
        today.setTime(targetday_milliseconds); //注意，这行是关键代码
        var tYear = today.getFullYear()
        var tMonth = today.getMonth()
        var tDate = today.getDate()
        tMonth = this.doHandleMonth(tMonth + 1)
        tDate = this.doHandleMonth(tDate)
        return tYear + "-" + tMonth + "-" + tDate
        },
    doHandleMonth(month) {
        var m = month
        if (month.toString().length == 1) {
            m = "0" + month
        }
        return m;
        },
    handleSelectedDate(){
        this.queryForm.beginDate =  this.getDay(-1)
        this.queryForm.endDate = this.getDay(0)
        console.log('this.beginDate = ' , this.queryForm.beginDate , 'endDate = ' , this.queryForm.endDate)
        },
    },
    created () { 
        this.handleSelectedDate()
    },
    activated () { },
    mounted () { },
    beforeDestroy () { }
  }
  </script>
  
  <style scoped>
.el-main{
    padding: 0px;
}
  </style>