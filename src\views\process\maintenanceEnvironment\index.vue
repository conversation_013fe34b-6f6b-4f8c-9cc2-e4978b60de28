<template>
  <div class="app-container">
    <el-form :model="queryForm" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="审批编号" prop="processInstanceId">
        <el-input v-model="queryForm.processInstanceId" clearable @keyup.enter.native="handleCurrentChange(1)" />
      </el-form-item>
      <el-form-item label="当前审批状态" prop="status">
        <el-select v-model="queryForm.status" clearable>
          <el-option v-for="item in handleSelectData('当前审批状态')"
                     :key="item"
                     :value="item"
                     :label="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="当前审批结果" prop="result">
        <el-select v-model="queryForm.result" clearable>
          <el-option v-for="item in handleSelectData('当前审批结果')"
                     :key="item"
                     :value="item"
                     :label="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="操作岗位" prop="operationPost">
        <el-select v-model="queryForm.operationPost" clearable  @change="postChange">
          <el-option v-for="item in handleSelectData('操作岗位')"
                     :key="item"
                     :value="item"
                     :label="item" />
        </el-select>
      </el-form-item>

      <el-form-item label="操作分类" prop="operationCategory">
        <el-select v-model="queryForm.operationCategory" clearable>
          <el-option v-for="item in operationCategoryList"
                     :key="item"
                     :value="item"
                     :label="item" />
        </el-select>
      </el-form-item>

      <el-form-item label="操作风险等级" prop="riskLevel">
        <el-select v-model="queryForm.riskLevel" clearable>
          <el-option v-for="item in handleSelectData('操作风险等级')"
                     :key="item"
                     :value="item"
                     :label="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="当前负责人" prop="currentResponsiblePerson">
        <el-input v-model="queryForm.currentResponsiblePerson" clearable @keyup.enter.native="handleCurrentChange(1)" />
      </el-form-item>

      <el-form-item label="创建时间">
        <el-date-picker
          v-model="queryForm.originatorTimeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateChange"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleCurrentChange(1)">查询</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" sizi="mini" style="width: 100%" v-loading="loading" stripe ref="processMaintenanceEnvironmentTable"
              :default-sort="{ prop: 'originatorTime', order: 'descending' }"
              height="calc(100vh - 280px)"
              @sort-change="handleSortChange" >
      <el-table-column prop="processInstanceId" label="审批编号" align="center"></el-table-column>
      <el-table-column prop="operationCategory" label="操作分类" align="center"></el-table-column>
      <el-table-column prop="operationPurpose" label="操作目的" align="center" width="200">
        <template slot-scope="scope">
          <el-tooltip placement="top" >
            <template #content>
              <div class="custom-tooltip">
                {{ scope.row.operationPurpose }}
              </div>
            </template>
            <span class="multi-line-text">
            {{ scope.row.operationPurpose}}
            </span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="riskLevel" label="操作风险等级" align="center"></el-table-column>
      <el-table-column prop="operationPost" label="操作岗位" align="center"></el-table-column>
      <el-table-column prop="plannedTime" label="计划开始时间-计划结束时间" align="center" sortable="custom">
        <template slot-scope="scope">
          {{formatMinutes(scope.row.plannedTime)}}
        </template>
      </el-table-column>
      <el-table-column prop="result" label="当前审批结果" align="center"></el-table-column>
      <el-table-column prop="status" label="当前审批状态" align="center"></el-table-column>
      <el-table-column align="center" label="审批终态">
        <template slot-scope="scope">
          <span>{{  scope.row.status === '已结束' ? scope.row.result : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="currentResponsiblePerson" label="当前负责人" align="center" ></el-table-column>
      <el-table-column prop="currentNode" label="当前节点" align="center" ></el-table-column>
      <el-table-column prop="lastActionTime" label="更新时间" align="center" sortable="custom" width="150"></el-table-column>
      <el-table-column prop="originatorUser" label="创建人" align="center" ></el-table-column>
      <el-table-column prop="originatorTime" label="创建时间" align="center" sortable="custom" width="150"></el-table-column>
    </el-table>
    <div style="margin-top: 20px;text-align: center">
      <el-pagination
        background
        :total="total"
        :current-page="queryForm.pageNum"
        :page-size="queryForm.pageSize"
        :current-page.sync="queryForm.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { listProcessMaintenanceEnvironment } from "@/api/process/processMaintenanceEnvironment"
import { listDatas } from "@/api/system/dict/data"
export default {
  name: "processMaintenanceEnvironment",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      total: 0,
      // 查询参数
      queryForm: {
        pageNum: 1,
        pageSize: 50,
        orderByField: 'originatorTime',
        orderRule: 'desc',
        processInstanceId: '',
        result: '',
        currentResponsiblePerson: '',
        status: '',
        originatorTimeRange: '',
        operationCategory: '',
        operationPost: '',
        riskLevel: '',
      },
      tableData: [],
      selectDate: [],
      operationCategoryList: [],
    };
  },
  async created() {
    this.initTime()
    this.loadSelectData()
  },
  methods: {
    //加载下拉框数据
    async loadSelectData() {
      await listDatas({ dictType: 'process_maintenance_environment' }).then(res => {
        if (res.code === 200) {
          this.selectDate = res.data
        }
      })
    },
    // 处理下拉框数据
    handleSelectData(dictLabel) {
      const selectDate = this.selectDate.filter(item => item.dictLabel === dictLabel)
      if( selectDate && selectDate.length > 0){
        return selectDate[0].dictValue.split(',');
      }else {
        return []
      }
    },
    // 处理时间
    initTime() {
      // 获取当前日期
      const currentDate = new Date();
      // 获取前 7 天的日期
      const sevenDaysAgo = new Date(currentDate);
      sevenDaysAgo.setDate(currentDate.getDate() - 6);
      // 设置开始时间为当天的00:00:00
      sevenDaysAgo.setHours(0, 0, 0, 0);
      // 设置结束时间为当天的23:59:59
      const endOfDay = new Date(currentDate);
      endOfDay.setHours(23, 59, 59, 999);
      // 设置时间范围
      this.queryForm.originatorTimeRange = [
        sevenDaysAgo,
        endOfDay
      ];
    },
    handleDateChange(){
      if (this.queryForm.originatorTimeRange && this.queryForm.originatorTimeRange.length === 2) {
        const endTime = new Date(this.queryForm.originatorTimeRange[1]);
        endTime.setHours(23, 59, 59, 999);
        this.queryForm.originatorTimeRange[1] = endTime;
      }
    },
    //岗位切换
    postChange(){
      this.queryForm.operationCategory = '';
      if (this.queryForm.operationPost === '运维岗'){
        this.operationCategoryList = this.handleSelectData('操作分类_运维');
      } else if (this.queryForm.operationPost === 'DBA岗'){
        this.operationCategoryList = this.handleSelectData('操作分类_DBA');
      } else if (this.queryForm.operationPost === 'BI岗'){
        this.operationCategoryList = this.handleSelectData('操作分类_BI');
      } else {
        this.operationCategoryList = [];
      }
    },
    // 排序
    handleSortChange (data) {
      this.queryForm.orderByField = data.prop
      if (data.order === 'ascending') {
        this.queryForm.orderRule = 'asc'
      } else if (data.order === 'descending') {
        this.queryForm.orderRule = 'desc'
      } else {
        this.queryForm.orderRule = ''
      }
      this.handleCurrentChange(1)
    },
    // 查询运维&DBA&BI生产环境维护操作申请管理数据
    handleQuery () {
      this.loading = true
      listProcessMaintenanceEnvironment(this.queryForm).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.total = res.total
          this.tableData = res.rows
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 改变每页显示数量
    handleSizeChange(val) {
      this.queryForm.pageSize = val
      this.handleQuery()
    },
    // 改变当前页码
    handleCurrentChange(val) {
      this.queryForm.pageNum = val
      this.handleQuery()
    },
    // 重置
    resetQuery (){
      this.queryForm = {
        pageNum: 1,
        pageSize: 50,
        orderByField: 'originatorTime',
        orderRule: 'desc',
        processInstanceId: '',
        result: '',
        currentResponsiblePerson: '',
        status: '',
        originatorTimeRange: [],
        operationCategory: '',
        operationPost: '',
        riskLevel: '',
      }
      this.initTime()
      this.$refs.processMaintenanceEnvironmentTable.clearSort();
      this.$refs.processMaintenanceEnvironmentTable.sort('originatorTime', 'descending');
      this.handleQuery()
    },
    // 将分钟数转换为天-小时-分钟格式
    formatMinutes(minutes) {
      if (typeof minutes !== 'number' || minutes < 0) {
        return '';
      }
      const days = Math.floor(minutes / (24 * 60)); // 计算天数
      const hours = Math.floor((minutes % (24 * 60)) / 60); // 计算小时数
      const mins = minutes % 60; // 计算分钟数

      let result = '';
      if (minutes < 60) return `${mins}分钟`;
      if (days > 0) result += `${days}天`;
      result += `${hours}小时`;
      result += `${mins}分钟`;
      return result;
    }
  }
};
</script>
<style scoped lang="scss">

.custom-tooltip {
  max-width: 400px;
  white-space: normal;
  word-wrap: break-word;
  line-height: 1.5;
}
.multi-line-text {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
}
</style>
