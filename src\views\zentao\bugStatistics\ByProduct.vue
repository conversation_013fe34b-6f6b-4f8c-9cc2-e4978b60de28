<template>
    <div class="app-container">
        <el-form :model="queryForm" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
            <el-form-item label="">
                <span>创建时间&nbsp;&nbsp;&nbsp;</span>
                <el-date-picker v-model="queryForm.createTimeStart" type="date" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd 00:00:00"></el-date-picker>
                <span>&nbsp;&nbsp;&nbsp;&nbsp;至&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                <el-date-picker v-model="queryForm.createTimeEnd" type="date" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd 23:59:59"></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button icon="el-icon-search" type="primary" @click="handleQuery">查询</el-button>
                <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
                <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
            </el-form-item>
            <el-form-item>
                <el-tag effect="plain" type="info" size="medium">最后更新时间：{{updateTime}}</el-tag>
            </el-form-item>
        </el-form>
        <el-table :data="tableData" sizi="mini" style="width: 100%" v-loading="loading" stripe
            :default-sort="{ prop: 'testTotal', order: 'descending' }">
            <el-table-column prop="productName" label="产品名称" width="300"></el-table-column>
            <el-table-column prop="testTotal" label="测试环境BUG总量" sortable
                :sort-method="(a, b) => { return a.testTotal - b.testTotal }">
                <template slot-scope="scope">
                  <el-button type="text" @click="toList(scope.row.productName, 'test', '')" v-if="scope.row.testTotal !== 0">{{scope.row.testTotal}}</el-button>
                  <span v-else>0</span>
                </template>
            </el-table-column>
            <el-table-column prop="prodTotal" label="生产环境BUG总量" sortable
                :sort-method="(a, b) => { return a.prodTotal - b.prodTotal }">
                <template slot-scope="scope">
                  <el-button type="text" @click="toList(scope.row.productName, 'prod', '')" v-if="scope.row.prodTotal !== 0">{{scope.row.prodTotal}}</el-button>
                  <span v-else>0</span>
                </template>
            </el-table-column>
            <el-table-column prop="activeNum" label="测试BUG激活数" sortable
                :sort-method="(a, b) => { return a.activeNum - b.activeNum }">
                <template slot-scope="scope">
                    <el-button type="text" @click="toList(scope.row.productName, 'test', 'active')" v-if="scope.row.activeNum !== 0">{{scope.row.activeNum}}</el-button>
                    <span v-else>0</span>
                </template>
            </el-table-column>
            <el-table-column prop="resolvedNum" label="测试BUG已解决数" sortable
                :sort-method="(a, b) => { return a.resolvedNum - b.resolvedNum }">
                <template slot-scope="scope">
                    <el-button type="text" @click="toList(scope.row.productName, 'test', 'resolved')" v-if="scope.row.resolvedNum !== 0">{{scope.row.resolvedNum}}</el-button>
                    <span v-else>0</span>
                </template>
            </el-table-column>
            <el-table-column prop="closedNum" label="测试BUG已关闭数" sortable
                :sort-method="(a, b) => { return a.closedNum - b.closedNum }">
                <template slot-scope="scope">
                    <el-button type="text" @click="toList(scope.row.productName, 'test', 'closed')" v-if="scope.row.resolvedNum !== 0">{{scope.row.closedNum}}</el-button>
                    <span v-else>0</span>
                </template>
            </el-table-column>
            <el-table-column prop="fixedProp" label="测试BUG修复率" sortable
                :sort-method="(a, b) => { return a.fixedProp - b.fixedProp }">
                <template slot-scope="scope">
                    <span
                        v-if="scope.row.fixedProp && scope.row.fixedProp !== 0">{{ scope.row.fixedProp }}%</span>
                    <span v-else>0</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="150">
              <template slot-scope="scope">
                <el-button type="text" @click="showDetail(scope.row)">查看详情</el-button>
              </template>
            </el-table-column>
        </el-table>

        <el-dialog  title="BUG分类统计" :visible.sync="open" width="40%">
          <el-table ref="table" v-loading="loadingBranch" :data="bugTableData"    border
                    style="width: 100%"
                    :default-sort = "{prop: 'testTotal', order: 'descending'}">
            <el-table-column
              prop="typeName"
              label="BUG类型"
              width="180">
            </el-table-column>
            <el-table-column
              prop="testTotal"
              label="测试环境数量"
              sortable
              width="180">
            </el-table-column>
            <el-table-column
              prop="prodTotal"
              sortable
              label="生产环境数量">
            </el-table-column>
          </el-table>
        </el-dialog>
    </div>
</template>

<script>
import { statisticByProduct } from '@/api/business/bugAttribution.js'
import { statisticByTypeAndOther } from '@/api/business/bugAttribution'

export default {
    name: "ByProject",
    data() {
        return {
            // 遮罩层
            loading: false,
            // 显示搜索条件
            showSearch: true,
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            loadingBranch: true,
            // 查询参数
            queryForm: {
                createTimeStart: '',
                createTimeEnd: ''
            },
            tableData: [],
            bugTableData: [],
            updateTime: null
        };
    },
    created() {
        this.initTime(),
        this.handleQuery()
    },
    methods: {
        // 处理时间
        getFormatDate(date) {
            var month = date.getMonth() + 1
            var strDate = date.getDate()
            if (month >= 1 && month <= 9) {
                month = '0' + month
            }
            if (strDate >= 0 && strDate <= 9) {
                strDate = '0' + strDate
            }
            var currentDate = date.getFullYear() + '-' + month + '-' + strDate + ' ' + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds()
            return currentDate
        },
        initTime() {
            const currentDate = new Date();
            const createTimeEnd = this.getFormatDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)).substr(0, 11) + '23:59:59'
            const createTimeStart = this.getFormatDate(new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)).substr(0, 11) + '00:00:00'
            this.queryForm = {
                createTimeStart: createTimeStart,
                createTimeEnd: createTimeEnd
            }
        },
        // 查询bug统计数据
        handleQuery() {
            this.loading = true
            statisticByProduct(this.queryForm).then(res => {
                this.loading = false
                if (res.code === 200) {
                    this.tableData = res.data.list
                    this.updateTime = res.data.lastUpdateTime
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        handleBugQuery(query) {
          this.loading = true
          if (this.$refs.table) {
            this.$refs.table.sort('testTotal', 'descending')
          }
          statisticByTypeAndOther(query).then(res => {
            if (res.code === 200) {
              this.bugTableData = res.data
            } else {
              this.$message.error(res.msg)
            }
            this.loadingBranch = false
            this.loading =false;
          })
        },
        // 取消按钮
        cancel() {
          this.open = false;
        },
        // 重置
        resetQuery() {
          this.queryForm = {
            createTimeStart: '',
            createTimeEnd: ''
          }
            this.initTime();
            this.handleQuery()
        },
        // 导出按钮操作
        handleExport() {
          this.download('bug/statisticByProduct/export', {
            ...this.queryForm
          }, `按产品统计bug数量_${new Date().getTime()}.xlsx`)
        },
        toList (productName, env, status) {
          let params = {
            productName: productName,
            env: env,
            createTimeStart: this.queryForm.createTimeStart,
            createTimeEnd: this.queryForm.createTimeEnd,
            status: status
          }
          this.$router.push({ path: '/manage/bug', query: params})
      },
        showDetail (row) {
          this.open = true;
          const queryBugStatisticForm = {
            createTimeStart: this.queryForm.createTimeStart,
            createTimeEnd: this.queryForm.createTimeEnd,
            productName: row.productName
          }
          this.handleBugQuery(queryBugStatisticForm);
        }
    }
};
</script>
