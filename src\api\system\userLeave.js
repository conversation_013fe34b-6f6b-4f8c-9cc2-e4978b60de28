import request from '@/utils/request'

// 查询请假记录列表
export function listUserLeave(query) {
  return request({
    url: '/system/userLeave/list',
    method: 'get',
    params: query
  })
}

// 查询请假记录详细
export function getUserLeave(id) {
  return request({
    url: '/system/userLeave/' + id,
    method: 'get'
  })
}

// 新增请假记录
export function addUserLeave(data) {
  return request({
    url: '/system/userLeave',
    method: 'post',
    data: data
  })
}

// 修改请假记录
export function updateUserLeave(data) {
  return request({
    url: '/system/userLeave',
    method: 'put',
    data: data
  })
}

// 删除请假记录
export function delUserLeave(id) {
  return request({
    url: '/system/userLeave/' + id,
    method: 'delete'
  })
}

//各部门请假人数统计
export function statisticUserLeave(query) {
  return request({
    url: '/system/userLeave/statistic/userLeaveCount',
    method: 'get',
    params: query
  })
}

//统计部门近两个月请假的总人数
export function userLeaveCountByDept() {
  return request({
    url: '/system/userLeave/statistic/userLeaveCountByDept',
    method: 'get',
  })
}
