<template>
    <div class="app-container">
      <el-form :model="queryForm"  size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="预警类型" prop="warnType">
          <el-select
            filterable
            allow-create
            v-model="queryForm.warnType"
            placeholder="请输入"
            clearable
          >
          <el-option
                  v-for="item in optionsWarnTypeData"
                  :key="item.key"
                  :value="item.value"
                  :label="item.name"
                />
          </el-select>
        </el-form-item>
        <el-form-item label="处理状态" prop="handleStatus">
          <el-select
                    filterable
                    allow-create
                    clearable
                    placeholder="请选择"
                    v-model="queryForm.handleStatus"
                  >
                  <el-option
                  v-for="item in optionsHandleStatusData"
                  :key="item.key"
                  :value="item.value"
                  :label="item.name"
                />
          </el-select>
        </el-form-item>
        <el-form-item label="">
                <span>预警时间&nbsp;&nbsp;&nbsp;</span>
                <el-date-picker
                            v-model="queryForm.beginDate"
                            type="date"

                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd 00:00:00"
                            >
                        </el-date-picker>
                        <span>&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
            </el-form-item>
            <el-form-item label="">
                <el-date-picker
                            v-model="queryForm.endDate"
                            type="date"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd 23:59:59"
                            >
                        </el-date-picker>
            </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>


      <el-table v-loading="loading" :data="warnRecordList" @selection-change="handleSelectionChange" :cell-style="changeCellStyle" height="calc(100vh - 210px)" stripe border>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="预警编号" align="center" prop="warnCode" />
        <el-table-column label="预警内容" align="center" prop="warnContent" />
        <el-table-column label="被预警人" align="center" prop="nickName" />
        <el-table-column label="状态" align="center" prop="warnStatus">
          <template slot-scope="scope">
            <span v-show="scope.row.warnStatus ==0">未通知</span>
            <span v-show="scope.row.warnStatus ==1">通知失败</span>
            <span v-show="scope.row.warnStatus ==2">已通知</span>
          </template>
        </el-table-column>
        <el-table-column label="预警类型" align="center" prop="warnType">
          <template slot-scope="scope">
            <span v-show="scope.row.warnType ==1">考勤</span>
            <span v-show="scope.row.warnType ==2">任务</span>
            <span v-show="scope.row.warnType ==3">代码</span>
            <span v-show="scope.row.warnType ==4">用例</span>
            <span v-show="scope.row.warnType ==5">项目</span>
            <span v-show="scope.row.warnType ==6">同步</span>
            <span v-show="scope.row.warnType ==7">文档</span>
            <span v-show="scope.row.warnType ==8">管理</span>
          </template>
        </el-table-column>
        <el-table-column label="预警级别" align="center" prop="warnLevel">
          <template slot-scope="scope">
            <span v-show="scope.row.warnLevel ==0">P0</span>
            <span v-show="scope.row.warnLevel ==1">P1</span>
            <span v-show="scope.row.warnLevel ==2">P2</span>
            <span v-show="scope.row.warnLevel ==3">P3</span>
            <span v-show="scope.row.warnLevel ==4">P4</span>
          </template>
        </el-table-column>
        <el-table-column label="预警内容" align="center" prop="warnContent" />
        <el-table-column label="预警时间" align="center" prop="warnTime" />
        <el-table-column label="预警方式" align="center" prop="warnWay">
          <template slot-scope="scope">
            <span v-show="scope.row.warnWay ==1">钉钉</span>
            <span v-show="scope.row.warnWay ==2">邮件</span>
            <span v-show="scope.row.warnWay ==4">短信</span>
            <span v-show="scope.row.warnWay ==8">电话</span>
          </template>
        </el-table-column>
        <el-table-column label="处理状态" align="center" prop="handleStatus">
          <template slot-scope="scope">
            <span v-show="scope.row.handleStatus ==0">否</span>
            <span v-show="scope.row.handleStatus ==2">是</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <span v-show="scope.row.handleStatus ==2">/</span>
            <span v-show="scope.row.handleStatus ==0">
              <el-button
            size="mini"
            type="text"
            @click="handleRecord(scope.row)"
          >
            待处理
          </el-button>
            </span>
        </template>
        </el-table-column>
      </el-table>

      <div class="block">
      <el-pagination
        background
        layout="->,total, sizes,prev, pager, next, jumper"
        :total="total"
        :current-page="queryForm.pageNo"
         :page-size="queryForm.pageSize"
         :current-page.sync="queryForm.pageNo"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
      </div>

      <!-- 添加或修改预警记录对话框 -->
      <el-dialog :title="title" :visible.sync="open"  width="500px">
        <el-form ref="form" :model="form" >
          <span>{{this.form.warnContent}}</span>
          <br/>
          <el-form-item>
            <el-input
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
              v-model="form.handleContent">
            </el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </template>

  <script>

 import {
  getWarnPersonalPage,getWarnHandleSelets,getWarnTypeSelets,updateHandleContent
    } from '@/api/warn/warn.js'
  export default {
    name: "WarnRecord",
    data() {
      return {
        // 按钮loading
        buttonLoading: false,
        // 遮罩层
        loading: false,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 预警记录表格数据
        warnRecordList: [],
        // 预警类型下拉框
        optionsWarnTypeData: [],
        // 处理状态下拉框
        optionsHandleStatusData: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        total: 0,
        // 查询参数
        queryForm: {
          pageNo: 1,
          size: 10,
          warnType: -1,
          beginDate: '',
          endDate: '',
        },
        // 表单参数
        form: {},
      };
    },
    async created() {
      await this.initTime();
      this.getWarnPersonalPage();
      this.getWarnTypeSelets();
      this.getWarnHandleSelets();
    },
    methods: {
     //  改变每页显示数量
      handleSizeChange(val) {
        console.log('pageSize',val)
        this.queryForm.pageSize = val;
        this.getWarnPersonalPage();
       },
  resetQuery() {
    this.queryForm.warnType= '';
      this.initTime();
      this.resetForm("queryForm");
      this.handleQuery();
    },
    //  改变当前页码
      handleCurrentChange(val) {
        console.log('pageNo',val)
      this.queryForm.pageNo = val;
      this.getWarnPersonalPage();
       },
      /** 查询预警记录列表 */
      getWarnPersonalPage() {
        this.loading = true;
        getWarnPersonalPage(this.queryForm).then(response => {
          console.log('',response)
          this.warnRecordList = response.data.records;
          this.total = response.data.total;
          this.queryForm.pageSize = response.data.size;
          this.loading = false;
        });
      },
      /** 查询预警类型下拉框 */
      getWarnTypeSelets(){
        getWarnTypeSelets().then(response => {
          this.optionsWarnTypeData = response.data;
        });
      },
      /** 查询处理状态下拉框 */
      getWarnHandleSelets(){
        getWarnHandleSelets().then(response => {
          this.optionsHandleStatusData = response.data;
        });
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.getWarnPersonalPage();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id)
        this.single = selection.length!==1
        this.multiple = !selection.length
      },
      /** 修改按钮操作 */
      handleRecord(row) {
          this.form.warnDetailId = row.warnDetailId
          this.form.warnContent = row.warnContent
          console.log('id = ',this.form.id,'warnContent = ',this.form.warnContent)
          this.open = true;
          this.title = "确认预警信息";
      },
      /** 提交按钮 */
      submitForm() {
        if (this.form.handleContent == '' || this.form.handleContent == null) {
          alert('处理内容不能为空!')
          return
        }
        this.$refs["form"].validate(valid => {
          if (valid) {
            this.buttonLoading = true;
            if (this.form.warnDetailId != null) {
              updateHandleContent(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getWarnPersonalPage();
              }).finally(() => {
                this.buttonLoading = false;
              });
            }
          }
        });
      },
      // 处理时间
      getFormatDate(date) {
        var month = date.getMonth() + 1
        var strDate = date.getDate()
        if (month >= 1 && month <= 9) {
          month = '0' + month
        }
        if (strDate >= 0 && strDate <= 9) {
          strDate = '0' + strDate
        }
        var currentDate = date.getFullYear() + '-' + month + '-' + strDate + ' ' + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds()
        return currentDate
      },
      initTime() {
        const endDate = this.getFormatDate(new Date()).substr(0, 11) + '23:59:59'
        const beginDate = this.getFormatDate(new Date(new Date() - 3600 * 1000 * 24 * 29)).substr(0, 11) + '00:00:00'
        this.queryForm.beginDate = beginDate
        this.queryForm.endDate = endDate
      },
      changeCellStyle({ row, column, rowIndex, columnIndex }) {
        if(row.warnLevel == 0 && columnIndex ==6){
          return 'color:#FF0000'
        }else if(row.warnLevel == 1 && columnIndex ==6){
          return 'color:#FFA500'
        }

        if(row.warnLevel == 0 &&  columnIndex == 2){
          return 'color:#FF0000'
        }else if(row.warnLevel == 1 && columnIndex == 2){
          return 'color:#FFA500'
        }
      }
    }
  };
  </script>
