<template>
  <div class="app-container">
    <el-form :model="queryForm" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="年份" prop="year">
        <el-input v-model="queryForm.year" clearable @keyup.enter.native="handleCurrentChange(1)" />
      </el-form-item>
      <el-form-item label="月份" prop="month">
        <el-input v-model="queryForm.month" clearable @keyup.enter.native="handleCurrentChange(1)" />
      </el-form-item>
      <el-form-item label="报告类型" prop="reportType">
        <el-select v-model="queryForm.reportType" clearable>
          <el-option v-for="item in reportTypeList"
                     :key="item.value"
                     :value="item.value"
                     :label="item.label" />
        </el-select>
      </el-form-item>
      <el-form-item label="姓名" prop="userName">
        <el-input v-model="queryForm.userName" clearable @keyup.enter.native="handleCurrentChange(1)" />
      </el-form-item>
      <el-form-item label="小组" prop="groupName">
        <el-select v-model="queryForm.groupName" clearable>
          <el-option v-for="(item,index) in groupList"
                     :key="index"
                     :value="item.deptName"
                     :label="item.deptName"/>
        </el-select>
      </el-form-item>
      <el-form-item label="岗位" prop="postName">
        <el-select v-model="queryForm.postName" clearable>
          <el-option v-for="(item,index) in postList"
                     :key="index"
                     :value="item.roleName"
                     :label="item.roleName"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleCurrentChange(1)">查询</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" sizi="mini" style="width: 100%" v-loading="loading" stripe ref="performanceReportTable" height="calc(100vh - 280px)">
      <el-table-column prop="year" label="年份" align="center" />
      <el-table-column prop="month" label="月份" align="center" />
      <el-table-column prop="reportType" label="报告类型" align="center" width="100">
        <template slot-scope="scope">
          <el-tag type="primary" v-if="scope.row.reportType === '0'">个人</el-tag>
          <el-tag type="success" v-if="scope.row.reportType === '1'">小组</el-tag>
          <el-tag type="warning" v-if="scope.row.reportType === '2'">岗位</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="userName" label="姓名" align="center" />
      <el-table-column prop="groupName" label="所属组" align="center" />
      <el-table-column prop="postName" label="岗位名称" align="center" />

      <el-table-column prop="result" label="操作" align="center">
         <template slot-scope="scope">
           <el-button type="text" @click="queryReport(scope.row.reportUrl)">查看报告</el-button>
         </template>
      </el-table-column>
    </el-table>
    <div style="margin-top: 20px;text-align: center">
      <el-pagination
        background
        :total="total"
        :current-page="queryForm.pageNum"
        :page-size="queryForm.pageSize"
        :current-page.sync="queryForm.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { performanceReportList } from "@/api/business/performanceReport"
import {deptSelect} from "@/api/commonBiz";
import {listRole} from "@/api/system/role";
export default {
  name: "performanceReport",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      total: 0,
      // 查询参数
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        year: '',
        month: '',
        reportType: '',
        userName: '',
        groupName: '',
        postName: ''
      },
      tableData:[],
      groupList: [],
      postList: [],
      reportTypeList: [
        {label: '个人', value: '0'},
        {label: '小组', value: '1'},
        {label: '岗位', value: '2'},
      ]
    };
  },
  async created() {
    await this.getDeptList()
    await listRole().then(response => {
      if (response.code === 200) {
        this.postList = response.rows.filter(item => item.roleKey !== 'admin')
      }
    })
    this.handleQuery();
  },
  methods: {
    //获取部门列表
    async getDeptList() {
      await deptSelect(this.queryParams).then(response => {
        if (response.code === 200) {
          this.groupList = response.data;
        }
      });
    },
    // 查询AI绩效报告列表数据
    handleQuery () {
      this.loading = true
      performanceReportList(this.queryForm).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.total = res.total
          this.tableData = res.rows
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 改变每页显示数量
    handleSizeChange(val) {
      this.queryForm.pageSize = val
      this.handleQuery()
    },
    // 改变当前页码
    handleCurrentChange(val) {
      this.queryForm.pageNum = val
      this.handleQuery()
    },
    // 重置
    resetQuery (){
      this.$refs.queryForm.resetFields();
      this.handleQuery()
    },
    //查看报告
    queryReport(reportUrl) {
      console.log("OSS URL:", reportUrl);
      const safeUrl = reportUrl.startsWith('http://') && window.location.protocol === 'https:'
        ? reportUrl.replace('http://', 'https://')
        : reportUrl;
      const link = document.createElement('a');
      link.href = safeUrl;
      link.target = '_blank';
      link.rel = 'noopener noreferrer';
      document.body.appendChild(link);
      link.click();
      setTimeout(function () {
        document.body.removeChild(link);
        window.URL.revokeObjectURL(safeUrl);
      }, 0);
    }
  }
};
</script>
