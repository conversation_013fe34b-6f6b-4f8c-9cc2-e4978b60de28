<template>
    <div class="header-charts">
        <div ref="myEcharts" style="width:100%;height:500px;"></div>
    </div>
  </template>

  <script>
  import * as echarts from 'echarts'
  export default {
    name: 'Header',
    props: {},
    components: {},
    data () {
      return {
        myEcharts: {},
        deptNameData: ['资金开发组','Android开发组','IOS开发组','风控开发组','海外开发组','自营资产开发组','前端开发','财务开发组','引流资产开发组','电销催收开发组','架构开发组','BI组'],
        deptData: [0,0,0,0,0,0,0,0,0,0,0],
        codeTeamData:[],
        queryForm:{
            beginDate:'',
            endDate:'',
            managered: false ,
        }
    }
     },
    watch: {
    },
    computed: {
    },
    methods: {
        parentMsg: function (data) {
            this.codeTeamData = data
            this.drawLine(this.codeTeamData)
          },
        drawLine(data){
              if (data) {
              console.log('data = ' , data)
              this.deptNameData = []
              this.deptData = []
              data.forEach((item) => {
                // 遍历名字
                this.deptNameData.push(item.deptName)
              // 遍历团队
                this.deptData.push(item.totalLine)
              })
            }else {
                this.deptNameData = []
                this.deptData = []
              }
            var myEcharts = echarts.init(this.$refs['myEcharts'])
            var option;
            // 设置option
            option = {
              tooltip: {
                  trigger: 'axis',
                  axisPointer: {
                    type: 'shadow'
                  }
                },
                // 标题
                title: {
                  text: "各开发团队代码贡献量",
                  align: 'center'
                },
                xAxis: {
                    type: 'category',
                    data: this.deptNameData,
                    axisTick: {
                      alignWithLabel: true
                       },
                  axisLabel: {
                    interval: 0 // 强制显示所有标签
                  }
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                    data: this.deptData,
                    type: 'bar',
                    itemStyle: {
                        normal: {
                            color:function(){return "#"+Math.floor(Math.random()*(256*256*256-1)).toString(16);}
                        },
                      }
                    }
                ]
            };
            option && myEcharts.setOption(option);
          //   window.addEventListener('resize',()=>{
          //     this.myEcharts.resize()
          //  })
        }
    },
    created () { },
    activated () { },
    mounted () {

     },
    beforeDestroy () {
    }
  }
  </script>

  <style scoped>

  </style>
