<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:performanceRegistration:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:performanceRegistration:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:performanceRegistration:remove']"
        >删除</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:performanceRegistration:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="performanceRegistrationList" @selection-change="handleSelectionChange" height="calc(100vh - 270px)" stripe border>
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="id" v-if="true"/> -->
      <el-table-column label="序号" align="center" width="80">
     <template slot-scope="{ $index }">{{ $index + 1 }}</template>
      </el-table-column>
      <el-table-column label="所属组名" align="center" prop="deptName" />
      <el-table-column label="姓名" align="center" prop="userName" />
      <el-table-column label="评定年" align="center" prop="evalYear" />
      <el-table-column label="评定月" align="center" prop="evalMonth" />
      <el-table-column label="绩效级别" align="center" prop="level" />
      <el-table-column label="最新操作人" align="center" prop="updateBy" />
      <el-table-column label="最新操作时间" align="center" prop="updateTime" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:performanceRegistration:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:performanceRegistration:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改绩效登记对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

        <el-row>
          <el-col :span="12">
            <el-form-item label="评定年" prop="evalYear">
              <el-select
                v-model="form.evalYear"
                maxlength="4"
                placeholder="选择评定年"
                clearable
                size="small"
                style="width: 160px"
              >
                <el-option
                  v-for="year in years"
                  :key="year"
                  :label="year"
                  :value="year"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="评定月" prop="evalMonth">
              <el-select
                v-model="form.evalMonth"
                placeholder="选择评定月"
                clearable
                size="small"
                maxlength="2"
                style="width: 160px"
              >
                <el-option
                  v-for="dict in monthDict"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="绩效S" prop="performanceByS">
          <el-input v-model="form.performanceByS" placeholder="请输入姓名,用空格隔开" />
        </el-form-item>
        <el-form-item label="绩效A" prop="performanceByA">
          <el-input v-model="form.performanceByA" placeholder="请输入姓名,用空格隔开" />
        </el-form-item>
        <el-form-item label="绩效B" prop="performanceByB">
          <el-input v-model="form.performanceByB" placeholder="请输入姓名,用空格隔开" />
        </el-form-item>
        <el-form-item label="绩效C" prop="performanceByC">
          <el-input v-model="form.performanceByC" placeholder="请输入姓名,用空格隔开" />
        </el-form-item>
        <el-form-item label="绩效D" prop="performanceByD">
          <el-input v-model="form.performanceByD" placeholder="请输入姓名,用空格隔开" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPerformanceRegistration, getPerformanceRegistration, delPerformanceRegistration, addPerformanceRegistration, updatePerformanceRegistration } from "@/api/performregistration/performanceRegistration";
import {getDicts} from "../../../api/system/dict/data";
export default {
  name: "performanceRegistration",
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 绩效登记表格数据
      performanceRegistrationList: [],
      // 弹出层标题
      title: "",
      // 月份
      monthDict:[],
      years: [],
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: undefined,
        userName: undefined,
        evalYear: undefined,
        evalMonth: undefined,
        level: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        evalYear: [
          { required: true, message: "评定年不能为空", trigger: "blur" }
        ],
        evalMonth: [
          { required: true, message: "评定月不能为空", trigger: "blur" }
        ],
        performanceByS: [
          { required: true, message: "姓名不能为空", trigger: "blur" }
        ],
        performanceByA: [
          { required: true, message: "姓名不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.createYear();
    this.init();
    this.getList();
  },
  methods: {
    /** 查询绩效登记列表 */
    getList() {
      this.loading = true;
      listPerformanceRegistration(this.queryParams).then(response => {
        this.performanceRegistrationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    createYear(){
  // 获取当前年份和前两年的年份
      const currentYear = new Date().getFullYear();
      const startYear = 2021;

      // 构造年份选择器数据源
      for (let i = currentYear; i >= startYear; i--) {
        this.years.push(i.toString());
      }
      this.evalYear = currentYear;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        evalYear: undefined,
        evalMonth: undefined,
        performanceByS: undefined,
        performanceByA: undefined,
        performanceByB: undefined,
        performanceByC: undefined,
        performanceByD: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加绩效登记";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getPerformanceRegistration(id).then(response => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = "修改绩效登记";
      });
    },
    /** 提交按钮 */
    submitForm() {

      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          if (this.form.id != null) {
            updatePerformanceRegistration(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addPerformanceRegistration(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除绩效登记记录？').then(() => {
        this.loading = true;
        return delPerformanceRegistration(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    // handleExport() {
    //   this.download('system/performanceRegistration/export', {
    //     ...this.queryParams
    //   }, `performanceRegistration_${new Date().getTime()}.xlsx`)
    // }
    async init() {
      await getDicts("month").then(response => {
        this.monthDict = response.data
        this.monthDict = this.monthDict.filter(item => item.dictValue !== '-1');
        console.log('monthDict = ',this.monthDict)
      })
    },
  }
};
</script>
