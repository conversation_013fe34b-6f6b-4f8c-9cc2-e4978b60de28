<!-- 处理弹窗 -->
<template>
  <div>
    <el-dialog title="慢SQL问题处理" :visible.sync="visible" width="500px" @close="cancel">
      <el-form>
        <el-form-item label="处理状态" prop="status">
          <el-select v-model="formData.status" placeholder="请选择处理状态" style="width: 300px">
            <el-option label="已处理" value="2"></el-option>
            <el-option label="未处理" value="1"></el-option>
            <el-option label="无需处理" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="处理说明" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            placeholder="请输入处理原因"
            rows="4"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm" :loading="btnLoading">确认</el-button>
        <el-button @click="cancel">取消</el-button>
      </span>
      </el-dialog>
  </div>
</template>
<script>
import {
  sqlProcess
} from "@/api/system/slowQuery"
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    processData: {
      type: Object
    }
  },
  data() {
    return {
      visible: this.dialogVisible,
      btnLoading: false,
      formData: {
        idList: [],
        status: '2',
        remark: ''
      }
    }
  },
  watch: {
    dialogVisible (val) {
      this.visible = val
      if (this.visible) {
        this.formData = Object.assign(this.formData, this.processData)
      }
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    cancel () {
      this.formData = {
        idList: [],
        status: '2',
        remark: ''
      }
      this.$emit('update:dialogVisible', false)
    },
    // 处理
    confirm () {
      this.btnLoading = true
      sqlProcess(this.formData).then((res) => {
        this.btnLoading = false
        if (res.code === 200) {
          this.$message.success('操作成功')
          this.cancel()
          this.$emit('callback')
        } else {
          this.$message.error(res.msg)
        }
      }).catch((err) => {
        this.btnLoading = false
      })
    },
  },
};
</script>
<style lang="scss" scoped>
</style>
