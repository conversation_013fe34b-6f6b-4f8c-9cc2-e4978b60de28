<template>
  <div>
    <!-- 添加最后更新时间 -->
    <el-alert
      :title="updateTime"
      :closable=false
      type="info">
    </el-alert>
    <div slot="header" class="chart-header">
      <el-button-group>
        <el-button
          v-if="tabs && tabs.length > 0"
          v-for="tab in tabs"
          :key="tab.value"
          :type="tab.value === activeTab ? 'primary' : 'default'"
          @click="switchTab(tab)"
          size="mini"
        >
          {{ tab.label }}
        </el-button>
        <!-- 占位按钮 -->
        <el-button v-else>暂无选项</el-button>
      </el-button-group>
    </div>
    <bar-chart :columns="monthList" :chartData="trendList" :title="title" :active-tab="activeTabName" />
  </div>
</template>

<script>
import BarChart from "./BarChart.vue";
import LineChart from "../NoShadowLineChart/LineChart.vue";
import {getHomeTrend} from "../../../../../api/system/homeTrend";

export default {
  components: {LineChart, BarChart},
  data() {
    return {
      activeTab: '5',
      monthList: [],
      trendList: [],
      updateTime: '',
    };
  },
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    columns: {
      type: Array,
      required: true
    },
    chartData: {
      type: Object,
      required: true
    },
    title: { // 新增标题的 Prop
      type: String,
      default: '' // 默认为空，如果不传递，则不会显示标题
    },
    tabs: {
      type: Array
    }
  },
  computed: {
    currentData() {
      // 根据当前选中的标签返回对应数据
      return this.chartData[this.activeTab];
    },
    activeTabName() {
      const activeTabObject = this.tabs.find(tab => tab.value === this.activeTab);
      return activeTabObject ? activeTabObject.label : 'Unknown Tab';
    }
  },
  created() {
    this.getHomeTrend(5);
  },
  methods: {
    switchTab(tab) {
      this.activeTab = tab.value;
      this.getHomeTrend(tab.value);
    },
    getHomeTrend(type) {
      getHomeTrend(type).then(res => {
        this.monthList = res.data.monthList;
        this.trendList = res.data.trendList;
        this.updateTime = '最后更新时间：' + res.data.updateTime;
      })
    }
  }
};
</script>

<style scoped>
.chart-header {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
  margin-top: 10px;
}
</style>
