<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="100px" size="small">
      <el-form-item label="审批编号" prop="processInstanceId">
        <el-input
          v-model="queryParams.processInstanceId"
          clearable
          placeholder="请输入审批编号"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="需求优先度" prop="priorityLevel">
        <el-select
          v-model="queryParams.priorityLevel"
          allow-create
          clearable
          filterable
          placeholder="请选择需求优先度"
          @change="handleQuery"
        >
          <el-option
            v-for="priority in priorityOptions"
            :key="priority"
            :label="priority"
            :value="priority"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="当前审批状态" prop="status">
        <el-select
          v-model="queryParams.status"
          allow-create
          clearable
          filterable
          placeholder="请选择当前审批状态"
          size="small"
          @change="handleQuery">
          <el-option
            v-for="status in approvalStatusOptions"
            :key="status"
            :label="status"
            :value="status"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="queryParams.originatorTimeRange"
          end-placeholder="结束日期"
          range-separator="至"
          start-placeholder="开始日期"
          type="daterange"
          @change="handleDateChange"
        />
      </el-form-item>
      <el-form-item label="业务大类" prop="businessCategory">
        <el-select
          v-model="queryParams.businessCategory"
          allow-create
          clearable
          default-first-option
          filterable
          placeholder="请选择业务大类"
          @change="handleQuery"
        >
          <el-option
            v-for="category in businessCategoryOptions"
            :key="category"
            :label="category"
            :value="category"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="当前审批结果" prop="result">
        <el-select
          v-model="queryParams.result"
          clearable
          placeholder="请选择当前审批结果"
          @change="handleQuery"
        >
          <el-option
            v-for="result in approvalResultOptions"
            :key="result"
            :label="result"
            :value="result"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="当前负责人" prop="currentResponsiblePerson">
        <el-input
          v-model="queryParams.currentResponsiblePerson"
          clearable
          placeholder="请输入当前负责人"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>


      <el-form-item>
        <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>


    <el-table ref="processInternalManagementSystemList" v-loading="loading" :data="processInternalManagementSystemList"
              :default-sort="{ prop: 'originatorTime', order: 'descending' }"
              height="calc(100vh - 280px)"
              @sort-change="handleSortChange">
      <el-table-column align="center" label="审批编号" prop="processInstanceId"/>
      <el-table-column align="center" label="业务大类" prop="businessCategory"/>
      <el-table-column align="center" label="需求明细" prop="requirementDetails">
        <template slot-scope="scope">
          <el-tooltip :disabled="scope.row.requirementDetails.length <= 20" placement="top">
            <template #content>
              <div class="custom-tooltip">
                {{ scope.row.requirementDetails }}
              </div>
            </template>
            <span class="multi-line-text">
            {{ scope.row.requirementDetails }}
            </span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column align="center" label="期望完成日期" prop="expectedCompletionDate" sortable="custom" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expectedCompletionDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="需求优先度" prop="priorityLevel"/>
      <el-table-column align="center" label="当前审批结果" prop="result"/>
      <el-table-column align="center" label="当前审批状态" prop="status"/>
      <el-table-column align="center" label="审批终态">
        <template slot-scope="scope">
          <span>{{ scope.row.status === '已结束' ? scope.row.result : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="当前负责人" prop="currentResponsiblePerson"/>
      <el-table-column align="center" label="当前节点" prop="currentNode"/>
      <el-table-column align="center" label="更新时间" prop="lastActionTime" sortable="custom" width="180"/>
      <el-table-column align="center" label="创建人" prop="originatorUser"/>
      <el-table-column align="center" label="创建时间" prop="originatorTime" sortable="custom" width="180"/>
    </el-table>

    <pagination
      v-show="total>0"
      :limit.sync="queryParams.pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :page.sync="queryParams.pageNum"
      :total="total"
      @pagination="getList"
    />
  </div>
</template>

<style lang="scss" scoped>
.custom-tooltip {
  max-width: 400px;
  white-space: normal;
  word-wrap: break-word;
  line-height: 1.5;
}

.multi-line-text {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
}
</style>

<script>
import {listProcessInternalManagementSystem} from "@/api/process/internalManagementSystem";
import {listDatas} from "@/api/system/dict/data";
import {parseTime} from "@/utils/ruoyi";

export default {
  name: "internalManagementSystem",
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 内部管理系统功能需求表格数据
      processInternalManagementSystemList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        processInstanceId: undefined,
        businessCategory: undefined,
        requirementDetails: undefined,
        expectedCompletionDate: undefined,
        priorityLevel: undefined,
        result: undefined,
        status: undefined,
        currentResponsiblePerson: undefined,
        currentNode: undefined,
        lastActionTime: undefined,
        originatorUser: undefined,
        originatorTime: undefined,
        originatorTimeStart: undefined,
        originatorTimeEnd: undefined,
        originatorTimeRange: undefined,
        orderByColumn: 'originatorTime',
        isAsc: 'desc'
      },
      priorityOptions: undefined,
      approvalStatusOptions: undefined,
      approvalResultOptions: undefined,
      businessCategoryOptions: undefined,
    };
  },
  async created() {
    this.initTime();
    await this.loadSelectData();
    await this.getList();
  },
  methods: {
    /** 查询内部管理系统功能需求列表 */
    async getList() {
      this.loading = true;
      await listProcessInternalManagementSystem(this.queryParams).then(res => {
        this.loading = false;
        if (res.code === 200) {
          this.processInternalManagementSystemList = res.rows;
          this.total = res.total;
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    initTime() {
      const today = new Date();
      const sixDaysAgo = new Date(today);
      sixDaysAgo.setDate(today.getDate() - 6);
      // 设置开始时间为当天的00:00:00
      sixDaysAgo.setHours(0, 0, 0, 0);
      // 设置结束时间为当天的23:59:59
      const endOfDay = new Date(today);
      endOfDay.setHours(23, 59, 59, 999);
      // 设置时间范围
      this.queryParams.originatorTimeRange = [
        sixDaysAgo,
        endOfDay
      ];
      this.handleDateChange();
    },
    async loadSelectData() {
      await listDatas({dictType: 'process_internal_management_system'}).then(res => {
        if (res.code === 200) {
          this.selectDate = res.data;
          this.priorityOptions = this.handleSelectData('需求优先度');
          this.approvalStatusOptions = this.handleSelectData('当前审批状态');
          this.approvalResultOptions = this.handleSelectData('当前审批结果');
          this.businessCategoryOptions = this.handleSelectData('业务大类');
        }
      });

    },
    handleSelectData(dictLabel) {
      const selectData = this.selectDate.filter(item => item.dictLabel === dictLabel);
      if (selectData && selectData.length > 0) {
        return selectData[0].dictValue.split(',');
      } else {
        return [];
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.handleDateChange();
      this.getList();
    },
    handleDateChange() {
      if (this.queryParams.originatorTimeRange && this.queryParams.originatorTimeRange.length === 2) {
        const endTime = new Date(this.queryParams.originatorTimeRange[1]);
        endTime.setHours(23, 59, 59, 999);
        this.queryParams.originatorTimeStart = parseTime(this.queryParams.originatorTimeRange[0]);
        this.queryParams.originatorTimeEnd = parseTime(endTime);
        this.queryParams.originatorTimeRange[1] = endTime;
      }
    },
    handleSortChange(data) {
      this.queryParams.orderByColumn = data.prop;
      if (data.order === 'ascending') {
        this.queryParams.isAsc = 'asc';
      } else if (data.order === 'descending') {
        this.queryParams.isAsc = 'desc';
      } else {
        this.queryParams.isAsc = '';
      }
      this.handleQuery();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.initTime();
      this.$refs.processInternalManagementSystemList.clearSort();
      this.$refs.processInternalManagementSystemList.sort('originatorTime', 'descending');
      this.handleQuery();
    }
  }
};
</script>
