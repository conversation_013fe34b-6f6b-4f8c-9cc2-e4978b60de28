<template>
  <div>
    <div className="header-charts">
      <div id="myEcharts" style="width:100%;height:500px"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'Header',
  props: ['codeTeamData'],
  components: {},
  data() {
    return {
      myEcharts: {},
      nameData: [],
      additonalsLineData: [],
      deleteLineData: [],
      codeFrontData: [],
      queryForm: {
        beginDate: '',
        endDate: '',
        managered: false,
      }
    }
  },
  watch: {},
  computed: {},
  methods: {
    parentMsg: function (data) {
      this.codeFrontData = data
      this.drawLine(data)
    },
    drawLine() {
      // if (this.option) {
      //   this.nameData = []
      //   this.additonalsLineData = []
      //   this.deleteLineData = []
      //   data.forEach((item) => {
      //     console.log('data = ', item)
      //     // 遍历名字
      //     this.nameData.push(item.nickName)
      //     // 遍历新增行
      //     this.additonalsLineData.push(item.num)
      //     // 遍历删除行
      //     this.deleteLineData.push(item.deleteLine)
      //   })
      // }
      var chartDom = document.getElementById('myEcharts');
      var myChart = echarts.init(chartDom);
      var option;
      let nickNameArr = []
      let numArr = []
      if (this.codeTeamData !== undefined) {
        this.codeTeamData.forEach((item, index) => {
          nickNameArr.push(item.nickName)
          numArr.push(item.num)
        })
      }
      option = {
        title: {
          text: "预警前10总数统计",
          left: 'left'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: nickNameArr,
          show: true
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: numArr,
            type: 'bar'
          }
        ]
      };

      option && myChart.setOption(option);
      // var myEcharts = echarts.init(this.$refs['myEcharts'])
      // var option;
      // option = {
      //   // 标题
      //   title: {
      //     text: "预警统计",
      //     align: 'center'
      //   },
      //   tooltip: {
      //     trigger: "axis",
      //   },
      //
      //   xAxis: {
      //     type: 'category',
      //     data: this.nameData
      //   },
      //   yAxis: {
      //     type: 'value'
      //   },
      //   series: [
      //     {
      //       data: this.deleteLineData,
      //       type: 'bar',
      //       stack: '1',
      //       name: '删除行数',
      //       itemStyle: {
      //         normal: {
      //           normal: {color: "#5C5C61"},
      //         },
      //       }
      //     },
      //     {
      //       name: '新增行数',
      //       type: 'bar',
      //       stack: '1',//根据此参数来堆叠数据
      //       data: this.additonalsLineData,
      //       itemStyle: {
      //         normal: {color: "#95CEFF"},
      //       }
      //     }
      //   ]
      // };
      // option && myEcharts.setOption(option);
      //   window.addEventListener('resize',()=>{
      //     this.myEcharts.resize()
      //  })
    }
  },
  created() {
  },
  activated() {
  },
  mounted() {
    this.drawLine()
  },
  beforeDestroy() {
  }
}
</script>

<style scoped>
</style>
