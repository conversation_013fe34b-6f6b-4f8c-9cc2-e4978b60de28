import request from '@/utils/request'


export function getWarnPersonalPage(data) {
    return request({
      url: '/warn/query/getWarnPersonalPage',
      method: 'get',
      params: data,
    })
  }

  export function getWarnGroupsPage(data) {
    return request({
      url: '/warn/query/getWarnGroupsPage',
      method: 'get',
      params: data,
    })
  }
  
  export function getWarnAllPage(data) {
    return request({
      url: '/warn/query/getWarnAllPage',
      method: 'get',
      params: data,
    })
  }  

  export function getWarnAllPageByUser(data) {
    return request({
      url: '/warn/query/getWarnAllPageByUser',
      method: 'get',
      params: data,
    })
  }

  export function getWarnLevelSelects(data) {
    return request({
      url: '/warn/query/getWarnLevelSelects',
      method: 'get',
      params: data,
    })
  }  

  export function getWarnTypeSelets(data) {
    return request({
      url: '/warn/query/getWarnTypeSelets',
      method: 'get',
      params: data,
    })
  }  

  export function getWarnHandleSelets(data) {
    return request({
      url: '/warn/query/getWarnHandleSelets',
      method: 'get',
      params: data,
    })
  }   

  export function updateHandleContent(data) {
    return request({
      url: '/warn/query/updateHandleContent',
      method: 'post',
      params: data,
    })
  }
  
  export function getWarnGroupSelets(data) {
    return request({
      url: '/warn/query/getWarnGroupSelets',
      method: 'get',
      params: data,
    })
  }
  