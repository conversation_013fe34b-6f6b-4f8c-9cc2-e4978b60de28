<template>
  <el-dialog title="生成成果" :visible.sync="visible" width="600px" @close="handleClose">
    <el-form ref="form" :model="formData" :rules="rules" label-width="120px">
      <el-form-item label="业务类型" prop="businessTypeId">
        <el-select v-model="formData.businessTypeId" clearable filterable placeholder="请选择业务类型" style="width: 100%">
          <el-option v-for="item in businessTypeOptions" :key="item.id" :value="item.id" :label="item.businessTypeName"/>
        </el-select>
      </el-form-item>
      <el-form-item label="成果类型" prop="resultType">
        <el-select clearable placeholder="请选择" v-model="formData.resultType">
          <el-option v-for="item in dict.type.project_outcome_types" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="项目/任务" prop="projectTaskName">
        <el-input v-model="formData.projectTaskName"  maxlength="30" show-word-limit placeholder="请输入项目/任务" clearable />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select clearable placeholder="请选择" v-model="formData.status">
          <el-option v-for="item in dict.type.project_outcome_status" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="优先级" prop="priorityLevel">
        <el-select clearable placeholder="请选择" v-model="formData.priorityLevel">
          <el-option v-for="item in dict.type.project_outcome_priority_level" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="负责项目经理" prop="projectManagers">
        <el-select multiple clearable placeholder="请选择" v-model="formData.projectManagers" style="width: 100%">
          <el-option v-for="item in dict.type.project_outcome_project_manager" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleConfirm" :loading="loading">保存</el-button>
      <el-button @click="handleClose">取消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { generateProjectResults } from "@/api/project/projectResult"
import { businessTypeList } from "@/api/project/businessType"

export default {
  name: "GenerateResultDialog",
  dicts: [
    'project_outcome_project_manager',
    'project_outcome_status',
    'project_outcome_priority_level',
    'project_outcome_types'
  ],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectedStoryIds: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      formData: {
        businessTypeId: '',
        resultType: '',
        projectTaskName: '',
        status: '',
        priorityLevel: '',
        projectManagers: []
      },
      rules: {
        businessTypeId: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
        resultType: [{ required: true, message: '请选择成果类型', trigger: 'change' }],
        projectTaskName: [{ required: true, message: '请输入项目/任务', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }],
        priorityLevel: [{ required: true, message: '请选择优先级', trigger: 'change' }],
        projectManagers: [{ required: true, message: '请选择负责项目经理', trigger: 'change' }]
      },
      // 下拉选项数据
      businessTypeOptions: []
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.resetForm()
        this.loadOptions()
      }
    }
  },
  methods: {
    // 加载下拉选项数据
    async loadOptions() {
      try {
        // 加载业务类型选项
        await businessTypeList().then(res => {
          if (res.code === 200) {
            this.businessTypeOptions = res.rows
          } else {
            this.$message.error(res.msg)
          }
        })
      } catch (error) {
        console.error('加载选项数据失败:', error)
        this.$message.error('加载选项数据失败')
      }
    },
    resetForm() {
      this.formData = {
        businessType: '',
        resultType: '',
        projectTaskName: '',
        status: '',
        priorityLevel: '',
        projectManagers: []
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    handleClose() {
      this.$emit('update:visible', false)
    },
    async handleConfirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          const submitData = { ...this.formData };
          if (Array.isArray(submitData.projectManagers) && submitData.projectManagers.length > 0) {
            submitData.projectManagers = submitData.projectManagers.join(',');
          }
          // 调用生成成果接口
          this.callGenerateApi(submitData);
        }
      })
    },

    // 调用生成成果接口
    async callGenerateApi(submitData) {
      try {
        // 将需求ID列表添加到提交数据中
        const finalData = {
          ...submitData,
          storyIdList: this.selectedStoryIds
        };
        const response = await generateProjectResults(finalData);
        if (response.code === 200) {
          this.$message.success('生成成果成功');
          this.handleClose();
          // 通知父组件刷新列表
          this.$emit('confirm', finalData);
        } else {
          this.$message.error(response.msg || '生成成果失败');
        }
      } catch (error) {
        console.error('生成成果失败:', error);
      } finally {
        this.loading = false;
      }
    },
    setLoading(loading) {
      this.loading = loading
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}
</style>
