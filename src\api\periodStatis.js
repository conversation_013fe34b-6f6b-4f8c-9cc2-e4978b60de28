import request from '@/utils/request'

// 查询时间综合统计柱状图
export function listHistogram(query) {
  return request({
    url: '/code/query/getTimePeriodStatisticsHistogram',
    method: 'get',
    timeout: 60 * 60 * 1000,
    params: query
  })
}


// 查询时间综合统计项目列表表格
export function listLeftInfoPage(query) {
    return request({
      url: '/code/query/getTimePeriodLeftStatisticsInfoPage',
      method: 'get',
      timeout: 60 * 60 * 1000,
      params: query
    })
  }


  // 查询时间综合统计项目作者分组列表表格
export function listRightInfoPage(query) {
  return request({
    url: '/code/query/getTimePeriodRightStatisticsInfoPage',
    method: 'get',
    timeout: 60 * 60 * 1000,
    params: query
  })
}