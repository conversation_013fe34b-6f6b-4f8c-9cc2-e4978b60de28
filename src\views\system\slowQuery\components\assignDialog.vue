<!-- 处理弹窗 -->
<template>
  <div>
    <el-dialog title="慢SQL问题指派" :visible.sync="visible" width="400px" @close="cancel">
      <el-form>
        <el-form-item label="指派给" prop="memberId">
          <el-select v-model="formData.memberId" placeholder="请选择处理人" style="width: 300px" filterable>
            <el-option v-for="(item, index) in processerOptions" :key="index" :value="item.id" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm" :loading="btnLoading">确认</el-button>
        <el-button @click="cancel">取消</el-button>
      </span>
      </el-dialog>
  </div>
</template>
<script>
import {
  processerList,
  sqlAssign
} from "@/api/system/slowQuery"
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    assignData: {
      type: Object
    }
  },
  data() {
    return {
      visible: this.dialogVisible,
      btnLoading: false,
      processerOptions: [], // 处理人下拉数据
      formData: {
        idList: [],
        memberId: '',
        dbNameList: []
      }
    }
  },
  watch: {
    dialogVisible (val) {
      this.visible = val
      if (this.visible) {
        this.formData = Object.assign(this.formData, this.assignData)
        this.processerList()
      }
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    // 取消
    cancel () {
      this.formData = {
        idList: [],
        memberId: ''
      }
      this.$emit('update:dialogVisible', false)
    },
    // 指派
    confirm () {
      this.btnLoading = true
      sqlAssign({
        idList: this.formData.idList,
        memberId: this.formData.memberId
      }).then((res) => {
        this.btnLoading = false
        if (res.code === 200) {
          this.$message.success('操作成功')
          this.cancel()
          this.$emit('callback')
        } else {
          this.$message.error(res.msg)
        }
      }).catch((err) => {
        this.btnLoading = false
      })
    },
    // 获取处理人列表
    processerList () {
      processerList({
        dbNameList: this.formData.dbNameList
      }).then(res => {
        if (res.code === 200) {
          this.processerOptions = res.data.map(item => {
            const { userId, nickName } = item
            return { id: userId, name: nickName }
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  },
};
</script>
<style lang="scss" scoped>
</style>
