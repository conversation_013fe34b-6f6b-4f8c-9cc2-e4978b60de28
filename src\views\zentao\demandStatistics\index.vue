<template>
  <div class="app-container">
    <el-form :model="queryForm" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="">
        <span>时间&nbsp;&nbsp;&nbsp;</span>
        <el-date-picker v-model="queryForm.beginDate" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd 00:00:00"></el-date-picker>
        <span>&nbsp;&nbsp;&nbsp;&nbsp;至&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
        <el-date-picker v-model="queryForm.endDate" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd 23:59:59"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" sizi="mini" style="width: 100%" v-loading="loading" stripe :default-sort="{prop: 'activeCount', order: 'descending'}">
      <el-table-column prop="product" label="产品名称" width="300" align="center"></el-table-column>
      <el-table-column label="需求情况" align="center">
        <el-table-column prop="draftCount" label="草稿" align="center" sortable :sort-method="(a,b)=>{return a.draftCount - b.draftCount}">
          <template slot-scope="scope">
            <el-button type="text" @click="toList(scope.row.productId, 'draft')" v-if="scope.row.draftCount !== 0">{{scope.row.draftCount}}</el-button>
            <span v-else>0</span>
          </template>
        </el-table-column>
        <el-table-column prop="activeCount" label="活跃" align="center" sortable :sort-method="(a,b)=>{return a.activeCount - b.activeCount}">
          <template slot-scope="scope">
            <el-button type="text" @click="toList(scope.row.productId, 'active')" v-if="scope.row.activeCount !== 0">{{scope.row.activeCount}}</el-button>
            <span v-else>0</span>
          </template>
        </el-table-column>
        <el-table-column prop="changeCount" label="变更" align="center" sortable :sort-method="(a,b)=>{return a.changeCount - b.changeCount}">
          <template slot-scope="scope">
            <el-button type="text" @click="toList(scope.row.productId, 'changed')" v-if="scope.row.changeCount !== 0">{{scope.row.changeCount}}</el-button>
            <span v-else>0</span>
          </template>
        </el-table-column>
        <el-table-column prop="closedCount" label="关闭" align="center" sortable :sort-method="(a,b)=>{return a.closedCount - b.closedCount}">
          <template slot-scope="scope">
            <el-button type="text" @click="toList(scope.row.productId, 'closed')" v-if="scope.row.closedCount !== 0">{{scope.row.closedCount}}</el-button>
            <span v-else>0</span>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getDemandStatistics } from '@/api/demandQuery'

export default {
  name: "DemandStatistics",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 查询参数
      queryForm: {
        beginDate: '',
        endDate: ''
      },
      tableData: []
    };
  },
  created() {
    this.initTime()
  },
  methods: {
    // 处理时间
    getFormatDate(date) {
      var month = date.getMonth() + 1
      var strDate = date.getDate()
      if (month >= 1 && month <= 9) {
        month = '0' + month
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = '0' + strDate
      }
      var currentDate = date.getFullYear() + '-' + month + '-' + strDate + ' ' + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds()
      return currentDate
    },
    initTime() {
      const endDate = this.getFormatDate(new Date()).substr(0, 11) + '23:59:59'
      const beginDate = this.getFormatDate(new Date(new Date() - 3600 * 1000 * 24 * 29)).substr(0, 11) + '00:00:00'
      this.queryForm = {
        beginDate: beginDate,
        endDate: endDate
      }
    },
    // 查询需求统计数据
    handleQuery () {
      this.loading = true
      getDemandStatistics(this.queryForm).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.tableData = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 重置
    resetQuery(){
      this.initTime()
      this.handleQuery()
    },
    // 跳转需求查询
    toList (productId, status) {
      let params = {
        beginDate: this.queryForm.beginDate,
        endDate: this.queryForm.endDate,
        productId: productId,
        status: status
      }
      this.$router.push({ name: 'Demand', query: { params: JSON.stringify(params) }})
    }
  }
};
</script>
