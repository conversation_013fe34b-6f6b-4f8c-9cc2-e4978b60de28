import request from '@/utils/request'

// type 类型,1开发、2测试、3项管、4组长、5等待任务、6进行中任务、7完成任务、8暂停任务、9文档数、10代码库数
export function getHomeTrend(type) {
  return request({
    url: '/system/homeTrend/getHomeTrend',
    method: 'get',
    params: { type }
  })
}

//时间，0本月，1上个月,不传默认本月
export function getKqAfter20(time) {
  return request({
    url: '/system/homeTrend/getKqAfter20',
    method: 'get',
    params: { time }
  })
}
