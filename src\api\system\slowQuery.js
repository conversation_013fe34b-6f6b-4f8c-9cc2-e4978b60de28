import request from '@/utils/request'

// 查询系统下拉树结构
export function systemTreeSelect() {
  return request({
    url: '/slowQuery/system/list',
    method: 'get'
  })
}

// 查询慢sql
export function slowQuerySqlPage(query) {
  return request({
    url: '/slowQuery/sql/page',
    method: 'get',
    params: query
  })
}

// 查询慢sql详细
export function slowQuerySqlDetailPage(query) {
  return request({
    url: '/slowQuery/sql/detail/page',
    method: 'get',
    params: query
  })
}

//获取各月慢sql数量统计
export function slowSqlStat(query) {
  return request({
    url: '/slowQuery/sql/stat',
    method: 'get',
    params: query
  })
}

// 调用AI分析慢SQL
export function analyzeSqlWithAI(data) {
  return request({
    url: '/slowQuery/sql/analyze',
    method: 'post',
    data: data
  })
}

// 处理SQL
export function processSql(data) {
  return request({
    url: '/slowQuery/sql/process',
    method: 'post',
    data: data
  })
}

// 获取SQL分析结果
export function getAnalyzeResult(sqlHash) {
  return request({
    url: '/slowQuery/sql/analyze/result',
    method: 'get',
    params: { sqlHash }
  })
}

// 查询处理人
export function processerList(data) {
  return request({
    url: '/slowQuery/processer/list',
    method: 'post',
    data: data
  })
}
// 指派
export function sqlAssign(data) {
  return request({
    url: '/slowQuery/sql/assign',
    method: 'post',
    data: data
  })
}
// 处理
export function sqlProcess(data) {
  return request({
    url: '/slowQuery/sql/process',
    method: 'post',
    data: data
  })
}