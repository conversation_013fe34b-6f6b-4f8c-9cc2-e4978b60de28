<template>
  <div class="chart-root">
    <!-- 添加最后更新时间 -->
    <el-alert
      :title="updateTime"
      :closable=false
      type="info">
    </el-alert>
    <div slot="header" class="chart-header">
      <el-button-group style="min-height: 40px;">
        <el-button
          v-if="tabs && tabs.length > 0"
          v-for="tab in tabs"
          :key="tab.value"
          :type="tab.value === activeTab ? 'primary' : 'default'"
          @click="switchTab(tab)"
          size="mini"
        >
          {{ tab.label }}
        </el-button>
        <!-- 占位按钮 -->
        <el-button v-else>暂无选项</el-button>
      </el-button-group>
    </div>
    <LineChart :chartData="trendList" :columns="monthList" :title="title" :ordinateName="ordinateName"/>
  </div>
</template>

<script>
import LineChart from './LineChart.vue';
import {getKqAfter20} from "../../../../../api/system/homeTrend";
export default {
  components: { LineChart },
  data() {
    return {
      activeTab: '0', // 默认选中"开发"标签
      monthList: [],
      trendList: [],
      updateTime: '',
    };
  },
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: Object,
      required: true
    },
    title: { // 新增标题的 Prop
      type: String,
      default: '' // 默认为空，如果不传递，则不会显示标题
    },
    tabs: {
      type: Array
    },
    ordinateName: { // 纵坐标名称
      type: String,
      default: '' // 默认为空，如果不传递，则不会显示
    }
  },
  created() {
    this.getKqAfter20(0);
  },
  methods: {
    switchTab(tab) {
      this.activeTab = tab.value;
      this.getKqAfter20(tab.value)
    },
    getKqAfter20(time) {
      getKqAfter20(time).then(res => {
        this.monthList = res.data.dayList;
        this.trendList = res.data.trendList;
        this.updateTime = '最后更新时间：' + res.data.updateTime;
      })
    }
  }
};
</script>

<style scoped>
.chart-root {
  width: 100%;
  height: 100%;
  min-height: 0;
  display: flex;
  flex-direction: column;
}
.chart-header {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
  margin-top: 10px;
}
</style>
