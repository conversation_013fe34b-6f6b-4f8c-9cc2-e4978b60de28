<!-- 处理弹窗 -->
<template>
  <div>
    <el-dialog title="代码质量问题处理" :visible.sync="visible" width="500px" @close="cancel">
      <el-form>
        <el-form-item label="处理状态" prop="targetStatus">
          <el-select v-model="formData.targetStatus" placeholder="请选择处理状态" style="width: 300px">
            <el-option label="已处理" value="2"></el-option>
            <el-option label="未处理" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="处理说明" prop="handleRemark">
          <el-input
            v-model="formData.handleRemark"
            type="textarea"
            placeholder="请输入处理原因"
            rows="4"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm" :loading="btnLoading">确认</el-button>
        <el-button @click="cancel">取消</el-button>
      </span>
      </el-dialog>
  </div>
</template>
<script>
import {
  scanProjectFileProcess
} from "@/api/Project"
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    processData: {
      type: Object
    }
  },
  data() {
    return {
      visible: this.dialogVisible,
      btnLoading: false,
      formData: {
        fileIds: [],
        targetStatus: '2',
        handleRemark: ''
      }
    }
  },
  watch: {
    dialogVisible (val) {
      this.visible = val
      if (this.visible) {
        this.formData = Object.assign(this.formData, this.processData)
      }
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    cancel () {
      this.formData = {
        fileIds: [],
        targetStatus: '2',
        handleRemark: ''
      }
      this.$emit('update:dialogVisible', false)
    },
    confirm () {
      this.btnLoading = true
      scanProjectFileProcess(this.formData).then((res) => {
        this.btnLoading = false
        if (res.code === 200) {
          this.$message.success('操作成功')
          this.cancel()
          this.$emit('callback')
        } else {
          this.$message.error(res.msg)
        }
      }).catch((err) => {
        this.btnLoading = false
      })
    },
  },
};
</script>
<style lang="scss" scoped>
</style>
