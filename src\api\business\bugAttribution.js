import request from '@/utils/request'

// 查询禅道Bug列表
export function listBug(query) {
  return request({
    url: '/bug/page',
    method: 'get',
    params: query
  })
}
// 根据产品统计
export function statisticByProduct(query) {
  return request({
    url: '/bug/statisticByProduct',
    method: 'get',
    params: query
  })
}

// 根据项目统计
export function statisticByProject(query) {
  return request({
    url: '/bug/statisticByProject',
    method: 'get',
    params: query
  })
}

// 根据环境统计
export function statisticByEnv(query) {
  return request({
    url: '/bug/statisticByEnv',
    method: 'get',
    params: query
  })
}


// 根据环境统计
export function statisticByType(query) {
  return request({
    url: '/bug/statisticByType',
    method: 'get',
    params: query
  })
}
// 根据类型统计
export function statisticByTypeAndOther(query) {
  return request({
    url: '/bug/statisticByTypeAndOther',
    method: 'get',
    params: query
  })
}
