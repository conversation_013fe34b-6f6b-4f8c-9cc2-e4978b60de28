import request from '@/utils/request'

// 查询值班列表
export function listOndutyUser(query) {
  return request({
    url: '/system/ondutyUser/list',
    method: 'get',
    params: query
  })
}

// 查询值班详细
export function getOndutyUser(id) {
  return request({
    url: '/system/ondutyUser/' + id,
    method: 'get'
  })
}

// 新增值班
export function addOndutyUser(data) {
  return request({
    url: '/system/ondutyUser',
    method: 'post',
    data: data
  })
}

// 修改值班
export function updateOndutyUser(data) {
  return request({
    url: '/system/ondutyUser',
    method: 'put',
    data: data
  })
}

// 删除值班
export function delOndutyUser(id) {
  return request({
    url: '/system/ondutyUser/' + id,
    method: 'delete'
  })
}
