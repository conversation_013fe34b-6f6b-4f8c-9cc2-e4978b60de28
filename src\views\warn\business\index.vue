<template>
  <div class="app-container">
    <el-form :model="queryForm" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="事件名称" prop="eventName">
        <el-input v-model="queryForm.eventName" clearable placeholder="事件名称" @keyup.enter.native="handleCurrentChange(1)" />
      </el-form-item>
      <el-form-item label="预警级别" prop="eventLevel">
        <el-select v-model="queryForm.eventLevel" clearable placeholder="预警级别">
          <el-option v-for="item in eventLevels" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryForm.status" clearable  placeholder="状态">
          <el-option v-for="item in statusList" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item label="所属应用组" prop="appGroupName" >
        <el-input v-model="queryForm.appGroupName" clearable placeholder="所属应用组" @keyup.enter.native="handleCurrentChange(1)" />
      </el-form-item>
      <el-form-item label="应用名称" prop="appName">
        <el-input v-model="queryForm.appName" clearable placeholder="应用名称" @keyup.enter.native="handleCurrentChange(1)" />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTimeRange">
        <el-date-picker
          v-model="queryForm.createTimeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          clearable
          style="width: 340px"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleCurrentChange(1)">查询</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tableData" sizi="mini" style="width: 100%" v-loading="loading" stripe ref="warnTable"
              height="calc(100vh - 280px)" border >
      <el-table-column prop="eventName" label="事件名称" align="center" />
      <el-table-column prop="eventContent" label="事件内容" align="center"  width="200" />
      <el-table-column prop="eventLevel" label="预警级别" align="center">
        <template slot-scope="scope">
          {{ getDictLabel(eventLevels,scope.row.eventLevel) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" align="center" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == 0" type="primary">{{ getDictLabel(statusList,scope.row.status) }}</el-tag>
          <el-tag v-else-if="scope.row.status == 1" type="success">{{ getDictLabel(statusList,scope.row.status) }}</el-tag>
          <el-tag v-else type="info">{{ getDictLabel(statusList,scope.row.status) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="eventType" label="事件类型" align="center">
        <template slot-scope="scope">
          {{ getDictLabel(eventTypeList,scope.row.eventType) }}
        </template>
      </el-table-column>
      <el-table-column prop="appGroupName" label="所属应用组" align="center" />
      <el-table-column prop="appName" label="应用名称" align="center" />
      <el-table-column prop="serverName" label="所属服务器名" align="center" />
      <el-table-column prop="appAddr" label="应用地址" align="center" width="150" />
      <el-table-column prop="createTime" label="创建时间" align="center"  width="180" />
      <el-table-column prop="result" label="事件原因" align="center"  width="200" >
        <template slot-scope="scope">
          <el-tooltip placement="top" >
            <template #content>
              <div class="custom-tooltip">
                {{ scope.row.result }}
              </div>
            </template>
            <span class="multi-line-text">
            {{ scope.row.result}}
            </span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="handleTime" label="处理时间" align="center"  width="180" />
      <el-table-column label="操作" align="center" width="150" >
        <template slot-scope="scope">
          <el-button size="mini" type="text" v-hasPermi="['system:monitorEventRecord:handle']"
                     @click="handle('',scope.row,1)" :disabled="scope.row.status != 0">受理</el-button>
          <el-button size="mini" type="text" v-hasPermi="['system:monitorEventRecord:handle']"
                     @click="openResultDialog(scope.row)" :disabled="scope.row.status == 2">去处理</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="margin-top: 20px;text-align: center">
      <el-pagination
        background
        :total="total"
        :current-page="queryForm.pageNum"
        :page-size="queryForm.pageSize"
        :current-page.sync="queryForm.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>

    <el-dialog title="预警问题处理" :visible.sync="dialogFormVisible" width="40%">
      <el-form :model="businessWarn" :rules="rules" ref="businessWarn">
        <el-form-item label="请填写问题原因" prop="result">
          <el-input v-model="businessWarn.result" autocomplete="off" type="textarea" :rows="4"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="handle('businessWarn',businessWarn,2)">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getMonitorEventPage,handleMonitorEvent } from "@/api/warn/business"
import { listDatas } from "@/api/system/dict/data";

export default {
  name: "businessWarn",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      total: 0,
      dialogFormVisible: false,
      // 查询参数
      queryForm: {
        pageNum: 1,
        pageSize: 20,
        eventName: '',
        eventLevel: null,
        status: null,
        appGroupName: '',
        appName: '',
        adminQueryFlag: '1',
        createTimeRange: [],
        createTimeBegin: '',
        createTimeEnd: ''
      },
      // 事件类型
      eventTypeList: [],
      // 预警级别
      eventLevels: [],
      // 状态
      statusList: [],
      tableData: [],
      businessWarn: {},
      rules: {
        result: [
          { required: true, message: '请输入原因', trigger: 'blur' }
        ]
      }
    };
  },
  async created() {
    await this.loadSelectData()
    this.handleCurrentChange(1);
  },
  methods: {
    //加载下拉框数据
     loadSelectData() {
       listDatas({ dictType: 'event_type' }).then(res => {
        if (res.code === 200) {
          this.eventTypeList = res.data
        }
      })
       listDatas({ dictType: 'event_level' }).then(res => {
        if (res.code === 200) {
          this.eventLevels = res.data
        }
      })
       listDatas({ dictType: 'handle_status' }).then(res => {
        if (res.code === 200) {
          this.statusList = res.data
        }
      })
    },
    handleQuery () {
      this.loading = true
      if (this.queryForm.createTimeRange && this.queryForm.createTimeRange.length === 2) {
        this.queryForm.createTimeBegin = this.queryForm.createTimeRange[0];
        this.queryForm.createTimeEnd = this.queryForm.createTimeRange[1];
      } else {
        this.queryForm.createTimeBegin = '';
        this.queryForm.createTimeEnd = '';
      }
      getMonitorEventPage(this.queryForm).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.total = res.total
          this.tableData = res.rows
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 改变每页显示数量
    handleSizeChange(val) {
      this.queryForm.pageSize = val
      this.handleQuery()
    },
    // 改变当前页码
    handleCurrentChange(val) {
      this.queryForm.pageNum = val
      this.handleQuery()
    },
    // 重置
    resetQuery (){
      this.$refs.queryForm.resetFields();
      this.queryForm.createTimeRange = [];
      this.queryForm.createTimeBegin = '';
      this.queryForm.createTimeEnd = '';
      this.handleCurrentChange(1)
    },
    getDictLabel(dictList,value) {
      return dictList.find(item => item.dictValue == value).dictLabel
    },
    // 打开去处理 原因框
    openResultDialog(businessWarn){
      this.businessWarn = businessWarn;
      this.businessWarn.result = '';
      this.dialogFormVisible = true;
    },
    //受理 或 去处理
    handle(formName, businessWarn, status){
      businessWarn.eventContent = '';
      const originalStatus = businessWarn.status;
      const originalResult = businessWarn.originalResult;
      this.businessWarn = businessWarn;
      if (status == 1) {
        this.$confirm('是否受理该预警？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.businessWarn.status = status;
          this.submitHandle(this.businessWarn,originalStatus,originalResult)
        }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
        })
      } else if (status == 2) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            this.businessWarn.status = status;
            this.submitHandle(this.businessWarn,originalStatus,originalResult)
          } else {
            return false;
          }
        })
      }
    },
    submitHandle(businessWarn,originalStatus,originalResult) {
      handleMonitorEvent(businessWarn).then(res => {
        if (res.code === 200) {
          this.$message.success(res.msg)
        }
        this.handleQuery()
      }).catch(error => {
        businessWarn.status = originalStatus;
        businessWarn.result = originalResult;
      });
      this.dialogFormVisible = false;
    }

  }
};
</script>
<style scoped lang="scss">
.custom-tooltip {
  max-width: 400px;
  white-space: normal;
  word-wrap: break-word;
  line-height: 1.5;
}
.multi-line-text {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
}
</style>
