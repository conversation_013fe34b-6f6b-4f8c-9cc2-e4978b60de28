<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'
import resize from '../mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '450px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: Object,
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    xAxis: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el)
      this.setOptions(this.chartData)
    },
    setOptions({ 5: data5, 6: data6, 7: data7, 11: data11, 8: data8, 12: data12 } = {}) {
      const series = []
      const names = ['需求中', '进行中', '已完成', '已关闭', '已暂停', '已取消']
      
      // 计算每个月份的总和
      const monthTotals = this.xAxis.map((_, index) => {
        return (data5?.[index] || 0) + (data6?.[index] || 0) + (data7?.[index] || 0) +
               (data11?.[index] || 0) + (data8?.[index] || 0) + (data12?.[index] || 0)
      })
      
      // 为每种类型创建堆叠系列
      const dataArray = [data5, data6, data7, data11, data8, data12]
      dataArray.forEach((data, index) => {
        series.push({
          name: names[index],
          type: 'bar',
          stack: 'total',
          emphasis: {
            focus: 'series'
          },
          data: data || [],
          label: {
            show: true,
            position: 'inside',
            formatter: function(params) {
              return params.value || ''
            }
          }
        })
      })

      this.chart.setOption({
        title: {
          text: this.title,
          left: 'center',
          top: 10,
          textStyle: {
            color: '#000000',
            fontSize: 18,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: names,
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.xAxis,
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: {
          type: 'value',
          name: '任务数'
        },
        series: series
      })
    }
  }
}
</script>