<template>
  <div class="dashboard-editor-container">
    <el-row :gutter="32" class="top-row">
      <el-col :xs="36" :sm="36" :lg="12">
        <div class="chart-wrapper">
          <stacked-chart title="团队人数趋势图" />
        </div>
      </el-col>
      <el-col :xs="36" :sm="36" :lg="12">
        <div class="chart-wrapper">
          <stacked-bar-chart title="全年任务趋势图"/>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="32" class="bottom-row">
      <el-col :xs="24" :sm="24" :lg="8">
        <div class="chart-wrapper">
          <attendance-after20 :tabs="attendanceTabs" :chart-data="teamNumDatas" :columns="docColumns" title="20点后考勤人数统计图" ordinateName="人数"/>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="8">
        <div class="chart-wrapper">
          <NoShadowLineChart :chart-data="docDatas" :columns="docColumns" :update-time="docUpdateTime" title="新增文档趋势图" ordinateName="文档数数"/>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="8">
        <div class="chart-wrapper">
          <NoShadowLineChart :chart-data="codeLibDatas" :columns="codeLibColumns" :update-time="codeLibUpdateTime" title="新增代码库趋势图" ordinateName="代码库"/>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import LineChart from './components/LineChartWithTab/LineChart.vue'
import LineChartWithTab from './components/LineChartWithTab'
import NoShadowLineChart from './components/NoShadowLineChart'
import RaddarChart from './components/RaddarChart'
import PieChart from './components/PieChart'
import BarChartWithTab from './components/BarChartWithTab'
import StackedChart from './components/StackedChart'
import StackedBarChart from './components/StackedBarChart'
import {getHomeTrend} from "../../../api/system/homeTrend";
import AttendanceAfter20 from "./components/AttendanceAfter20/index.vue";

const lineChartData = {
  newVisitis: {
    expectedData: [100, 120, 161, 134, 105, 160, 165,100, 120, 161, 134, 105 ]
  },
  messages: {
    expectedData: [200, 192, 120, 144, 160, 130, 140],
    actualData: [180, 160, 151, 106, 145, 150, 130]
  },
  purchases: {
    expectedData: [80, 100, 121, 104, 105, 90, 100],
    actualData: [120, 90, 100, 138, 142, 130, 130]
  },
  shoppings: {
    expectedData: [130, 140, 141, 142, 145, 150, 160],
    actualData: [120, 82, 91, 154, 162, 140, 130]
  }
}

export default {
  name: 'DashboardAdmin',
  components: {
    LineChart,
    LineChartWithTab,
    NoShadowLineChart,
    RaddarChart,
    PieChart,
    BarChartWithTab,
    AttendanceAfter20,
    StackedChart,
    StackedBarChart,
  },
  data() {
    return {
      lineChartData: lineChartData.newVisitis.expectedData,
      teamNumDatas: {
        first: [30, 50, 55, 80, 45, 90, 123, 133, 132, 120, 130, 135],
        test: [20, 30, 45, 60, 50, 70, 80, 90, 100, 110, 120, 130],
        project: [40, 60, 70, 85, 60, 75, 95, 100, 120, 125, 140, 150],
        leader: [10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120],
        last: [10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120]
      },
      teamNumTabs: [
        { label: '开发', value: '1' },
        { label: '测试', value: '2' },
        { label: '项管', value: '3' },
        { label: '组长', value: '4' }
      ],
      attendanceTabs: [
        { label: '本月', value: '0' },
        { label: '上月', value: '1' },
      ],
      tasksTabs: [
        { label: '需求中', value: '5' },
        { label: '进行中', value: '6' },
        { label: '已完成', value: '7' },
        { label: '已关闭', value: '11' },
        { label: '已暂停', value: '8' },
        { label: '已取消', value: '12' },
      ],
      tasksDatas: {
        first: [30, 50, 55, 80, 45, 90, 123, 133, 132, 120, 130, 135],
        ongoing: [20, 30, 45, 60, 50, 70, 80, 90, 100, 110, 120, 130],
        down: [40, 60, 70, 85, 60, 75, 95, 100, 120, 125, 140, 150],
        paused: [10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120]
      },
      docColumns:['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月'],
      docDatas: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月'],
      codeLibColumns:['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月'],
      codeLibDatas:['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月'],
      docUpdateTime:'',
      codeLibUpdateTime:'',
  }
  },
  created() {
    this.getTrend()
  },
  methods: {
    handleSetLineChartData(type) {
      this.lineChartData = lineChartData[type]
    },
    getTrend() {
      getHomeTrend(9).then(res => {
        console.log(res)
        this.docColumns = res.data.monthList;
        this.docDatas = res.data.trendList;
        this.docUpdateTime ='最后更新时间：' + res.data.updateTime;
      })
      getHomeTrend(10).then(res => {
        this.codeLibColumns = res.data.monthList;
        this.codeLibDatas = res.data.trendList;
        this.codeLibUpdateTime ='最后更新时间：' + res.data.updateTime;
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  padding: 2px;
  background-color: rgb(240, 242, 245);
  position: relative;
  height: calc(100vh - 84px);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .top-row,
  .bottom-row {
    flex: 1 1 0;
    min-height: 0;
    margin: 0 !important;
  }

  .el-row {
    height: 100% !important;
    margin: 0 !important;
    flex: 1 1 0;
  }

  .el-col {
    height: 100% !important;
    padding: 0 !important;
    display: flex;
    flex-direction: column;
  }

  .chart-wrapper {
    background: #fff;
    padding: 8px;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    margin: 8px 4px;
    flex: 1 1 0;
    min-height: 0;
  }

  .chart-wrapper > * {
    flex: 1 1 0;
    min-height: 0;
    height: 100%;
  }
}

@media (max-width:2048px) {
  .chart-wrapper {
    padding: 8px;
  }
}
</style>
