<template>
  <div class="app-container">
    <el-table :data="tableData" sizi="mini" style="width: 100%" height="700" v-loading="loading" stripe :default-sort="{prop: 'activeCount', order: 'descending'}">
      <el-table-column type="index" label="#" width="50"></el-table-column>
      <el-table-column prop="pId" label="项目id" align="center"></el-table-column>
      <el-table-column prop="scanBeginLine" label="开始行 " align="center" width="60"></el-table-column>
      <el-table-column prop="scanEndLine" label="结束行" align="center" width="60"></el-table-column>
      <el-table-column prop="scanBeginColumn" label="开始列" align="center" width="60"></el-table-column>
      <el-table-column prop="scanEndColumn" label="结束列" align="center" width="60"></el-table-column>
      <el-table-column prop="scanVersion" label="扫描版本号" align="center" width="60"></el-table-column>
      <el-table-column prop="scanRule" label="扫描规则" align="center" min-width="100"></el-table-column>
      <el-table-column prop="scanRuleSet" label="扫描规则集" align="center"></el-table-column>
      <el-table-column prop="scanClass" label="扫描类" align="center"></el-table-column>
      <el-table-column prop="scanPriority" label="优先级" align="center" width="60"></el-table-column>
      <el-table-column prop="scanMethod" label="扫描的方法" align="center"></el-table-column>
      <el-table-column prop="scanVariable" label="扫描的变量" align="center"></el-table-column>
      <el-table-column prop="scanPackage" label="扫描的包路径" align="center" min-width="100"></el-table-column>
      <el-table-column prop="scanDescribe" label="扫描问题描述" align="center" min-width="110"></el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center"></el-table-column>
      <el-table-column prop="updateTime" label="更新时间" align="center"></el-table-column>
    </el-table>
    <div class="fl mt10">
      <el-pagination
        background
        layout="prev, pager, next, slot, jumper, sizes, total"
        :total="total"
        :page-size="pageSize"
        :current-page.sync="pageNum"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { scanProjectDetailList } from "@/api/Project"
export default {
  props: ['fileId'],
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      pageNum: 1,
      pageSize: 50
    }
  },
  watch: {
  },
  created() {
  },
  methods: {
    // 查询报告数据
    getData () {
      this.loading = true
      scanProjectDetailList({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        fileId: this.fileId
      }).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.tableData = res.rows
          this.total = res.total
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 改变每页显示数量
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    // 改变当前页码
    handleCurrentChange(val) {
      this.pageNum = val
      this.getData()
    }
  }
}
</script>
<style lang="scss" scoped>
</style>