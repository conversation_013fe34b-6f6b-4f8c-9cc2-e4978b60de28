<!--月度部门合作访谈-->
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="年份" prop="year">
        <el-select v-model="queryParams.year" placeholder="选择年份" clearable>
          <el-option
            v-for="year in yearOptions"
            :key="year"
            :label="year"
            :value="year">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="月份" prop="month">
        <el-select v-model="queryParams.month" placeholder="选择月份" clearable>
          <el-option
            v-for="dict in dict.type.month"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="部门" prop="deptId">
        <el-select v-model="queryParams.deptId" placeholder="选择部门" clearable>
          <el-option
            v-for="dept in deptOptions"
            :key="dept.deptId"
            :label="dept.deptName"
            :value="dept.deptId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="访谈状态" prop="interviewStatus">
        <el-select v-model="queryParams.interviewStatus" placeholder="选择访谈状态" clearable>
          <el-option
            v-for="dict in dict.type.interview_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="访谈人" prop="interviewer">
        <el-input v-model="queryParams.interviewer" placeholder="请输入访谈人" clearable />
      </el-form-item>
      <el-form-item label="被访谈人" prop="interviewee">
        <el-input v-model="queryParams.interviewee" placeholder="请输入被访谈人" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['process:monthlyDepartmentCooperationInterview:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['process:monthlyDepartmentCooperationInterview:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['process:monthlyDepartmentCooperationInterview:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['process:monthlyDepartmentCooperationInterview:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="interviewList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="访谈编号" align="center" prop="interviewId" />
      <el-table-column label="年份" align="center" prop="year" />
      <el-table-column label="月份" align="center" prop="month">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.month" :value="scope.row.month"/>
        </template>
      </el-table-column>
      <el-table-column label="部门" align="center" prop="deptName" />
      <el-table-column label="访谈主题" align="center" prop="interviewTopic" :show-overflow-tooltip="true" />
      <el-table-column label="访谈人" align="center" prop="interviewer" />
      <el-table-column label="被访谈人" align="center" prop="interviewee" />
      <el-table-column label="访谈时间" align="center" prop="interviewDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.interviewDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="访谈状态" align="center" prop="interviewStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.interview_status" :value="scope.row.interviewStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['process:monthlyDepartmentCooperationInterview:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['process:monthlyDepartmentCooperationInterview:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['process:monthlyDepartmentCooperationInterview:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改月度部门合作访谈对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="年份" prop="year">
              <el-select v-model="form.year" placeholder="请选择年份">
                <el-option
                  v-for="year in yearOptions"
                  :key="year"
                  :label="year"
                  :value="year">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="月份" prop="month">
              <el-select v-model="form.month" placeholder="请选择月份">
                <el-option
                  v-for="dict in dict.type.month"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="部门" prop="deptId">
              <el-select v-model="form.deptId" placeholder="请选择部门">
                <el-option
                  v-for="dept in deptOptions"
                  :key="dept.deptId"
                  :label="dept.deptName"
                  :value="dept.deptId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="访谈状态" prop="interviewStatus">
              <el-select v-model="form.interviewStatus" placeholder="请选择访谈状态">
                <el-option
                  v-for="dict in dict.type.interview_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="访谈主题" prop="interviewTopic">
          <el-input v-model="form.interviewTopic" placeholder="请输入访谈主题" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="访谈人" prop="interviewer">
              <el-input v-model="form.interviewer" placeholder="请输入访谈人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="被访谈人" prop="interviewee">
              <el-input v-model="form.interviewee" placeholder="请输入被访谈人" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="访谈时间" prop="interviewDate">
          <el-date-picker
            v-model="form.interviewDate"
            type="date"
            placeholder="选择访谈时间"
            value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="访谈内容" prop="interviewContent">
          <el-input v-model="form.interviewContent" type="textarea" :rows="4" placeholder="请输入访谈内容" />
        </el-form-item>
        <el-form-item label="访谈结果" prop="interviewResult">
          <el-input v-model="form.interviewResult" type="textarea" :rows="3" placeholder="请输入访谈结果" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog title="访谈详情" :visible.sync="viewOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="访谈编号">{{ viewForm.interviewId }}</el-descriptions-item>
        <el-descriptions-item label="年份">{{ viewForm.year }}</el-descriptions-item>
        <el-descriptions-item label="月份">
          <dict-tag :options="dict.type.month" :value="viewForm.month"/>
        </el-descriptions-item>
        <el-descriptions-item label="部门">{{ viewForm.deptName }}</el-descriptions-item>
        <el-descriptions-item label="访谈主题" :span="2">{{ viewForm.interviewTopic }}</el-descriptions-item>
        <el-descriptions-item label="访谈人">{{ viewForm.interviewer }}</el-descriptions-item>
        <el-descriptions-item label="被访谈人">{{ viewForm.interviewee }}</el-descriptions-item>
        <el-descriptions-item label="访谈时间">{{ parseTime(viewForm.interviewDate, '{y}-{m}-{d}') }}</el-descriptions-item>
        <el-descriptions-item label="访谈状态">
          <dict-tag :options="dict.type.interview_status" :value="viewForm.interviewStatus"/>
        </el-descriptions-item>
        <el-descriptions-item label="访谈内容" :span="2">{{ viewForm.interviewContent }}</el-descriptions-item>
        <el-descriptions-item label="访谈结果" :span="2">{{ viewForm.interviewResult }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewForm.remark }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(viewForm.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(viewForm.updateTime) }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getInterviewList, 
  getInterview, 
  delInterview, 
  addInterview, 
  updateInterview,
  exportInterview
} from "@/api/process/monthlyDepartmentCooperationInterview";
import { deptSelect } from "@/api/commonBiz";

export default {
  name: "MonthlyDepartmentCooperationInterview",
  dicts: ['month', 'interview_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 月度部门合作访谈表格数据
      interviewList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看详情弹出层
      viewOpen: false,
      // 年份选项
      yearOptions: [],
      // 部门选项
      deptOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        year: null,
        month: null,
        deptId: null,
        interviewStatus: null,
        interviewer: null,
        interviewee: null
      },
      // 表单参数
      form: {},
      // 查看表单参数
      viewForm: {},
      // 表单校验
      rules: {
        year: [
          { required: true, message: "年份不能为空", trigger: "change" }
        ],
        month: [
          { required: true, message: "月份不能为空", trigger: "change" }
        ],
        deptId: [
          { required: true, message: "部门不能为空", trigger: "change" }
        ],
        interviewTopic: [
          { required: true, message: "访谈主题不能为空", trigger: "blur" }
        ],
        interviewer: [
          { required: true, message: "访谈人不能为空", trigger: "blur" }
        ],
        interviewee: [
          { required: true, message: "被访谈人不能为空", trigger: "blur" }
        ],
        interviewDate: [
          { required: true, message: "访谈时间不能为空", trigger: "change" }
        ],
        interviewStatus: [
          { required: true, message: "访谈状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.initYearOptions();
    this.getDeptList();
    this.getList();
  },
  methods: {
    /** 初始化年份选项 */
    initYearOptions() {
      const currentYear = new Date().getFullYear();
      for (let i = 0; i < 5; i++) {
        this.yearOptions.push(currentYear - i);
      }
    },
    /** 查询部门列表 */
    getDeptList() {
      deptSelect().then(response => {
        this.deptOptions = response.data;
      });
    },
    /** 查询月度部门合作访谈列表 */
    getList() {
      this.loading = true;
      getInterviewList(this.queryParams).then(response => {
        this.interviewList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        interviewId: null,
        year: null,
        month: null,
        deptId: null,
        deptName: null,
        interviewTopic: null,
        interviewer: null,
        interviewee: null,
        interviewDate: null,
        interviewContent: null,
        interviewResult: null,
        interviewStatus: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.interviewId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加月度部门合作访谈";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const interviewId = row.interviewId || this.ids
      getInterview(interviewId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改月度部门合作访谈";
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      getInterview(row.interviewId).then(response => {
        this.viewForm = response.data;
        this.viewOpen = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.interviewId != null) {
            updateInterview(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInterview(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const interviewIds = row.interviewId || this.ids;
      this.$modal.confirm('是否确认删除月度部门合作访谈编号为"' + interviewIds + '"的数据项？').then(function() {
        return delInterview(interviewIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('process/monthlyDepartmentCooperationInterview/export', {
        ...this.queryParams
      }, `月度部门合作访谈_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
