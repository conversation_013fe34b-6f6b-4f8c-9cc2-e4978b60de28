<template>
  <div class="app-container">
    <el-form :model="queryForm" ref="queryForm" size="small" :inline="true" label-width="100px">
      <el-form-item label="代码库ID" prop="pId">
        <el-input v-model="queryForm.pId" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="代码库名称" prop="scanName">
        <el-input v-model="queryForm.scanName" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="负责开发组" prop="devDept">
        <el-select v-model="queryForm.devDept" clearable>
          <el-option v-for="item in dict.type.code_dev_dept"
            :key="item.value"
            :value="item.value"
            :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="最新扫描时间">
        <el-date-picker v-model="queryForm.lastScanTimeStart" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd 00:00:00"></el-date-picker>
        <span>&nbsp;&nbsp;&nbsp;&nbsp;至&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
        <el-date-picker v-model="queryForm.lastScanTimeEnd" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd 23:59:59"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" sizi="mini" style="width: 100%" height="calc(100vh - 260px)" v-loading="loading" stripe border ref="codeTable"
      @sort-change="handleSortChange" :default-sort="{prop: 'lastScanTime', order: 'descending'}">
      <el-table-column prop="pId" label="代码库ID" align="center"></el-table-column>
      <el-table-column prop="scanName" label="代码库名称" align="center">
        <template slot-scope="scope">
          <a :href="scope.row.webUrl" target="_blank">{{ scope.row.scanName }}</a>
        </template>
      </el-table-column>
      <el-table-column prop="scanDescribe" label="代码库描述" align="center"></el-table-column>
      <el-table-column prop="lastCommitTime" label="最后提交时间" align="center" sortable="custom"></el-table-column>
      <el-table-column label="负责开发组" align="center">
        <template slot-scope="scope">
          {{ getDevDeptName(scope.row.devDept) }}
        </template>
      </el-table-column>
      <el-table-column prop="lastScanTime" label="最新扫描时间" align="center" sortable="custom"></el-table-column>
      <el-table-column prop="blockerAmount" align="center" sortable="custom">
        <template slot="header">
          严重问题<br>（master分支）
        </template>
      </el-table-column>
      <el-table-column prop="criticalAmount" align="center" sortable="custom">
        <template slot="header">
          一般问题<br>（master分支）
        </template>
      </el-table-column>
      <el-table-column prop="unassignedAmount" label="未指派条数" align="center" sortable="custom"></el-table-column>
      <el-table-column prop="unhandledAmount" label="已指派未处理条数" align="center" sortable="custom"></el-table-column>
      <el-table-column prop="handledAmount" label="已指派已处理条数" align="center" sortable="custom"></el-table-column>
      <el-table-column label="问题明细" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="toReportUrl(scope.row)">问题分配及处理</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="fl mt10">
      <el-pagination
        background
        layout="prev, pager, next, slot, jumper, sizes, total"
        :total="total"
        :page-size="queryForm.pageSize"
        :current-page.sync="queryForm.pageNum"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { scanProjectList } from "@/api/Project"
export default {
  name: "Quality",
  dicts: ['code_dev_dept'],
  data() {
    return {
      // 遮罩层
      loading: false,
      total: 0,
      // 查询参数
      queryForm: {
        pageNum: 1,
        pageSize: 50,
        orderByField: 'lastScanTime',
        orderRule: 'desc',
        pId: '',
        scanName: '',
        devDept: '',
        lastScanTimeStart: '',
        lastScanTimeEnd: ''
      },
      tableData: []
    };
  },
  computed: {
  },
  async created() {
    this.initTime()
  },
  methods: {
    // 处理时间
    getFormatDate(date) {
      var month = date.getMonth() + 1
      var strDate = date.getDate()
      if (month >= 1 && month <= 9) {
        month = '0' + month
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = '0' + strDate
      }
      var currentDate = date.getFullYear() + '-' + month + '-' + strDate + ' ' + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds()
      return currentDate
    },
    initTime() {
      const lastScanTimeEnd = this.getFormatDate(new Date()).substr(0, 11) + '23:59:59'
      const lastScanTimeStart = this.getFormatDate(new Date(new Date() - 3600 * 1000 * 24 * 29)).substr(0, 11) + '00:00:00'
      this.queryForm.lastScanTimeStart = lastScanTimeStart
      this.queryForm.lastScanTimeEnd = lastScanTimeEnd
    },
    // 排序
    handleSortChange (data) {
      this.queryForm.orderByField = data.prop
      if (data.order === 'ascending') {
        this.queryForm.orderRule = 'asc'
      } else if (data.order === 'descending') {
        this.queryForm.orderRule = 'desc'
      } else {
        this.queryForm.orderRule = ''
      }
      this.handleCurrentChange(1)
    },
    // 查询代码质量管理数据
    handleQuery () {
      this.loading = true
      scanProjectList(this.queryForm).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.total = res.total
          this.tableData = res.rows
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 改变每页显示数量
    handleSizeChange(val) {
      this.queryForm.pageSize = val
      this.handleQuery()
    },
    // 改变当前页码
    handleCurrentChange(val) {
      this.queryForm.pageNum = val
      this.handleQuery()
    },
    // 重置
    resetQuery (){
      this.queryForm = {
        pageNum: 1,
        pageSize: 50,
        orderByField: 'lastScanTime',
        orderRule: 'desc',
        pId: '',
        scanName: '',
        devDept: '',
        lastScanTimeStart: '',
        lastScanTimeEnd: ''
      }
      this.initTime()
      // 设置排序会触发 handleSortChange 方法
      this.$refs.codeTable.sort('lastScanTime', 'descending')
    },
    // 跳转报告地址
    toReportUrl (row) {
      this.$router.push({ path: '/code/qualityDetail', query: { pId: row.pId, scanName: row.scanName }})
    },
    getDevDeptName (value) {
      const item = this.dict.type.code_dev_dept.find(dept => dept.value === value)
      return item ? item.label : value
    }
  }
};
</script>
