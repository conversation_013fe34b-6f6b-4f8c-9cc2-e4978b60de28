<template>
    <div class="app-container">
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="按项目统计bug数量" name="first">
                <by-project></by-project>
            </el-tab-pane>
            <el-tab-pane label="按产品统计bug数量" name="third">
                <by-product></by-product>
            </el-tab-pane>
            <el-tab-pane label="按bug类型统计bug数量" name="fourth">
                <by-type></by-type>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
<script>
import ByProject from './ByProject';
import ByProduct from './ByProduct';
import ByCount from './ByCount';
import ByType from './ByType';

export default {
    components: {
        ByProject,
        ByProduct,
        ByCount,
        ByType
    },
    name: 'BugStatistics',  
    data() {
        return {
            activeName: 'first'
        };
    },
    methods: {
        handleClick(tab, event) {
            console.log(tab, event);
        }
    }
};
</script>