import request from '@/utils/request'

// 一类二类指标下拉数据
export function getIndicatorList(query) {
  return request({
    url: '/performIndicatorResult/indicatorList',
    method: 'get',
    params: query
  })
}
// 查询绩效辅导列表
export function getPerformanceTutoringList(query) {
  return request({
    url: '/system/performanceTutoring/list',
    method: 'get',
    params: query
  })
}
// 绩效建议
export function suggestPerformanceTutoring(data) {
  return request({
    url: '/system/performanceTutoring/suggest',
    method: 'post',
    data: data
  })
}
// 获取绩效辅导最后编辑辅导人
export function getLastEditTotur(query) {
  return request({
    url: '/system/performanceTutoring/getLastEditTotur',
    method: 'get',
    params: query
  })
}
// 绩效辅导登记
export function tutoringPerformanceTutoring(data) {
  return request({
    url: '/system/performanceTutoring/tutoring',
    method: 'post',
    data: data
  })
}
// 文件上传
export function upload(data) {
  return request({
    url: '/system/files/upload',
    method: 'post',
    data: data
  })
}
// 多文件上传
export function uploadBatch(data) {
  return request({
    url: '/system/files/uploadBatch',
    method: 'post',
    data: data
  })
}
// 文件下载
export function downloadFile (params) {
  return `/imp-admin/${params.tutoringAttachment}`
}
// 下载多个文件（压缩包形式）
export function downloadBatch (params) {
  // console.log('params.filePaths', params.filePaths)
  // return `/imp-admin/system/files/downloadBatch?filePaths=${params.filePaths}`
  return postBlob(`/imp-admin/system/files/downloadBatch`, params)
}


// 文件删除
export function delFiles(filePath) {
  return request({
    url: '/system/files/delete?filePath=' + filePath,
    method: 'delete'
  })
}

