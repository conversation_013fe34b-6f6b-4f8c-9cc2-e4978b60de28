<template>
  <div class="app-container">
    <el-form :model="queryForm" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item label="时间">
        <el-date-picker v-model="queryForm.originatorTimeStart" type="date" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd 00:00:00"></el-date-picker>
        <span>&nbsp;&nbsp;&nbsp;&nbsp;至&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
        <el-date-picker v-model="queryForm.originatorTimeEnd" type="date" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd 23:59:59"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-tag type="info">最后更新时间：{{this.lastUpdateTime}}</el-tag>
      </el-form-item>
    </el-form>

    <el-table  style="width: 100%" v-loading="loading" :data="tableData" sizi="mini"
               :default-sort="{ prop: 'total_total', order: 'descending' }" border ref="table"
               height="calc(100vh - 180px)" :cell-style="changeCellStyle">
      <el-table-column prop="deptName" label="事项" width="150" fixed />
      <el-table-column prop="total_total" label="总数" width="200" align="center" fixed  sortable>
        <template slot-scope="scope">
          <div>
            <span>创建：{{ getprocessStatCount(scope.row.processStatCountList, 'total', 'createTotal') }} </span>
            <span style="margin-left: 10px">参与：{{ getprocessStatCount(scope.row.processStatCountList, 'total', 'participateTotal') }}</span>
          </div>
          <div>总量：{{ getprocessStatCount(scope.row.processStatCountList, 'total', 'total') }} </div>
        </template>
      </el-table-column>
      <el-table-column
        v-for="item in processList"
        :key="item.processCode"
        :label="item.processName"
        width="250"
        align="center"
        sortable
        :prop="`${item.processCode}_total`">
        <template slot-scope="scope">
          <div>创建： {{ getprocessStatCount(scope.row.processStatCountList, item.processCode, 'createTotal') }} </div>
          <div>参与： {{ getprocessStatCount(scope.row.processStatCountList, item.processCode, 'participateTotal') }} </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getProcessStatList } from '@/api/process/processStat'

export default {
  name: 'processStat',
  data() {
    return {
      queryForm: {
        originatorTimeStart: '',
        originatorTimeEnd: '',
      },
      tableData: [],
      loading: false,
      lastUpdateTime: '',
      // 流程列表
      processList: [
        // {processName: '总数', processCode: 'total'},
        {processName: '内部管理系统功能需求', processCode: 'PROC-BFED446A-BCC0-486E-89AD-500FC8C207A4'},
        {processName: '风控规则开发需求', processCode: 'PROC-041C6C2A-93AC-495A-B1AE-27A1FB665160'},
        {processName: '运营看板及报表需求', processCode: 'PROC-7E506C58-6D56-450D-AEBE-D572BF0C4E9C'},
        {processName: '内部管理系统发布', processCode: 'PROC-BC1EC039-13B7-4518-A893-EC72AA7A09FD'},
        {processName: '发布移动端应用', processCode: 'PROC-1E853673-4134-4BA6-8AEF-9EAF6FA6B971'},
        {processName: '系统功能异常报障处理', processCode: 'PROC-2B19F911-6BBA-4636-80C6-0B6E7AEC6936'},
        {processName: '生产维护SQL执行', processCode: 'PROC-A0B35093-1C77-4816-8631-AAB4F10D5A73'},
        {processName: '催收财务运营生产数据修复', processCode: 'PROC-4DA49620-0254-4F20-912B-7C7A8C8281E9'},
        {processName: '信息系统帐号及权限申请', processCode: 'PROC-8227A6EB-2EF2-47F7-8C54-1CDD3BA50854'},
        {processName: '运维技术协助申请', processCode: 'PROC-E2221578-24E3-4EC7-A06C-6DC08FC31928'},
        {processName: '业务敏感信息导出申请', processCode: 'PROC-6386FE1F-B64E-466E-B38F-1B32E327C10D'},
        {processName: '生产数据库查询权限申请', processCode: 'PROC-46B593B4-61E9-426E-BABF-5B8488CE897C'},
        {processName: '设备借用流程', processCode: 'PROC-31E92A1C-277C-40DD-9B9D-9D591A1AF812'},
        {processName: '生产应用每周巡检流程', processCode: 'PROC-CDD873FA-2CFF-418C-A8CB-2CD4D6D3190B'},
        {processName: '敏感用户数据加解密', processCode: 'PROC-4BBBBBD4-0DFE-490B-A219-CA78E4C2127D'},
        {processName: '运维&DBA&BI生产环境计划性维护操作申请', processCode: 'PROC-5709F938-BBBC-4C89-A00D-EACC8CBDA442'},
      ]
    }
  },
  created() {
    this.initTime()
  },
  methods: {
    handleQuery() {
      this.loading = true
      getProcessStatList(this.queryForm).then(response => {
        this.loading = false
        if (response.code === 200) {
          this.lastUpdateTime = response.data.lastUpdateTime
          this.tableData = response.data.processStatListVoList.map(item => {
            const processedItem = { ...item };
            const processTotalList = [{processName: '总数', processCode: 'total'}]
            processTotalList.push(...this.processList)
            processTotalList.forEach(process => {
              const stat = item.processStatCountList.find(
                statItem => statItem.processCode === process.processCode
              );
              processedItem[`${process.processCode}_total`] = stat ? stat.total : 0;
            });
            return processedItem;
          });
          this.$nextTick(() => {
            this.$refs.table.sort('total_total', 'descending');
          });
        } else {
          this.$message.error(response.msg)
        }
      })
    },
    initTime() {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 1)
      this.queryForm.originatorTimeEnd = this.formatDate(end, true)
      this.queryForm.originatorTimeStart = this.formatDate(start, false)
      this.handleQuery()
    },
    formatDate(date, isEndOfDay) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      if (isEndOfDay) {
        return `${year}-${month}-${day} 23:59:59`
      } else {
        return `${year}-${month}-${day} 00:00:00`
      }
    },
    getprocessStatCount(processStatCountList,processCode,paramName) {
        const stat = processStatCountList.find(statItem => statItem.processCode === processCode);
        return  stat ? stat[paramName] : 0;
    },
    resetQuery() {
      this.queryForm.originatorTimeEnd = '';
      this.queryForm.originatorTimeStart = '';
      this.$refs.table.clearSort();
      this.handleQuery();
    },
    changeCellStyle({ row, column, rowIndex, columnIndex }) {
      const processCode = column.property.replace('_total', '');
      const createTotal = this.getprocessStatCount(row.processStatCountList, processCode, 'createTotal');
      const participateTotal = this.getprocessStatCount(row.processStatCountList, processCode, 'participateTotal');
      if (column.property != 'deptName' && createTotal === 0 && participateTotal === 0) {
        return 'background: #fff1f1'
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>

