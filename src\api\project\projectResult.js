import request from '@/utils/request'

// 查询项目成果列表
export function projectResultList(query) {
  return request({
    url: '/system/projectResult/list',
    method: 'get',
    params: query
  })
}

// 查询项目成果详细
export function projectResultDetail(id) {
  return request({
    url: '/system/projectResult/' + id,
    method: 'get'
  })
}

// 新增项目成果
export function projectResultAdd(data) {
  return request({
    url: '/system/projectResult',
    method: 'post',
    data: data
  })
}

// 修改项目成果
export function projectResultEdit(data) {
  return request({
    url: '/system/projectResult',
    method: 'put',
    data: data
  })
}

// 删除项目成果
export function projectResultDelete(id) {
  return request({
    url: '/system/projectResult/' + id,
    method: 'delete'
  })
}

// 批量删除项目成果
export function projectResultBatchDelete(ids) {
  return request({
    url: '/system/projectResult/' + ids,
    method: 'delete'
  })
}

// 同步项目数据
export function projectResultSync(data) {
  return request({
    url: '/system/projectResult/sync',
    method: 'post',
    data: data
  })
}

// 归档项目成果
export function projectResultArchive(id) {
  return request({
    url: '/system/projectResult/archive/' + id,
    method: 'put'
  })
}

// 导出项目成果
export function projectResultExport(query) {
  return request({
    url: '/system/projectResult/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取业务类型选项
export function getBusinessTypeOptions() {
  return request({
    url: '/system/businessType/list',
    method: 'get',
    params: {
      pageNum: 1,
      pageSize: 1000  // 获取所有业务类型
    }
  })
}

// 获取未开始或进行中的项目成果
export function listByDoingOrNotStart(data) {
  return request({
    url: '/system/projectResult/listByDoingOrNotStart',
    method: 'get',
    params: data
  })
}

// 生成项目成果
export function generateProjectResults(data) {
  return request({
    url: '/system/projectResult/generateResults',
    method: 'post',
    data: data
  })
}

// 加入项目成果
export function joinProjectResults(data) {
  return request({
    url: '/system/projectResult/joinResults',
    method: 'post',
    data: data
  })
}

// 取消加入项目成果
export function cannelJoinResults(storyIds) {
  return request({
    url: '/system/projectResult/cannelJoinResults',
    method: 'post',
    data: storyIds
  })
}

// 移除归档
export function removeArchive(id) {
  return request({
    url: `/system/projectResult/removeArchive/${id}`,
    method: 'post',
    data: id
  })
}

// 发送项目成果邮件
export function sendProjectResultEmail(resultIds) {
  return request({
    url: '/system/projectResult/sendEmail',
    method: 'post',
    data: resultIds
  })
}

// 根据ID列表查询项目成果
export function selectProjectResultsByIds(ids) {
  return request({
    url: '/system/projectResult/selectByIds',
    method: 'post',
    data: ids
  })
}
