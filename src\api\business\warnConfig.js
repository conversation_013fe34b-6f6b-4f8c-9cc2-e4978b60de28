import request from '@/utils/request'

// 查询预警配置列表
export function listWarnConfig(query) {
  return request({
    url: '/business/warnConfig/list',
    method: 'get',
    params: query
  })
}

// 查询预警配置详细
export function getWarnConfig(id) {
  return request({
    url: '/business/warnConfig/' + id,
    method: 'get'
  })
}

// 新增预警配置
export function addWarnConfig(data) {
  return request({
    url: '/business/warnConfig',
    method: 'post',
    data: data
  })
}

// 修改预警配置
export function updateWarnConfig(data) {
  return request({
    url: '/business/warnConfig',
    method: 'put',
    data: data
  })
}

// 修改预警配置
export function enableWarnConfig(data) {
  return request({
    url: '/business/warnConfig/enable',
    method: 'put',
    data: data
  })
}

// 删除预警配置
export function delWarnConfig(id) {
  return request({
    url: '/business/warnConfig/' + id,
    method: 'delete'
  })
}
