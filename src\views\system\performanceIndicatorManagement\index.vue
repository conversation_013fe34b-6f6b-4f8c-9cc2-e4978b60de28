<!--绩效指标管理-->
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="一类指标" prop="firstIndecator">
        <el-select v-model="queryParams.firstIndecator" clearable @change="handleIndecator(queryParams.firstIndecator)">
          <el-option
            v-for="dict in firstIndicators"
            :key="dict.code"
            :label="dict.name"
            :value="dict.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="二类指标" prop="secondIndecator">
        <el-select v-model="queryParams.secondIndecator" clearable :disabled="queryParams.firstIndecator === ''">
          <el-option
            v-for="dict in secondIndicators"
            :key="dict.code"
            :label="dict.name"
            :value="dict.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="绩效级别" prop="level">
        <el-select v-model="queryParams.level" clearable>
          <el-option
            v-for="dict in dict.type.level"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="tableData" @selection-change="handleSelectionChange" style="width: 100%;" height="calc(100vh - 270px)" stripe border>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="一类指标" align="center" prop="firstIndecatorName" width="180" />
      <el-table-column label="二类指标" align="center" prop="secondIndecatorName" width="220" />
      <el-table-column label="绩效等级" align="center" prop="level" width="150" />
      <el-table-column label="推荐原因" align="center" prop="result"></el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="150">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改弹框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="一类指标" prop="firstIndecator">
          <el-select v-model="form.firstIndecator" clearable @change="handleIndecator(form.firstIndecator)" style="width: 100%;">
            <el-option
              v-for="dict in firstIndicators"
              :key="dict.code"
              :label="dict.name"
              :value="dict.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="二类指标" prop="secondIndecator">
          <el-select v-model="form.secondIndecator" clearable :disabled="form.firstIndecator === ''" style="width: 100%;">
            <el-option
              v-for="dict in secondIndicators"
              :key="dict.code"
              :label="dict.name"
              :value="dict.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="绩效等级" prop="level">
          <el-select v-model="form.level" clearable style="width: 100%;">
            <el-option
              v-for="dict in dict.type.level"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="推荐原因" prop="result">
          <el-input v-model="form.result" type="textarea" :rows="5" />
          <p class="text-muted">请换行填写每个推荐原因</p>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getIndicatorList
} from "@/api/business/performanceGuidanceManagement";
import {
  getPerformIndicatorResultList, addPerformIndicatorResult, editPerformIndicatorResult, delPerformIndicatorResult
} from "@/api/system/performanceIndicatorManagement";
export default {
  name: "Post",
  dicts: ['level'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        firstIndecator: '', // 	一类指标
        secondIndecator: '', // 	二类指标
        level: '' // 绩效级别(S/A/B/C/D)
      },
      firstIndicators: [], // 一类指标
      secondIndicators: [], // 二类指标
      // 表单参数
      form: {
        id: '',
        firstIndecator: '',
        secondIndecator: '',
        level: '',
        result: ''
      },
      // 表单校验
      rules: {
        firstIndecator: [
          { required: true, message: "请选择一类指标", trigger: "change" }
        ],
        secondIndecator: [
          { required: true, message: "请选择二类指标", trigger: "change" }
        ],
        level: [
          { required: true, message: "请选择绩效等级", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getIndicatorList()
    this.getList()
  },
  methods: {
    /** 一类指标 */
    getIndicatorList() {
      getIndicatorList({}).then(response => {
        this.firstIndicators = response.data
      });
    },
    /** 二类指标 */
    handleIndecator(code) {
      this.queryParams.secondIndecator = ''
      this.firstIndicators.forEach((item) => {
        if (item.code === code) {
          this.secondIndicators = item.secondaryIndicators
        }
      })
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      getPerformIndicatorResultList(this.queryParams).then(response => {
        this.tableData = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: '',
        firstIndecator: '',
        secondIndecator: '',
        level: '',
        result: ''
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.title = "编辑";
      this.form = this.dataClone(row)
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id !== '') {
            editPerformIndicatorResult(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPerformIndicatorResult(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete() {
      const resultsIds = this.ids;
      this.$modal.confirm('是否确认删除绩效指标为"' + resultsIds + '"的数据项？').then(function() {
        return delPerformIndicatorResult(resultsIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    // 拷贝
    dataClone (data) {
      let obj = {}
      obj = JSON.parse(JSON.stringify(data))
      return obj
    }
  }
};
</script>
