<template>
  <div>
    <!-- 添加最后更新时间 -->
    <el-alert
      :title="updateTime"
      :closable=false
      type="info">
    </el-alert>
    <div slot="header" class="chart-header" >
      <el-button-group>
        <el-button
          v-for="tab in tabs"
          :key="tab.value"
          :type="tab.value === activeTab ? 'primary' : 'default'"
          @click="switchTab(tab)"
          size="mini"
        >
          {{ tab.label }}
        </el-button>
      </el-button-group>
    </div>
    <LineChart :chartData="trendList" :title="title" :x-axis="monthList"/>
  </div>
</template>

<script>
import LineChart from './LineChart.vue';
import { getHomeTrend } from '@/api/system/homeTrend'

export default {
  components: { LineChart },
  data() {
    return {
      activeTab: '1', // 默认选中“开发”标签
      monthList: [],
      trendList: [],
      updateTime: '',
    };
  },
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: Object,
      required: true
    },
    title: { // 新增标题的 Prop
      type: String,
      default: '' // 默认为空，如果不传递，则不会显示标题
    },
    tabs: {
      type: Array
    }
  },
  created() {
    this.getHomeTrend(1);
  },
  methods: {
    switchTab(tab) {
      this.activeTab = tab.value;
      this.getHomeTrend(tab.value);
    },
    getHomeTrend(type) {
      getHomeTrend(type).then(res => {
        console.log(res);
        this.monthList = res.data.monthList;
        this.trendList = res.data.trendList;
        this.updateTime = '最后更新时间：' + res.data.updateTime;
      })
    }
  }
};
</script>

<style scoped>
.chart-header {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
  margin-top: 10px;
}
</style>
