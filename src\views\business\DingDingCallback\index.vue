<template>
  <div class="dingding-callback">
    <el-card class="box-card">
      <div slot="header" class="clearfix card-header">
        <span class="dingding-icon">
          <svg viewBox="0 0 1024 1024" width="24" height="24"><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z" fill="#2D8CF0"/><path d="M512 128c212.1 0 384 171.9 384 384s-171.9 384-384 384S128 724.1 128 512 299.9 128 512 128m0-48C264.6 80 80 264.6 80 512s184.6 432 432 432 432-184.6 432-432S759.4 80 512 80z" fill="#2D8CF0"/><path d="M672 384c-17.7 0-32 14.3-32 32v64H384v-64c0-17.7-14.3-32-32-32s-32 14.3-32 32v192c0 17.7 14.3 32 32 32s32-14.3 32-32v-64h256v64c0 17.7 14.3 32 32 32s32-14.3 32-32V416c0-17.7-14.3-32-32-32z" fill="#fff"/></svg>
        </span>
        <span class="header-title">钉钉登录中...</span>
      </div>
      <transition name="fade">
        <div>
          <div v-if="loading" class="loading-block">
            <i class="el-icon-loading loading-icon"></i>
            <span class="loading-text">正在登录，请稍候...</span>
          </div>
          <div v-else-if="error" class="error-block">
            <i class="el-icon-warning-outline error-icon"></i>
            <div class="error-message">{{ error }}</div>
            <el-button type="primary" @click="retryLogin" size="medium" class="retry-btn">重试</el-button>
          </div>
        </div>
      </transition>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'DingDingCallback',
  data() {
    return {
      loading: true,
      error: ''
    };
  },
  created() {
    this.handleLogin();
  },
  methods: {
    /**
     * 处理钉钉登录回调
     */
    handleLogin() {
      this.loading = true;
      this.error = '';
      const code = this.$route.query.code;
      if (!code) {
        this.error = '未获取到钉钉授权码，请重新扫码登录。';
        this.loading = false;
        return;
      }
      this.$store.dispatch('DingDingLogin', { authCode: code })
        .then(() => {
          this.$router.replace({ path: '/feedbackManagementMobile' });
        })
        .catch((err) => {
          this.error = err && err.message ? err.message : '钉钉登录失败，请稍后重试。';
          this.loading = false;
        });
    },
    /**
     * 重新尝试登录
     */
    retryLogin() {
      this.handleLogin();
    }
  }
};
</script>

<style scoped>
.dingding-callback {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #e0e7ef 0%, #f5f7fa 100%);
}
.box-card {
  margin: 0 10px;
  width: 420px;
  border-radius: 18px;
  box-shadow: 0 8px 32px 0 rgba(60, 120, 240, 0.10), 0 1.5px 4px 0 rgba(0,0,0,0.04);
  border: none;
  padding-bottom: 12px;
}
.card-header {
  display: flex;
  align-items: center;
  padding-bottom: 8px;
}
.dingding-icon {
  margin-right: 10px;
  vertical-align: middle;
}
.header-title {
  font-size: 20px;
  font-weight: 600;
  color: #2d8cf0;
  letter-spacing: 1px;
}
.loading-block {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 36px 0 28px 0;
}
.loading-icon {
  font-size: 32px;
  color: #2d8cf0;
  margin-bottom: 12px;
}
.loading-text {
  font-size: 16px;
  color: #666;
  letter-spacing: 1px;
}
.error-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 36px 0 28px 0;
}
.error-icon {
  font-size: 32px;
  color: #f56c6c;
  margin-bottom: 10px;
}
.error-message {
  color: #f56c6c;
  margin-bottom: 18px;
  font-weight: bold;
  font-size: 15px;
  text-align: center;
}
.retry-btn {
  width: 120px;
  border-radius: 20px;
  font-weight: 500;
  letter-spacing: 1px;
  transition: background 0.2s;
}
.retry-btn:hover {
  background: #409eff;
  color: #fff;
}
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.4s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}
</style> 