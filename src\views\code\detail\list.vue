<template>
    <div >
      <div class="app-container">
        <div style="padding-top: 20px; padding-left: 10px">
              <el-form :inline="true"  @submit.native.prevent :model="queryForm">
                <el-form-item label="负责开发组：">
                  <el-select
                    clearable
                    placeholder=""
                    v-model="queryForm.pDevDept"
                  >
                  <el-option
                  v-for="item in developDeptList"
                  :key="item.value"
                  :value="item.value"
                  :label="item.desc"
                />
                  </el-select>
                </el-form-item>

                <el-form-item label="负责测试组：">
                  <el-select
                    clearable
                    placeholder=""
                    v-model="queryForm.pTestDept"
                  >
                    <el-option
                      v-for="item in testDeptList"
                      :key="item.value"
                      :value="item.value"
                      :label="item.desc"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item label="业务大类：">
                  <el-select
                    clearable
                    placeholder=""
                    v-model="queryForm.pBroadBusiness"
                  >
                    <el-option
                      v-for="item in broadBusinessList"
                      :key="item.id"
                      :value="item.id"
                      :label="item.name"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item label="业务小类：">
                  <el-select
                    clearable
                    placeholder=""
                    v-model="queryForm.pNarrowBusiness"
                  >
                    <el-option
                      v-for="item in narrowBusinessList"
                      :key="item.id"
                      :value="item.id"
                      :label="item.name"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item label="提交人姓名：">
                    <el-input v-model="queryForm.submitter"
                  />
                </el-form-item>
                <el-form-item label="提交说明：">
                    <el-input v-model="queryForm.submitterMessage"
                  />
                </el-form-item>
                <el-form-item label="所属代码库：">
                    <el-input v-model="queryForm.project"
                  />
                </el-form-item>
                <el-form-item label="分支：">
                    <el-input v-model="queryForm.branch"
                  />
                </el-form-item>

              <el-form-item></el-form-item>
              <div>
                <span>提交时间&nbsp;&nbsp;&nbsp;</span>
                <el-date-picker
                v-model="queryForm.beginDate"
                type="date"
                @input="changeTime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                :picker-options="datePickerOptions"
                >
              </el-date-picker>
              <span>&nbsp;&nbsp;&nbsp;&nbsp;至&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
              <el-date-picker
                v-model="queryForm.endDate"
                type="date"
                @input="changeTime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                :picker-options="datePickerOptions"
                >
              </el-date-picker>
              <span style="margin-left:10px;">
              <el-form-item >
                    <el-checkbox v-model="queryForm.merged">不含合并消息</el-checkbox>
                </el-form-item>
              </span>
                <el-form-item>
                  <el-button
                    icon="el-icon-search"
                    type="primary"
                    @click="handleQuery"
                  >
                    查询
                  </el-button>
                  <el-button
                    icon="el-icon-refresh"
                    type="primary"
                    @click="resetQuery"
                  >
                  </el-button>
                </el-form-item>
              </div>
              </el-form>
        </div>

        <el-table
          v-loading="listLoading"
          border
          stripe
          :data="tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
          :summary-method="getSummaries"
          show-summary
          height="calc(100vh - 350px)"
          @sort-change="handleSortChange"
          ref="table"
        >
          <el-table-column
            type="selection"
            fixed
            width="55">
          </el-table-column>
          <el-table-column label="提交编号" width="220" prop="gitShortId" sortable="custom">
            <template slot-scope="scope">
              <div class="link-type" @click="toGitShort(scope.row)">{{ scope.row.gitShortId }}</div>
            </template>
          </el-table-column>
          <el-table-column label="代码库" width="200" prop="project">
            <template slot-scope="scope">
              <div v-html="(scope.row.project)"></div>
            </template>
          </el-table-column>
          <el-table-column label="分支" width="200" prop="branch" sortable="custom">
            <template slot-scope="scope">
              {{scope.row.branch}}
            </template>
          </el-table-column>
          <el-table-column prop="message" label="提交说明" width="400">
            <template slot-scope="scope">
              {{scope.row.message}}
            </template>
          </el-table-column>

          <el-table-column label="提交人姓名" width="120" prop="committer"></el-table-column>
          <el-table-column label="负责开发组" width="120" prop="pDevDeptName"></el-table-column>
          <el-table-column label="负责测试组" width="120" prop="pTestDeptName"></el-table-column>
          <el-table-column label="业务大类" width="120" prop="pBroadBusinessName" sortable="custom"></el-table-column>
          <el-table-column label="业务小类" width="120" prop="pNarrowBusinessName" sortable="custom"></el-table-column>

          <el-table-column label="提交时间" width="130" prop="commitDate" sortable="custom" >
            <template slot-scope="scope">
              {{scope.row.commitDate}}
            </template>
          </el-table-column>
          <el-table-column label="增加行数" width="130" prop="additionsLine" sortable="custom" >
            <template slot-scope="scope">
              {{scope.row.additionsLine}}
            </template>
          </el-table-column>
          <el-table-column label="删除行数" width="130" prop="deleteLine" sortable="custom" >
            <template slot-scope="scope">
              {{scope.row.deleteLine}}
            </template>
          </el-table-column>
          <el-table-column label="调整总行数" width="130" prop="totalLine" sortable="custom" >
            <template slot-scope="scope">
              {{scope.row.totalLine}}
            </template>
          </el-table-column>
          <el-table-column label="采集时间" width="130" prop="createTime" >
            <template slot-scope="scope">
              {{scope.row.createTime}}
            </template>
          </el-table-column>
        </el-table>
        <!-- <el-pagination
          :current-page="1"
          :layout="layout"
          :page-size="50"
          :total="100"
          background
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        /> -->
  <div class="container">
    <el-pagination
      class="rear-page"
      :current-page="queryForm.pageNo"
      :page-size="queryForm.pageSize"
      layout="prev, pager, next, slot, jumper, sizes, total"
      :total="queryForm.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
      <!-- slot部分，跳转末页 -->
      <button
        class="lastPage"
        :disabled="queryForm.lastPageDisabled"
        @click="toLastPage"
      >
        <i class="el-icon-d-arrow-right"></i>
      </button>
    </el-pagination>
    <el-pagination
      class="ahead-page"
      :current-page="queryForm.pageNo"
      :page-size="queryForm.pageSize"
      layout="slot"
      :total="queryForm.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
      <!-- slot部分，跳转首页 -->
      <button
        class="firstPage"
        :disabled="queryForm.firstPageDisabled"
        @click="toFirstPage"
      >
        <i class="el-icon-d-arrow-left"></i>
      </button>
    </el-pagination>
    </div>
    </div>
    </div>
    </template>

    <script>
    import {
      getCodeDetail
    } from '@/api/gitcode'
    import { businessConfigByType } from '@/api/system/businessConfig'
    import { getListByType } from '@/api/system/dept'

      export default {
        name: 'Detail',
        data(){
          return{
            tableData: [], // 表格数据列表
            listLoading: false,
            queryForm: {
            dept: 0,
            total: 0,
            pageSize: 50, //  展示数量
            firstPageDisabled: false, //  首页
            lastPageDisabled: true, //  末页
            submitter: '',
            submitterMessage: '',
            project:'',
            branch:'',
            beginDate:'',
            endDate:'',
            merged: 0 ,
            pDevDept: 0,//负责开发组
            pTestDept: 0,//负责测试组
            pBroadBusiness: 0,//业务大类
            pNarrowBusiness: 0,//业务小类
            orderByField: '', // 排序字段
            orderRule: '', // 排序规则
            },
            optionsData: [],
            broadBusinessList: [],//业务大类
            narrowBusinessList: [],//业务小类
            busiconfigParam: {
              configType: null
            },
            testDeptList: [],//负责测试组
            developDeptList: [],//负责开发组
            }
        },
        computed: {
          datePickerOptions() {
            const today = new Date(); // 获取当前日期
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(today.getDate() - 6); // 计算7天前的日期

            return {
              disabledDate(date) {
                return date > today; // 禁用范围外的日期
              },
            };
          },
        },
    watch: {
        // 分页 计算首页和末页
        queryForm: {
          handler(newVal) {
            let pages = Math.ceil(newVal.total / newVal.pageSize);
        if (pages === 0) {
          // 数据(totalResult)为0
          this.queryForm.firstPageDisabled = true; // 首页按钮是否禁用
          this.queryForm.lastPageDisabled = true; // 末页按钮是否禁用
        } else {
          this.queryForm.firstPageDisabled = newVal.pageNo === 1;
          this.queryForm.lastPageDisabled = newVal.pageNo === pages;
        }
          },
          // 一进页面就执行
          immediate: true,
          deep: true,
        },
      },
  methods:{
    // 跳转到git提交页
    toGitShort (row) {
      const url = `https://git.qmqb.top/${row.project}/commit/${row.gitShortId}`
      window.open(url, "_blank")
    },
    // 获取业务配置
    async geBusinessConfigList(configType) {
        const res = await businessConfigByType(configType);
        if (res.code === 200) {
          return res.data;
        }
    },
    //通过部门类型获取部门列表
    async getDeptListByType(deptType) {
      const res = await getListByType({deptType: deptType});
      if (res.code === 200) {
        return res.data;
      }
    },
    async fetchData() {
      //业务大类
      this.busiconfigParam.configType = 1;
      this.broadBusinessList = await this.geBusinessConfigList(this.busiconfigParam);
      //业务小类
      this.busiconfigParam.configType = 2;
      this.narrowBusinessList = await this.geBusinessConfigList(this.busiconfigParam);
      //负责开发组
      this.developDeptList =  await this.getDeptListByType(1);
      //负责测试组
      this.testDeptList = await this.getDeptListByType(2);
    },
    //按首字母进行排序
    handleSortChange({ prop, order }) {
      if (order === 'descending') {
        this.queryForm.orderByField = prop;
        this.queryForm.orderRule = 'desc';
      } else if (order === 'ascending') {
        this.queryForm.orderByField = prop;
        this.queryForm.orderRule = 'asc';
      } else {
        // 如果没有指定排序方向，则清空排序条件
        this.queryForm.orderByField = '';
        this.queryForm.orderRule = '';
      }
      // 更新查询条件后重新获取列表
      this.getList();
    },
    //  改变页码
    handlePageChange({ pageNo, pageSize }) {
      this.queryForm.pageNo = pageNo;
      this.queryForm.pageSize = pageSize;
    },


    //  前往首页
    toFirstPage() {
      this.handleCurrentChange(1);
    },

    //  前往末页
    toLastPage() {
      let max = Math.ceil(this.queryForm.total / this.queryForm.pageSize);
      this.handleCurrentChange(max);
    },

    // 查询
    handleQuery(){
        this.getList();
    },

    // 重置
    resetQuery(){
      this.queryForm = {
          dept: 0,
          total: 0,
          pageSize: 50, //  展示数量
          firstPageDisabled: false, //  首页
          lastPageDisabled: true, //  末页
          submitter: '',
          submitterMessage: '',
          project:'',
          branch:'',
          beginDate:'',
          endDate:'',
          merged: 0 ,
          pDevDept: 0,//负责开发组
          pTestDept: 0,//负责测试组
          pBroadBusiness: 0,//业务大类
          pNarrowBusiness: 0,//业务小类
          orderByField: '', // 排序字段
          orderRule: '', // 排序规则
      }
      this.getList()
    },
    // 当前页面
    handleCurrentChange(val) {
        this.queryForm.pageNo = val
        console.log('handleCurrentChange',val)
        this.getList()
      },

    // 页数
    handleSizeChange(val) {
        this.queryForm.pageSize = val
        console.log('handleSizeChange',val)
        this.getList()
      },

    // 下拉框选择
    handleSelectionChange(val){

    },
        // 初始化
    async getList(){
    this.listLoading = true
    this.tableData = []
    console.log('dept = ' ,this.queryForm.dept, 'submitter = ' ,
    this.queryForm.submitter, 'submitterMessage = ', this.queryForm.submitterMessage,
    'branch = ',this.queryForm.branch, 'project = ' , this.queryForm.project,
    'beginDate = ', this.queryForm.beginDate , 'endDate = ' , this.queryForm.endDate,
    'merged = ' , this.queryForm.merged)
    const resp = await getCodeDetail(this.queryForm)
    console.log(' resp = ' ,resp)
    this.formatResource(resp.data.records)
    this.listLoading = false
    this.queryForm.total = resp.data.total * 1
    this.listLoading = false
    },
    formatResource(data){
    if (data) {
    data.forEach((item) => {
      let resource = {
      gitShortId: item.gitShortId,
      project: item.project,
      branch: item.branch,
      message: item.message,
      author: item.author,
      committer: item.committer,
      pTestDeptName: item.pTestDeptName,
      pDevDeptName: item.pDevDeptName,
      pBroadBusinessName: item.pBroadBusinessName,
      pNarrowBusinessName: item.pNarrowBusinessName,
      commitDate: item.commitDate,
      additionsLine: item.additionsLine,
      deleteLine: item.deleteLine,
      totalLine: item.totalLine,
      finisheddate: item.finisheddate,
      createTime: item.createTime,
            }
            this.tableData.push(resource)
          })
        }
    },
    // 获取当前年份月
    getDate(){
      const nowDate = new Date();
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
      };
      this.queryForm.finishYear = date.year;
      this.queryForm.finishMonth = date.month > 10 ? date.month : date.month;
    },
    changeTime(){
      this.$forceUpdate()
    },
   getDay(day){
    var today = new Date()
    var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day
    today.setTime(targetday_milliseconds); //注意，这行是关键代码
    var tYear = today.getFullYear()
    var tMonth = today.getMonth()
    var tDate = today.getDate()
    tMonth = this.doHandleMonth(tMonth + 1)
    tDate = this.doHandleMonth(tDate)
    return tYear + "-" + tMonth + "-" + tDate
    },
    doHandleMonth(month) {
    var m = month
    if (month.toString().length === 1) {
        m = "0" + month
    }
    return m;
    },
    handleSelectedDate(){
     this.queryForm.beginDate =  this.getDay(-6)
     this.queryForm.endDate = this.getDay(0)
     console.log('this.beginDate = ' , this.queryForm.beginDate , 'endDate = ' , this.queryForm.endDate)
    },
    getSummaries(param){
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }
        if (index === 11 || index === 12 || index === 13) { //指定哪一列合计 如果需要全部合计 去掉这个判断即可
          const values = data.map((item) => Number(item[column.property]));
          if (!values.every((value) => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              console.log();
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
          } else {
            sums[index] = "";
          }
        }
      });
      return sums;
    },
      },
        created(){
          this.getDate();
          this.handleSelectedDate()
        },
        mounted(){
          this.fetchData()
        },
        updated() {
          this.$nextTick(() => {
            this.$refs['table'].doLayout();
          })

        }
      }
    </script>

    <style lang=scss scoped>
    .app-container{
        padding: 20px;
    }
    .table {
        background-color: #fff;
        .tiltle-cn {
          color: #2184d8;
        }
        .control-cell {
          display: flex;
          flex-direction: row;
          .control-cell-item {
            margin-left: 10px;
          }
        }
      }

     .container{
        float: left;
     }
      .el-pagination {
      float: right;
      margin-top: 10px;
    }
    .el-pagination.ahead-page {
      padding-right: 0;
    }
    .el-pagination.rear-page {
      padding-left: 0;
    }
    .firstPage,
    .lastPage {
      background-color: white;
      cursor: pointer;
    }
    </style>
