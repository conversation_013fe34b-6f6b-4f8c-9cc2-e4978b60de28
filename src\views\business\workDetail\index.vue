<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="查询年份">
        <el-select
          v-model="queryParams.evalYear"
          maxlength="4"
          placeholder="选择查询年份"
          clearable
          size="small"
          style="width: 160px"
        >
          <el-option
            v-for="year in years"
            :key="year"
            :label="year"
            :value="year"
          />
          </el-select>
      </el-form-item>
      <el-form-item label="查询月份">
        <el-select
          v-model="queryParams.evalMonth"
          placeholder="选择查询月份"
          clearable
          size="small"
          maxlength="2"
          style="width: 160px"
        >
        <el-option
          v-for="dict in monthDict"
          :key="dict.dictValue"
          :label="dict.dictLabel"
          :value="dict.dictValue"
        />
        </el-select>
      </el-form-item>
      <el-form-item label="姓名" prop="workUsername">
        <el-input
          v-model="queryParams.workUsername"
          placeholder="请输入姓名"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getList">查询</el-button>
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-form-item>
    </el-form>
    <el-tabs  v-model="activeName" @tab-click="handleClick" v-show="showDetail">
      <!-- 任务明细 -->
      <el-tab-pane name="task" label="任务明细">
        <el-table
          v-loading="loading"
          border
          stripe
          :data="taskTableData"
          style="width: 100%"
          @sort-change="onSortChange"
          @row-dblclick="handleTableRow"
          height="calc(100vh - 270px)"
        >
          <el-table-column label="序号" type="index" width="50" />
          <el-table-column label="所属项目" width="220" prop="pname" sortable />
          <el-table-column label="需求标题" width="200" prop="title" sortable>
            <template slot-scope="scope">
              <div v-html="(scope.row.title)"></div>
            </template>
          </el-table-column>
          <el-table-column label="主任务名称" width="200" prop="name" sortable />
          <el-table-column label="子任务名称" width="200" prop="sonName" sortable />
          <el-table-column label="当前状态" width="100" prop="status" sortable />
          <el-table-column label="类型" width="100" prop="type" sortable />
          <el-table-column label="任务启动人" width="130" prop="taskStarter" sortable  />
          <el-table-column label="任务启动时间" width="130" prop="realStarted" sortable="custom" :sort-orders="['ascending', 'descending']" />
          <el-table-column label="完成人" width="130" prop="finishedby" sortable />
          <el-table-column label="完成时间" width="130" prop="finishedDate" sortable="custom" :sort-orders="['ascending', 'descending']" />
          <el-table-column label="消耗时间(h)" width="130" prop="consumed" sortable="custom" :sort-orders="['ascending', 'descending']" />
          <el-table-column label="关闭人" width="130" prop="closedby" sortable />
          <el-table-column label="关闭时间" width="130" prop="closeddate" sortable />
          <el-table-column label="关闭原因" width="130" prop="closedreason" sortable />
        </el-table>
      </el-tab-pane>
      <!-- 代码提交明细 -->
      <el-tab-pane name="code" label="代码提交明细">
        <el-table
          v-loading="loading"
          border
          stripe
          :data="codeTableData"
          style="width: 100%"
          :summary-method="getSummaries"
          show-summary
          height="calc(100vh - 270px)"
          @sort-change="onSortChange"
        >
          <el-table-column label="序号" type="index" width="50" />
          <el-table-column label="提交编号" width="220" prop="gitShortId" sortable="custom">
            <template slot-scope="scope">
              <div class="link-type" @click="toGitShort(scope.row)">{{ scope.row.gitShortId }}</div>
            </template>
          </el-table-column>
          <el-table-column label="代码库" width="200" prop="project">
            <template slot-scope="scope">
              <div v-html="(scope.row.project)"></div>
            </template>
          </el-table-column>
          <el-table-column label="分支" width="200" prop="branch" sortable="custom" />
          <el-table-column label="提交说明" width="200" prop="message" />
          <el-table-column label="提交人姓名" width="120" prop="committer" />
          <el-table-column label="负责开发组" width="120" prop="pDevDeptName" />
          <el-table-column label="负责测试组" width="120" prop="pTestDeptName" />
          <el-table-column label="业务大类" width="120" prop="pBroadBusinessName" sortable="custom" />
          <el-table-column label="业务小类" width="120" prop="pNarrowBusinessName" sortable="custom" />
          <el-table-column label="提交时间" width="130" prop="commitDate" sortable="custom" />
          <el-table-column label="增加行数" width="130" prop="additionsLine" sortable="custom" />
          <el-table-column label="删除行数" width="130" prop="deleteLine" sortable="custom" />
          <el-table-column label="调整总行数" width="130" prop="totalLine" sortable="custom" />
          <el-table-column label="采集时间" width="130" prop="createTime" />
        </el-table>
      </el-tab-pane>
      <!-- 文档记录 -->
      <el-tab-pane name="doc" label="文档记录">
        <el-table  v-loading="loading" :data="docTableData" style="width: fit-content;" class="elTable" @cell-dblclick="jumpToDoc" height="calc(100vh - 270px)" stripe border>
          <el-table-column align="center" label="序号" type="index" width="50" />
          <el-table-column align="center" label="id" prop="id" width="80" />
          <el-table-column align="center" label="标题(双击对应行可跳转文档链接)" prop="title" :show-overflow-tooltip="true" width="400" />
          <el-table-column align="center" label="所属组" prop="groupName" :show-overflow-tooltip="true" width="150" />
          <el-table-column align="center" label="作者" prop="addedBy" :show-overflow-tooltip="true" width="80" />
          <el-table-column align="center" label="添加日期" prop="addedDate" :show-overflow-tooltip="true" width="160" />
        </el-table>
      </el-tab-pane>
      <!-- 全部预警 -->
      <el-tab-pane name="warn" label="全部预警">
        <el-table v-loading="loading" :data="warnTableData" height="calc(100vh - 270px)" stripe border>
          <el-table-column label="序号" type="index" width="50" />
          <el-table-column label="预警编号" align="center" prop="warnCode" />
          <el-table-column label="预警内容" align="center" prop="warnContent" />
          <el-table-column label="被预警人" align="center" prop="nickName" />
          <el-table-column label="状态" align="center" prop="warnStatus">
            <template slot-scope="scope">
              <span v-show="scope.row.warnStatus == 0">未通知</span>
              <span v-show="scope.row.warnStatus == 1">通知失败</span>
              <span v-show="scope.row.warnStatus == 2">已通知</span>
            </template>    
          </el-table-column>
          <el-table-column label="预警类型" align="center" prop="warnType">
            <template slot-scope="scope">
              <span v-show="scope.row.warnType == 1">考勤</span>
              <span v-show="scope.row.warnType == 2">任务</span>
              <span v-show="scope.row.warnType == 3">代码</span>
              <span v-show="scope.row.warnType == 4">用例</span>
              <span v-show="scope.row.warnType == 5">项目</span>
              <span v-show="scope.row.warnType == 6">同步</span>
              <span v-show="scope.row.warnType == 7">文档</span>
              <span v-show="scope.row.warnType == 8">管理</span>
            </template>
          </el-table-column>
          <el-table-column label="预警级别" align="center" prop="warnLevel">
            <template slot-scope="scope">
              <span v-show="scope.row.warnLevel == 0">P0</span>
              <span v-show="scope.row.warnLevel == 1">P1</span>
              <span v-show="scope.row.warnLevel == 2">P2</span>
              <span v-show="scope.row.warnLevel == 3">P3</span>
              <span v-show="scope.row.warnLevel == 4">P4</span>
            </template>
          </el-table-column>
          <el-table-column label="预警时间" align="center" prop="warnTime" />
          <el-table-column label="预警方式" align="center" prop="warnWay">
            <template slot-scope="scope">
              <span v-show="scope.row.warnWay == 1">钉钉</span>
              <span v-show="scope.row.warnWay == 2">邮件</span>
              <span v-show="scope.row.warnWay == 4">短信</span>
              <span v-show="scope.row.warnWay == 8">电话</span>
            </template>
          </el-table-column>
          <el-table-column label="是否处理" align="center" prop="handleStatus">
            <template slot-scope="scope">
              <span v-show="scope.row.handleStatus == 0">否</span>
              <span v-show="scope.row.handleStatus == 2">是</span>
            </template>
          </el-table-column>
          <el-table-column label="预警处理情况" align="center" prop="handleContent" />
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <div class="container">
      <el-pagination
        class="rear-page"
        :current-page="paginationData[activeName].pageNo"
        :page-size="paginationData[activeName].pageSize"
        :page-sizes="[10, 20, 50, 100, 200]"
        layout="prev, pager, next, slot, jumper, sizes, total"
        :total="paginationData[activeName].total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <!-- slot部分，跳转末页 -->
        <button class="lastPage" :disabled="paginationData[activeName].lastPageDisabled" @click="toLastPage">
          <i class="el-icon-d-arrow-right"></i>
        </button>
      </el-pagination>
      <el-pagination class="ahead-page" layout="slot">
        <!-- slot部分，跳转首页 -->
        <button class="firstPage" :disabled="paginationData[activeName].firstPageDisabled" @click="toFirstPage">
          <i class="el-icon-d-arrow-left"></i>
        </button>
      </el-pagination>
    </div>
  </div>
</template>

<script>
import * as api from "@/api/business/workDetail"
import {getDicts} from "../../../api/system/dict/data"

export default {
  name: 'WorkDetail',
  data() {
    return {
      activeName: 'task', // ['task', 'code', 'doc', 'warn']
      lastActiveName: 'task',
      // 遮罩层
      loading: true,
      // 是否显示明细
      showDetail: false,
      // 显示搜索条件
      showSearch: true,
      // 分页参数
      total: 0,
      // 查询参数
      monthDict: [],
      years: [],
      taskTableData: [],
      codeTableData: [],
      docTableData: [],
      warnTableData: [],
      paginationData: {
        task: {
          total: 0,
          pageNo: 1,
          pageSize: 50,
          firstPageDisabled: false, //  首页
          lastPageDisabled: true //  末页
        },
        code: {
          total: 0,
          pageNo: 1,
          pageSize: 50,
          firstPageDisabled: false, //  首页
          lastPageDisabled: true //  末页
        },
        doc: {
          total: 0,
          pageNo: 1,
          pageSize: 50,
          firstPageDisabled: false, //  首页
          lastPageDisabled: true //  末页
        },
        warn: {
          total: 0,
          pageNo: 1,
          pageSize: 50,
          firstPageDisabled: false, //  首页
          lastPageDisabled: true //  末页
        }
      },
      queryParams: {
        evalYear: undefined,
        evalMonth: undefined,
        workUsername: undefined,
      },
    }
  },
  watch: {
    $route(route) {
      if (this.$route.query.params && this.$route.name === 'WorkDetail' && this.$route.params.fromList) {
        const queryParams = JSON.parse(this.$route.query.params || '{}')
        if (queryParams.workUsername !== this.queryParams.workUsername ||
          queryParams.evalYear !== this.queryParams.evalYear ||
          queryParams.evalMonth !== this.queryParams.evalMonth
        ) {
          this.init()
          this.getList()
        }
      }
    }
  },
  created() {
    this.createYear()
    this.init()
    this.getList()
  },
  methods: {
    createYear (){
      // 获取当前年份和前两年的年份
      const currentYear = new Date().getFullYear()
      const startYear = 2021

      // 构造年份选择器数据源
      for (let i = currentYear; i >= startYear; i--) {
          this.years.push(i.toString())
      }
      this.evalYear = currentYear
    },
    async init () {
      const now = new Date()
      this.queryParams.evalYear = now.getFullYear()
      this.queryParams.evalMonth = now.getMonth()+1

      const queryParams = JSON.parse(this.$route.query.params || '{}')
      this.queryParams.evalYear = queryParams.evalYear
      this.queryParams.evalMonth = queryParams.evalMonth
      this.queryParams.workUsername = queryParams.workUsername

      await getDicts("month").then(response => {
        this.monthDict = response.data
      })
    },
    handleClick (tab) {
      if (tab.name !== this.lastActiveName) {
        this.lastActiveName = tab.name
        delete this.queryParams.orderByField
        delete this.queryParams.orderRule
        this.getList()
      }
    },
    /** 查询【请填写功能名称】列表 */
    getList () {
      if(this.queryParams.evalYear === undefined || this.queryParams.evalYear === '') {
        this.$message.error("请选择查询年份!")
        return
      }
      if(this.queryParams.workUsername === undefined || this.queryParams.workUsername === '') {
        this.$message.error("请输入姓名!")
        return
      }
      let apiName = ''
      switch (this.activeName) {
        case 'task':
          apiName = 'queryUserTask'
          break
        case 'code':
          apiName = 'userCommitList'
          break
        case 'doc':
          apiName = 'getUserDocList'
          break
        case 'warn':
          apiName = 'getWarnAllPageByUser'
          // 添加测试代码，检查API是否可用
          console.log('API对象:', api);
          console.log('全部预警API函数:', api['getWarnAllPageByUser']);
          break
      }
      this.loading = true
      this.showDetail = true
      let params = {...this.queryParams}
      if (this.paginationData[this.activeName]) {
        params.pageNo = this.paginationData[this.activeName].pageNo,
        params.pageSize = this.paginationData[this.activeName].pageSize
      }
      
      // 添加更多测试代码
      console.log('当前tab:', this.activeName);
      console.log('API名称:', apiName);
      console.log('请求参数:', params);
      
      api[apiName](params).then(res => {
        this.loading = false
        if (res.code === 200) {
          this[this.activeName + 'TableData'] = res.data.records
          this.paginationData[this.activeName].total = res.data.total
          // 首页、末页按钮是否禁用
          if (res.data.pages === 0) {
            this.paginationData[this.activeName].firstPageDisabled = true
            this.paginationData[this.activeName].lastPageDisabled = true
          } else {
            this.paginationData[this.activeName].firstPageDisabled = this.paginationData[this.activeName].pageNo === 1
            this.paginationData[this.activeName].lastPageDisabled = this.paginationData[this.activeName].pageNo === res.data.pages
          }
        } else {
          this.$message.error(res.msg)
        }
      }).catch(err => {
        this.loading = false
      })
    },
    handleCurrentChange (val) {
      this.paginationData[this.activeName].pageNo = val
      this.getList()
    },
    handleSizeChange (val) {
      this.paginationData[this.activeName].pageSize = val
      this.getList()
    },
    //  前往首页
    toFirstPage() {
      this.handleCurrentChange(1);
    },
    //  前往末页
    toLastPage() {
      let max = Math.ceil(this.paginationData[this.activeName].total / this.paginationData[this.activeName].pageSize);
      this.handleCurrentChange(max);
    },
    // 点击排序
    onSortChange (column) {
      this.queryParams.orderByField = column.prop
      this.queryParams.orderRule = column.order === "ascending" ? 'asc' : 'desc'
      // 获取后台列表数据
      this.getList()
    },
    // 跳转到任务地址
    handleTableRow (row) {
      const url = 'http://pm.qmqb.top/task-view-'+ row.id  +'.html/'
      window.open(url, "_blank")
    },
    // 跳转到git提交页
    toGitShort (row) {
      const url = `https://git.qmqb.top/${row.project}/commit/${row.gitShortId}`
      window.open(url, "_blank")
    },
    getSummaries (param){
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计"
          return
        }
        if (index === 11 || index === 12 || index === 13) { //指定哪一列合计 如果需要全部合计 去掉这个判断即可
          const values = data.map((item) => Number(item[column.property]))
          if (!values.every((value) => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              console.log()
              const value = Number(curr)
              if (!isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0)
          } else {
            sums[index] = ""
          }
        }
      })
      return sums
    },
    // 跳转到文档
    jumpToDoc(row, column, cell, event) {
      window.open(row.jumpUrl)
      console.log(row, event, cell, column)
    },
    //导出
    handleExport(){
      let exportUrl = ''
      let fileName = ''
      
      switch (this.activeName) {
        case 'task':
          exportUrl = 'task/queryUserTask/export'
          fileName = `个人任务明细_${new Date().getTime()}.csv`
          break
        case 'code':
          exportUrl = '/code/query/userCommitList/export'
          fileName = `代码提交明细_${new Date().getTime()}.csv`
          break
        case 'doc':
          exportUrl = '/business/imsPerformStat/exportUserDocList'
          fileName = `文档记录_${new Date().getTime()}.csv`
          break
        case 'warn':
          exportUrl = '/warn/query/exportUserWarn'
          fileName = `全部预警_${new Date().getTime()}.csv`
          break
      }
      
      this.download(exportUrl, {
        ...this.queryParams
      }, fileName)
    },
  }
}
</script>
<style lang="scss" scoped>
.container{
  float: left;
}
.el-pagination {
  float: right;
  margin-top: 10px;
}
.el-pagination.ahead-page {
  padding-right: 0;
}
.el-pagination.rear-page {
  padding-left: 0;
}
.firstPage, .lastPage {
  background-color: white;
  cursor: pointer;
}
</style>
