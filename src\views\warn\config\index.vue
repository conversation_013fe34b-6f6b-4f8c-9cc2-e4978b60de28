<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="预警类型" prop="warnType">
        <el-select v-model="queryParams.warnType" placeholder="请选择预警类型" clearable>
          <el-option
            v-for="dict in dict.type.warn_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="预警对象" prop="warnObject">
        <el-select v-model="queryParams.warnObject" placeholder="请选择预警对象" clearable>
          <el-option
            v-for="dict in dict.type.warn_object"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="预警级别" prop="warnLevel">
        <el-select v-model="queryParams.warnLevel" placeholder="请选择预警级别" clearable>
          <el-option
            v-for="dict in dict.type.warn_level"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="预警编码" prop="warnCode">
        <el-input
          v-model="queryParams.warnCode"
          placeholder="请输入预警编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="触发方式" prop="triggerMethod">
        <el-select v-model="queryParams.triggerMethod" placeholder="请选择触发方式" clearable>
          <el-option
            v-for="dict in dict.type.trigger_method"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="统计范围" prop="statisticsScope">
        <el-select v-model="queryParams.statisticsScope" placeholder="请选择统计范围" clearable>
          <el-option
            v-for="dict in dict.type.statistics_scope"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="预警开关" prop="isUsed">
        <el-select v-model="queryParams.isUsed" placeholder="请选择预警开关" clearable>
          <el-option
            v-for="dict in dict.type.biz_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['business:warnConfig:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['business:warnConfig:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['business:warnConfig:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['business:warnConfig:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="warnConfigList" @selection-change="handleSelectionChange" height="calc(100vh - 320px)" stripe border>
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="主键" align="center" prop="id" v-if="true"/>
      <el-table-column label="预警类型" align="center" prop="warnType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.warn_type" :value="scope.row.warnType"/>
        </template>
      </el-table-column>
      <el-table-column label="预警对象" align="center" prop="warnObject">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.warn_object" :value="scope.row.warnObject"/>
        </template>
      </el-table-column>
      <el-table-column label="预警级别" align="center" prop="warnLevel">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.warn_level" :value="scope.row.warnLevel"/>
        </template>
      </el-table-column>
      <el-table-column label="预警编码" align="center" prop="warnCode" width="200"/>
      <el-table-column label="触发规则" align="center" prop="rule.ruleName"/>
      <el-table-column label="触发方式" align="center" prop="triggerMethod">
        <template slot-scope="scope">
          <span v-for=" (item, index) in scope.row.triggerMethods" :key="index">
            <dict-tag :options="dict.type.trigger_method" :value="item"/>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="预警内容" align="center" prop="warnContent" width="300"/>
      <el-table-column label="统计范围" align="center" prop="statisticsScope">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.statistics_scope" :value="scope.row.statisticsScope"/>
        </template>
      </el-table-column>
      <el-table-column label="预警开关" align="center" prop="isUsed">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.isUsed"
                     :active-value="1"
                     :inactive-value="0"
                     @change="handleChangeSwitchState(scope.row)">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['business:warnConfig:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['business:warnConfig:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改预警配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="预警类型" prop="warnType">
          <el-select v-model="form.warnType" placeholder="请选择预警类型">
            <el-option
              v-for="dict in dict.type.warn_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="预警对象" prop="warnObject">
          <el-select v-model="form.warnObject" placeholder="请选择预警对象">
            <el-option
              v-for="dict in dict.type.warn_object"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="预警级别" prop="warnLevel">
          <el-select v-model="form.warnLevel" placeholder="请选择预警级别">
            <el-option
              v-for="dict in dict.type.warn_level"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="预警编码" prop="warnCode">
          <el-input v-model="form.warnCode" placeholder="请输入预警编码"/>
        </el-form-item>
        <el-form-item label="触发规则" prop="triggerRuleId">
          <el-select v-model="form.triggerRuleId" placeholder="请选择触发规则">
            <el-option
              v-for="rule in ruleList"
              :key="rule.id"
              :label="rule.ruleName"
              :value="rule.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="触发方式" prop="triggerMethod">
          <el-checkbox-group v-model="form.triggerMethods">
            <el-checkbox
              v-for="dict in dict.type.trigger_method"
              :key="dict.value"
              :label="parseInt(dict.value)"
            > {{dict.label}} </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="预警内容">
          <el-input v-model="form.warnContent" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="统计范围" prop="statisticsScope">
          <el-select v-model="form.statisticsScope" placeholder="请选择统计范围">
            <el-option
              v-for="dict in dict.type.statistics_scope"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {addWarnConfig, delWarnConfig, getWarnConfig, listWarnConfig, updateWarnConfig, enableWarnConfig} from "@/api/business/warnConfig";
import {listWarnTriggerRule} from "@/api/business/warnTriggerRule";

export default {
  name: "WarnConfig",
  dicts: ['trigger_method', 'warn_object', 'statistics_scope', 'warn_level', 'biz_yes_no', 'warn_type'],
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 预警配置表格数据
      warnConfigList: [],
      // 预警配置触发规则数据
      ruleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        warnType: undefined,
        warnObject: undefined,
        warnLevel: undefined,
        warnCode: undefined,
        triggerRuleId: undefined,
        triggerMethod: undefined,
        triggerMethods: [],
        warnContent: undefined,
        statisticsScope: undefined,
        isUsed: undefined,
        rule: {
          warnType: undefined,
          ruleName: undefined,
          ruleType: undefined,
          compareType: undefined,
          compareSymbol: undefined,
          compareValue: undefined,
        },
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        warnType: [
          {required: true, message: "预警类型不能为空", trigger: "change"}
        ],
        warnObject: [
          {required: true, message: "预警对象不能为空", trigger: "change"}
        ],
        warnLevel: [
          {required: true, message: "预警级别不能为空", trigger: "change"}
        ],
        warnCode: [
          {required: true, message: "预警编码不能为空", trigger: "blur"}
        ],
        triggerMethods: [
          {required: true, message: "触发方式不能为空", trigger: "change"}
        ],
        statisticsScope: [
          {required: true, message: "统计范围不能为空", trigger: "change"}
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getRuleList();
  },
  methods: {
    /** 查询预警配置列表 */
    getList() {
      this.loading = true;
      listWarnConfig(this.queryParams).then(response => {
        this.warnConfigList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询预警配置规则列表 */
    getRuleList() {
      this.loading = true;
      this.queryParams.pageNum = null;
      this.queryParams.pageSize = null;
      listWarnTriggerRule(this.queryParams).then(response => {
        this.ruleList = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        warnType: undefined,
        warnObject: undefined,
        warnLevel: undefined,
        warnCode: undefined,
        triggerRuleId: undefined,
        triggerMethod: undefined,
        triggerMethods: [],
        warnContent: undefined,
        statisticsScope: undefined,
        isUsed: undefined,
        remark: undefined,
        delFlag: undefined,
        version: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 10;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();

      this.open = true;
      this.title = "添加预警配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getWarnConfig(id).then(response => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = "修改预警配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          if (this.form.id != null) {
            updateWarnConfig(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addWarnConfig(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除预警配置编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delWarnConfig(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('business/warnConfig/export', {
        ...this.queryParams
      }, `warnConfig_${new Date().getTime()}.xlsx`)
    },
    /** 开关按钮操作 */
    handleChangeSwitchState(row) {
      this.loading = true;
      this.form.id = row.id;
      this.form.isUsed = row.isUsed;
      enableWarnConfig(this.form).then(response => {
        this.loading = false;
        this.getList();
      })
    },
  }
};
</script>
