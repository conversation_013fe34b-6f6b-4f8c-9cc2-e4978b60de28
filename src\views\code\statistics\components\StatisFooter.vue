<template>
    <div>
      <div class="header-charts">
        <div ref="myEcharts" style="width:100%;height:500px"></div>
    </div>
    </div>
  </template>

  <script>
  import * as echarts from 'echarts'
  export default {
    name: 'Footer',
    props: {},
    components: {},
    data () {
      return {
        myEcharts: {},
        nameData: [] ,
        additonalsLineData: [],
        deleteLineData: [],
        codeFrontData:[],
        queryForm:{
            beginDate:'',
            endDate:'',
            managered: false ,
        }
      }
    },
    watch: {},
    computed: {},
    methods: {
      parentMsg: function (data) {
            this.codeFrontData = data
            this.drawLine(data)
          },
      drawLine(data){
        if (data) {
              console.log('data = ' , data)
              this.nameData = []
              this.additonalsLineData = []
              this.deleteLineData = []
              data.forEach((item) => {
                // 遍历名字
                this.nameData.push(item.name)
                // 遍历新增行
                this.additonalsLineData.push(item.additonalsLine)
                // 遍历删除行
                this.deleteLineData.push(item.deleteLine)
              })
            }else {
                this.nameData = []
                this.additonalsLineData = []
                this.deleteLineData = []
            }
            var myEcharts = echarts.init(this.$refs['myEcharts'])
            var option;
            option = {
                // 标题
                title: {
                  text: "代码贡献量前25名统计",
                  align: 'center'
                },
                tooltip:{
                  trigger: "axis",
                },

                xAxis: {
                    type: 'category',
                    data: this.nameData
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                    data: this.deleteLineData ,
                    type: 'bar',
                    stack:'1',
                    name: '删除行数',
                    itemStyle: {
                        normal: {
                          normal:{color:"#5C5C61"},
                        },
                      }
                    },
                    {
                    name: '新增行数',
                    type: 'bar',
                    stack:'1',//根据此参数来堆叠数据
                    data: this.additonalsLineData,
                    itemStyle:{
                          normal:{color:"#95CEFF"},
                            }
                    }
                ]
            };
            option && myEcharts.setOption(option);
          //   window.addEventListener('resize',()=>{
          //     this.myEcharts.resize()
          //  })
        }
    },
    created () { },
    activated () { },
    mounted () {
      this.drawLine()
     },
    beforeDestroy () { }
  }
  </script>

  <style scoped>
  </style>
