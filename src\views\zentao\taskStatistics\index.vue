<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item label="年份" prop="year">
        <el-select v-model="queryParams.year" placeholder="请选择年份" value-key="value">
          <el-option
            v-for="item in yearList"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
      <el-form-item>
        <div v-if="updateTime!= null">
          <el-tag effect="plain" type="info" size="medium">最后更新时间：{{updateTime}}</el-tag>
        </div>
      </el-form-item>
    </el-form>
    <!--为echarts准备一个具备大小的容器dom-->
    <div id="chart" style="width: 1650px;height: 700px;" v-loading="loading"></div>
  </div>

</template>

<script>
import {getTechnologyTaskCount} from "@/api/taskQuery";
import * as echarts from 'echarts/core';
import {GridComponent, LegendComponent, TitleComponent, ToolboxComponent, TooltipComponent} from 'echarts/components';
import {BarChart} from 'echarts/charts';
import {UniversalTransition} from 'echarts/features';
import {CanvasRenderer} from 'echarts/renderers';

echarts.use([
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  BarChart,
  CanvasRenderer,
  UniversalTransition
]);

var codeChart;

export default {
  name: "TaskStatistics",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 查询参数
      queryParams: {
        year: null
      },
      yearList: [],
      updateTime: null
    };
  },
  created() {
    this.getYearList();
  },
  methods: {
    /** 查询年份列表 */
    getYearList() {
      var date = new Date();
      var year = date.getFullYear();
      //起始年份
      var startYear = year - 3;
      //结束年份
      var endYear = year;
      var years = [];
      for (var i = startYear; i <= endYear; i++) {
        var obj = {
          label: i,
          value: i,
        }
        years.unshift(obj);
      }
      this.yearList = years;
      this.queryParams.year = year;
    },
    /** 查询年度数据 */
    getStatisticsLine() {
      this.loading = true
      getTechnologyTaskCount({year: this.queryParams.year}).then(res => {
        if (res.code === 200) {
          this.loading = false
          if (res.data && res.data.length > 0) {
            this.updateTime = res.data[0].updateTime;
            let series = []
            res.data.forEach(item => {
              let serie = {
                name: item.deptName,
                type: 'bar',
                stack: 'total',
                label: {
                  show: true
                },
                emphasis: {
                  focus: 'series'
                },
                data: item.taskCountList
              }
              series.push(serie)
            })
            this.loadChart(series)
          } else {
            this.$message.error('数据异常')
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 初始化数据，不够12月份，用0补齐
    padArray (arr) {
        while (arr.length < 12) {
          arr.push(0)
        }
        return arr
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getStatisticsLine();
    },
    // 重置
    resetQuery(){
      this.getYearList()
      this.handleQuery()
    },
    /** 加载柱状图 */
    loadChart(series) {
      var chartDom = document.getElementById('chart');
      // 如果初始化过 echarts 实例，销毁。
      if (codeChart != null && codeChart != "" && codeChart != undefined) {
        codeChart.dispose();
      }
      // 基于准备好的 dom ，重新初始化 echarts 实例
      codeChart = echarts.init(chartDom);
      var option;
      var text = this.queryParams.year + '年度技术中心各组任务统计图';
      option = {
        title: {
          left: 'center',
          text: text
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // Use axis to trigger tooltip
            type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
          },
          order: 'valueDesc',
          // 悬浮窗在图表内
          confine: true
        },
        legend: {
          top: 40,
          width: 1300,
          tooltip: {
            show: true,
            formatter: function (params) {

              var seriesData = series.find(s => s.name === params.name).data
              var tooltipData = []
              for (var i = seriesData.length - 1; i >= 0; i--) {
                tooltipData.push({
                  name: option.yAxis.data[i],
                  value: seriesData[i]
                })
              }
              let seriesIndex = codeChart.getOption().series.findIndex(s => s.name === params.name)
              let colors = codeChart.getOption().color;
              let color = colors[seriesIndex % colors.length]
              var html = `<div style="min-width: 80px">
                            <span style="display: inline-block; width: 10px; height: 10px; border-radius: 10px; background: ${color}; margin-right: 3px"></span>
                            <strong>${params.name}</strong>
                          </div>`
              tooltipData.forEach(function (item) {
                html += `<div style="display: flex; justify-content: space-between; margin-top: 5px"><span>${item.name}:</span> <strong>${item.value}</strong></div>`
              })
              return html
            }
          }
        },
        grid: {
          top: 100,
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value'
        },
        yAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
        },
        series: series
      };
      option && codeChart.setOption(option, true);
    }
  }
};
</script>
