import request from '@/utils/request'

// 登录方法
export function login(username, password, code, uuid) {
  const data = {
    username,
    password,
    code,
    uuid
  }
  return request({
    url: '/login',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 注册方法
export function register(data) {
  return request({
    url: '/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 免登录跳转到本系统
export function  noLoginJump(tokenValue) {
  return request({
    url: '/checkToken',
    headers: {
      isToken: false
    },
    method: 'get',
    params: { tokenValue }
  })
}

// 获取印尼绩效中心跳转地址
export function getIndonesiaJixiaoUrl() {
  return request({
    url: '/getIndonesiaJixiaoUrl',
    method: 'get',
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/getInfo',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}

// 短信验证码
export function getCodeSms() {
  return request({
    url: '/captchaSms',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}

/**
 * 钉钉扫码登录
 * @param {Object} data { code: string }
 */
export function dingdingLogin(data) {
  return request({
    url: '/dingTalkLogin',
    method: 'post',
    data
  })
}
