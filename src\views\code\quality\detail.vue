<template>
  <div class="app-container">
    <p class="title">代码库（{{ $route.query.scanName }}）问题明细</p>
    <el-form :model="queryForm" ref="queryForm" size="small" :inline="true" label-width="75px">
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryForm.status" clearable>
          <el-option v-for="item in dict.type.code_quality_file_status"
            :key="item.value"
            :value="item.value"
            :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="处理人" prop="handleUser">
        <el-input v-model="queryForm.handleUser" clearable />
      </el-form-item>
      <el-form-item label="指派人" prop="assignUser">
        <el-input v-model="queryForm.assignUser" clearable />
      </el-form-item>
      <el-form-item label="处理时间">
        <el-date-picker v-model="queryForm.handleTimeBegin" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd 00:00:00"></el-date-picker>
        <span>&nbsp;&nbsp;&nbsp;&nbsp;至&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
        <el-date-picker v-model="queryForm.handleTimeEnd" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd 23:59:59"></el-date-picker>
      </el-form-item>
      <el-form-item label="指派时间">
        <el-date-picker v-model="queryForm.assignTimeBegin" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd 00:00:00"></el-date-picker>
        <span>&nbsp;&nbsp;&nbsp;&nbsp;至&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
        <el-date-picker v-model="queryForm.assignTimeEnd" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd 23:59:59"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row class="mb10">
      <el-button size="small" type="primary" @click="assignQuestions" v-hasPermi="['system:scanProjectFile:assign']" :disabled="selectedIds.length === 0">指派/更换指派</el-button>
      <el-button size="small" type="primary" @click="handleQuestions" v-hasPermi="['system:scanProjectFile:process']" :disabled="selectedIds.length === 0">处理</el-button>
    </el-row>
    <el-table :data="tableData" sizi="mini" style="width: 100%" height="calc(100vh - 340px)" v-loading="loading" stripe border
      @sort-change="handleSortChange" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="#" width="50"></el-table-column>
      <el-table-column prop="pId" label="项目id" align="center" width="90"></el-table-column>
      <el-table-column prop="scanFileUrl" label="问题文件路径" align="center" width="250"></el-table-column>
      <el-table-column prop="blockerAmount" align="center" sortable="custom" width="150">
        <template slot="header">
          严重问题<br>（master分支）
        </template>
      </el-table-column>
      <el-table-column prop="criticalAmount" align="center" sortable="custom" width="150">
        <template slot="header">
          一般问题<br>（master分支）
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" align="center" sortable="custom">
        <template slot-scope="scope">
          {{ getDictLabel(scope.row.status, dict.type.code_quality_file_status) }}
        </template>
      </el-table-column>
      <el-table-column prop="handleUser" label="处理人" align="center"></el-table-column>
      <el-table-column prop="handleTime" label="处理时间" align="center" sortable="custom"></el-table-column>
      <el-table-column prop="handleRemark" label="处理说明" align="center"></el-table-column>
      <el-table-column prop="assignUser" label="指派人" align="center"></el-table-column>
      <el-table-column prop="assignTime" label="指派时间" align="center" sortable="custom"></el-table-column>
      <el-table-column label="操作" align="center" width="150">
        <template slot-scope="scope">
          <el-button type="text" @click="showDetail(scope.row)" v-hasPermi="['system:scanProjectDetail:list']">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="fl mt10">
      <el-pagination
        background
        layout="prev, pager, next, slot, jumper, sizes, total"
        :total="total"
        :page-size="queryForm.pageSize"
        :current-page.sync="queryForm.pageNum"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <!-- 文件路径扫描报告 -->
    <el-dialog :title="`代码库（${$route.query.scanName}）文件路径（${fileDetailDialog.scanFileUrl}）扫描报告`" :visible.sync="fileDetailDialog.show" width="1600px">
      <fileDetail ref="fileDetail" :fileId="fileDetailDialog.fileId"></fileDetail>
    </el-dialog>
    <!--问题指派-->
    <assignDialog :dialogVisible.sync="assignDialog.show" :assignData="assignDialog.assignData" @callback="handleCurrentChange(1)"></assignDialog>
    <!--处理弹窗-->
    <processDialog :dialogVisible.sync="processDialog.show" :processData="processDialog.processData" @callback="handleCurrentChange(1)"></processDialog>
  </div>
</template>

<script>
import { pageGroupingFile } from "@/api/Project"
import fileDetail from './components/fileDetail.vue'
import {getDicts} from "../../../api/system/dict/data"
import assignDialog from './components/assignDialog'
import processDialog from './components/processDialog'
export default {
  name: "QualityDetail",
  components: { fileDetail, assignDialog, processDialog },
  dicts: ['code_quality_file_status'],
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      fileDetailDialog: {
        show: false,
        scanFileUrl: '',
        fileId: ''
      },
      statusOptions: [],
      queryForm: {
        pageNum: 1,
        pageSize: 50,
        orderByField: '',
        orderRule: '',
        status: '',
        handleUser: '',
        assignUser: '',
        handleTimeBegin: '',
        handleTimeEnd: '',
        assignTimeBegin: '',
        assignTimeEnd: ''
      },
      selectedIds: [],
      assignDialog: { // 问题指派弹窗
        show: false,
        assignData: {
          fileIds: []
        }
      },
      processDialog: { // 问题处理弹窗
        show: false,
        processData: {
          fileIds: []
        }
      }
    }
  },
  created() {
    this.handleQuery()
    getDicts("status").then(response => {
      this.statusOptions = response.data
    })
  },
  methods: {
    // 查询报告数据
    handleQuery () {
      this.loading = true
      pageGroupingFile({
        pageNum: this.queryForm.pageNum,
        pageSize: this.queryForm.pageSize,
        pId: this.$route.query.pId,
        orderByField: this.queryForm.orderByField,
        orderRule: this.queryForm.orderRule,
        status: this.queryForm.status,
        handleUser: this.queryForm.handleUser,
        assignUser: this.queryForm.assignUser,
        handleTimeBegin: this.queryForm.handleTimeBegin,
        handleTimeEnd: this.queryForm.handleTimeEnd,
        assignTimeBegin: this.queryForm.assignTimeBegin,
        assignTimeEnd: this.queryForm.assignTimeEnd
      }).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.tableData = res.rows
          this.total = res.total
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 改变每页显示数量
    handleSizeChange(val) {
      this.queryForm.pageSize = val
      this.handleQuery()
    },
    // 改变当前页码
    handleCurrentChange(val) {
      this.queryForm.pageNum = val
      this.handleQuery()
    },
    // 排序
    handleSortChange (data) {
      this.queryForm.orderByField = data.prop
      if (data.order === 'ascending') {
        this.queryForm.orderRule = 'asc'
      } else if (data.order === 'descending') {
        this.queryForm.orderRule = 'desc'
      } else {
        this.queryForm.orderRule = ''
      }
      this.handleCurrentChange(1)
    },
    // 重置
    resetQuery (){
      this.queryForm = {
        pageNum: 1,
        pageSize: 50,
        orderByField: '',
        orderRule: '',
        status: '',
        handleUser: '',
        assignUser: '',
        handleTimeBegin: '',
        handleTimeEnd: '',
        assignTimeBegin: '',
        assignTimeEnd: ''
      }
    },
    // 查看文件路径报告地址
    showDetail (row) {
      this.fileDetailDialog.fileId = row.id
      this.fileDetailDialog.scanFileUrl = row.scanFileUrl
      this.fileDetailDialog.show = true
      this.$nextTick(() => {
        this.$refs.fileDetail.getData()
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectedIds = selection.map(item => item.id)
    },
    // 指派/更换指派
    assignQuestions () {
      this.assignDialog.assignData = {
        fileIds: this.selectedIds
      }
      this.assignDialog.show = true
    },
    // 处理
    handleQuestions () {
      let selectedRows = this.tableData.filter(item => this.selectedIds.indexOf(item.id) >= 0)
      let firstHandleUserId = null
      for (const row of selectedRows) {
        if (!row.handleUserId) {
          this.$message.warning('处理人不能为空')
          return
        }
        if (firstHandleUserId === null) {
          firstHandleUserId = row.handleUserId
        } else if (firstHandleUserId !== row.handleUserId) {
          this.$message.warning('请选择同一处理人')
          return
        }
      }
      this.processDialog.processData = {
        fileIds: this.selectedIds
      }
      this.processDialog.show = true
    },
    // 根据value获取字典label
    getDictLabel (value, dictData) {
      const item = dictData.find(item => item.value === value)
      return item ? item.label : value
    }
  }
}
</script>
<style lang="scss" scoped>
.app-container {
  .title {
    margin: 0 auto 20px;
    font-weight: bold;
    text-align: center;
  }
}
</style>