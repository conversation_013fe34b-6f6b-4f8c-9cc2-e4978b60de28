<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="完成年份:" prop="roleName">
        <el-input
          v-model="queryParams.doneYear"
          placeholder="请输入年份"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="完成月份:" prop="roleKey">
        <el-input
          v-model="queryParams.doneMonth"
          placeholder="请输入月份"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSearch">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['business:performanceGroup:group:export']"
        >导出</el-button>
      </el-form-item>
    </el-form>

    <div style="display: inline-block; width: 100%;">
      <el-table :data="groupPerfList" style="width: 100%;" height="calc(100vh - 200px)" stripe border>
        <el-table-column   label="编号" align="center" width="40" fixed>
          <template slot-scope="scop">
            {{scop.$index+1}}
          </template>
        </el-table-column>
        <el-table-column align="center" label="年份" prop="year" width="60" fixed/>
        <el-table-column align="center" label="月份" prop="month" :show-overflow-tooltip="true" width="45" fixed/>
        <el-table-column align="center" label="组名" prop="groupName" :show-overflow-tooltip="true" width="130" fixed/>
        <el-table-column align="center" label="组内人数" prop="peopleNumber" :show-overflow-tooltip="true" width="50" fixed/>
        <el-table-column align="center" label="人员列表" prop="peopleList" :show-overflow-tooltip="true" width="340" fixed/>
        <el-table-column align="center" label="人均完成任务总数" prop="taskCount" :show-overflow-tooltip="true" width="155" sortable :sort-method="(a,b)=>{return a.taskCount - b.taskCount}"/>
        <el-table-column align="center" label="平均工作时长(小时)" prop="workTime" :show-overflow-tooltip="true" width="160" sortable :sort-method="(a,b)=>{return a.workTime - b.workTime}"/>
        <el-table-column align="center" label="每任务所耗小时" prop="elapsedTime" :show-overflow-tooltip="true" width="150" sortable :sort-method="(a,b)=>{return a.elapsedTime - b.elapsedTime}"/>
        <el-table-column align="center" label="平均每日工时(工作日)" prop="dailyWorkTime" :show-overflow-tooltip="true" width="130" sortable :sort-method="(a,b)=>{return a.dailyWorkTime - b.dailyWorkTime}"/>
        <el-table-column align="center" label="平均迟到时长(分钟)" prop="lateMinute" :show-overflow-tooltip="true" width="130" sortable :sort-method="(a,b)=>{return a.lateMinute - b.lateMinute}"/>
        <el-table-column align="center" label="总加班时长(小时)" prop="overtime" :show-overflow-tooltip="true" width="145" sortable :sort-method="(a,b)=>{return a.overtime - b.overtime}"/>
        <el-table-column align="center" label="巡检次数" prop="cycleInspectionCount" :show-overflow-tooltip="true" width="80"  sortable :sort-method="(a,b)=>{return a.cycleInspectionCount - b.cycleInspectionCount}"/>
        <el-table-column align="center" label="P0预警次数" prop="p0WarningCount" :show-overflow-tooltip="true" width="80"  sortable :sort-method="(a,b)=>{return a.p0WarningCount - b.p0WarningCount}"/>
        <el-table-column align="center" label="P1预警次数" prop="p1WarningCount" :show-overflow-tooltip="true" width="80"  sortable :sort-method="(a,b)=>{return a.p1WarningCount - b.p1WarningCount}"/>
        <el-table-column align="center" label="P2预警次数" prop="p2WarningCount" :show-overflow-tooltip="true" width="80"  sortable :sort-method="(a,b)=>{return a.p2WarningCount - b.p2WarningCount}"/>
        <el-table-column align="center" label="最大工时差(小时)" prop="groupMaxWorkTimeGap" :show-overflow-tooltip="true" width="80"  sortable :sort-method="(a,b)=>{return a.groupMaxWorkTimeGap - b.groupMaxWorkTimeGap}"/>
        <el-table-column align="center" label="平均工时小于8小时的人数" prop="groupAvgWorkTimeLtEightHourCount" :show-overflow-tooltip="true" width="100"  sortable :sort-method="(a,b)=>{return a.groupAvgWorkTimeLtEightHourCount - b.groupAvgWorkTimeLtEightHourCount}"/>
      </el-table>
      <br/>
    </div>

  </div>
</template>

<script>
import { listWorkTotal } from "@/api/business/performance";

export default {
  name: "PerformanceGroup",
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      taskList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      groupPerfList:[],
      queryParams: {
        doneYear:0,
        doneMonth:0,
      },

    };
  },
  created() {
    this.init();
  },
  methods: {
    async handleSearch() {
      if(!/^\d{4}$/.test(this.queryParams.doneYear)){
        alert("请输入正确的年份");
        return;
      }
      if (!(/(^[1-9]\d*$)/.test(this.queryParams.doneMonth))) {
        alert("请输入正确的月份");
        return
      }
      if (!(this.queryParams.doneMonth < 13)) {
        alert("请输入正确的月份");
        return
      }
      await listWorkTotal(this.queryParams).then(response => {
        this.groupPerfList = response.data
      })
      // await listDocStat(this.queryParams).then(response => {
      //   this.docStatList = response.data
      //   if (this.docStatList.length == 0) {
      //     this.docListByGroup = null
      //     return
      //   }
      //   this.queryParams.groupName = this.docStatList[0].deptName
      // })
      // await listDocByGroup(this.queryParams).then(response => {
      //   this.docListByGroup = response.data
      // })
      // await listDocCount(this.queryParams).then(response => {
      //   this.docCountList = response.data
      // })
    },

    async init() {
      await this.getNowDate()
      listWorkTotal(this.queryParams).then(response => {
        this.groupPerfList = response.data
      })
    },


    getNowDate() {
      const timeOne = new Date()
      const year = timeOne.getFullYear()
      let month = timeOne.getMonth() + 1
      this.queryParams.doneYear = year
      this.queryParams.doneMonth = month
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('/business/imsPerformStat/export', {
        ...this.queryParams
      }, `${this.queryParams.doneYear}年${this.queryParams.doneMonth}月各组绩效合计_${new Date().getTime()}.xlsx`)
    },


  }
};
</script>

<style scoped>
  .elTable {
    display: inline;
    background-color: #1ab394;
  }
</style>
