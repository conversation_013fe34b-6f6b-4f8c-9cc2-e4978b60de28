<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item label="库名" prop="dbName">
        <el-select
          v-model="queryParams.dbName"
          clearable filterable
          style="width: 240px"
        >
          <el-option :value="null" label="全部"/>
          <el-option
            v-for="item in systemOptions"
            :key="item.dbCode"
            :label="item.dbName"
            :value="item.dbCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="负责组别" prop="groupId">
        <el-select
          v-model="queryParams.groupId"
          placeholder="负责组别"
          clearable filterable
          size="small"
          style="width: 160px"
        >
          <el-option
            v-for="item in groupOptions"
            :key="item.deptId"
            :label="item.deptName"
            :value="item.deptId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="负责人员" prop="memberName">
        <el-input
          v-model="queryParams.memberName"
          placeholder="负责人员"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:dataBasePermission:add']"
        >新增</el-button>
      </el-col>
    </el-row>
    <el-table v-loading="loading" :data="tableData" height="calc(100vh - 270px)" stripe border>
      <el-table-column type="index" width="55" align="center" />
      <el-table-column label="库名" align="center" prop="dbRemark" />
      <el-table-column label="负责组别" align="center" prop="groupNames" />
      <el-table-column label="负责人员" align="center" prop="memberNames" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:dataBasePermission:edit']"
          >编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改数据库权限弹窗 -->
    <el-dialog :title="addEditDialog.title" :visible.sync="addEditDialog.show" width="450px" append-to-body @close="cancel">
      <el-form ref="form" :model="addEditDialog.form" :rules="addEditDialog.rules" label-width="80px">
        <el-form-item label="库名" prop="dbName">
          <el-select v-model="addEditDialog.form.dbName" placeholder="库名" style="width: 300px">
            <el-option
              v-for="dict in systemOptions"
              :key="dict.dbCode"
              :label="dict.dbName"
              :value="dict.dbCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="负责组别" prop="groupIds">
          <el-select
            v-model="addEditDialog.form.groupIds"
            placeholder="负责组别"
            multiple clearable filterable
            size="small"
            style="width: 300px"
            @change="handleGroupChange(true)"
          >
            <el-option
              v-for="dict in groupOptions"
              :key="dict.deptId"
              :label="dict.deptName"
              :value="dict.deptId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="负责人员" prop="memberIds">
          <el-select
            v-model="addEditDialog.form.memberIds"
            placeholder="负责人员"
            multiple clearable filterable
            size="small"
            style="width: 300px"
            :disabled="addEditDialog.form.groupIds.length === 0"
          >
            <el-option
              v-for="dict in employeeByGroup"
              :key="dict.userId"
              :label="dict.nickName"
              :value="dict.userId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="addEditDialog.btnLoading">保 存</el-button>
        <el-button @click="addEditDialog.show = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { dataBasePermissionList, dataBasePermissionDeptAndUser, dataBasePermissionAdd, dataBasePermissionEdit } from "@/api/system/dataBasePermission"
import { systemTreeSelect } from "@/api/system/slowQuery"
export default {
  name: "databasePermission",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 数据库权限表格数据
      tableData: [],
      // 数据库下拉数据
      systemOptions: [],
      // 组别下拉数据
      groupOptions: [],
      // 负责人员
      employeeDict: [],
      // 组别对应负责人员
      employeeByGroup: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dbName: null,
        groupId: null,
        memberName: null
      },
      // 新增编辑弹窗
      addEditDialog: {
        show: false,
        btnLoading: false,
        title: "",
        form: {
          id: '',
          dbName: '',
          groupIds: [],
          memberIds: []
        },
        // 表单校验
        rules: {
          dbName: [
            { required: true, message: "库名能为空", trigger: "blur" }
          ],
          groupIds: [
            { required: true, message: "负责组别不能为空", trigger: "blur" }
          ],
          memberIds: [
            { required: true, message: "负责人员不能为空", trigger: "blur" }
          ]
        }
      }
    }
  },
  created() {
    this.getList()
    this.getSystemTree()
    this.getDeptList()
  },
  methods: {
    /** 查询数据库权限列表 */
    getList () {
      this.loading = true
      dataBasePermissionList(this.queryParams).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.total = res.total
          this.tableData = res.rows
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 取消按钮
    cancel () {
      this.addEditDialog.form = {
        id: '',
        dbName: '',
        groupIds: [],
        memberIds: []
      }
      this.addEditDialog.show = false
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.addEditDialog.show = true
      this.addEditDialog.title = '新增数据库权限'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.addEditDialog.form = {
        id: row.id,
        dbName: row.dbName,
        groupIds: row.groupIds.split(','),
        memberIds: row.memberIds.split(',')
      }
      this.handleGroupChange(false)
      this.addEditDialog.show = true
      this.addEditDialog.title = '编辑数据库权限'
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          let params = {
            dbName: this.addEditDialog.form.dbName,
            dbRemark: this.systemOptions.find(item => item.dbCode === this.addEditDialog.form.dbName).dbName,
            groupIds: this.addEditDialog.form.groupIds.join(','),
            memberIds: this.addEditDialog.form.memberIds.join(','),
            groupNames: this.groupOptions.filter(item => this.addEditDialog.form.groupIds.indexOf(item.deptId) >= 0)
                        .map(item => item.deptName).join(','),
            memberNames: this.employeeByGroup.filter(item => this.addEditDialog.form.memberIds.indexOf(item.userId) >= 0)
                        .map(item => item.nickName).join(',')
          }
          this.addEditDialog.btnLoading = true
          if (this.addEditDialog.form.id !== '') {
            params.id = this.addEditDialog.form.id
            dataBasePermissionEdit(params).then(res => {
              this.addEditDialog.btnLoading = false
              if (res.code === 200) {
                this.$modal.msgSuccess("修改成功")
                this.addEditDialog.show = false
                this.getList()
              } else {
                this.$message.error(res.msg)
              }
            }).catch(error => {
              this.addEditDialog.btnLoading = false
            })
          } else {
            dataBasePermissionAdd(params).then(res => {
              this.addEditDialog.btnLoading = false
              if (res.code === 200) {
                this.$modal.msgSuccess("新增成功")
                this.addEditDialog.show = false
                this.getList()
              } else {
                this.$message.error(res.msg)
              }
            }).catch(error => {
              this.addEditDialog.btnLoading = false
            })
          }
        }
      })
    },
    /** 查询数据库下拉树结构 */
    getSystemTree() {
      systemTreeSelect().then(res => {
        if (res.code === 200) {
          this.systemOptions = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    /** 查询部门列表 */
    getDeptList() {
      dataBasePermissionDeptAndUser().then(res => {
        this.groupOptions = res
      })
    },
    // 负责组别选中
    handleGroupChange (clear) {
      if (clear) {
        this.addEditDialog.form.memberIds = []
      }
      let selectedGroupData = this.groupOptions.filter(item => this.addEditDialog.form.groupIds.indexOf(item.deptId) >= 0)
      let employeeByGroup = []
      selectedGroupData.forEach(item => {
        employeeByGroup.push(
          ...item.userList.map(userItem => {
            return {
              userId: userItem.userId,
              nickName: userItem.nickName
            }
          })
        )
      })
      this.$set(this, 'employeeByGroup', employeeByGroup)
    }
  }
}
</script>
