import request from '@/utils/request'
import { getWarnAllPageByUser } from '@/api/warn/warn.js'

export function queryUserTask(data) {
  return request({
    url: '/task/queryUserTask',
    method: 'get',
    params: data,
  })
}
export function userCommitList(data) {
  return request({
    url: '/code/query/userCommitList',
    method: 'get',
    params: data,
  })
}
export function getUserDocList(query) {
  return request({
    url: '/business/imsPerformStat/getUserDocList',
    method: 'get',
    params: query
  })
}

// 直接导出，不再使用重命名
export { getWarnAllPageByUser }

