import request from '@/utils/request'

// 查询渠道API基本信息配置列表
export function listApiBaseConfInfo(query) {
  return request({
    url: '/system/apiBaseConfInfo/list',
    method: 'get',
    params: query
  })
}

// 查询渠道API基本信息配置详细
export function getApiBaseConfInfo(id) {
  return request({
    url: '/system/apiBaseConfInfo/' + id,
    method: 'get'
  })
}

// 新增渠道API基本信息配置
export function addApiBaseConfInfo(data) {
  return request({
    url: '/system/apiBaseConfInfo',
    method: 'post',
    data: data
  })
}

// 修改渠道API基本信息配置
export function updateApiBaseConfInfo(data) {
  return request({
    url: '/system/apiBaseConfInfo',
    method: 'put',
    data: data
  })
}

// 删除渠道API基本信息配置
export function delApiBaseConfInfo(id) {
  return request({
    url: '/system/apiBaseConfInfo/' + id,
    method: 'delete'
  })
}
