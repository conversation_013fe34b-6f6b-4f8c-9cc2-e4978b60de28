<template>
    <div>
        <div class="app-container">
            <div style="padding-top: 20px; padding-left: 10px">
                <el-form :inline="true" @submit.native.prevent :model="queryForm">
                    <el-form-item label="环境：">
                        <el-select clearable placeholder="请选择" @change="handleStatusSelectChange"
                            v-model="queryForm.env">
                            <el-option v-for="item in optionsData" :key="item.key" :value="item.key"
                                :label="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="bug类型：">
                        <el-select clearable placeholder="请选择" @change="handleStatusSelectChange"
                            v-model="queryForm.type">
                            <el-option v-for="item in bugType" :key="item.key" :value="item.key" :label="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="产品：">
                        <el-input v-model="queryForm.product" />
                    </el-form-item>
                    <el-form-item label="项目：">
                        <el-input v-model="queryForm.project" />
                    </el-form-item>
                    <el-form-item label="严重程度：">
                        <el-select clearable placeholder="请选择" @change="handleStatusSelectChange"
                            v-model="queryForm.severity">
                            <el-option v-for="item in levels" :key="item.key" :value="item.key" :label="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="优先级：">
                        <el-select clearable placeholder="请选择" @change="handleStatusSelectChange"
                            v-model="queryForm.pri">
                            <el-option v-for="item in levels" :key="item.key" :value="item.key" :label="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="解决方案：">
                        <el-select clearable placeholder="请选择" @change="handleStatusSelectChange"
                            v-model="queryForm.resolution">
                            <el-option v-for="item in resolutions" :key="item.key" :value="item.key"
                                :label="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="是否确认：">
                        <el-select clearable placeholder="请选择" v-model="queryForm.confirmed">
                            <el-option label="全部" value="" />
                            <el-option label="已确认" value="1" />
                            <el-option label="未确认" value="0" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="创建人：">
                        <el-input v-model="queryForm.openedBy" />
                    </el-form-item>
                    <el-form-item label="创建时间：">
                        <el-date-picker v-model="queryForm.openedDateRange" type="daterange" range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期" @change="handleDateChange"
                            @clear="handleDateClear" clearable>
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="bug状态：">
                        <el-select clearable placeholder="请选择" v-model="queryForm.status">
                            <el-option label="全部" value="" />
                            <el-option label="激活" value="active" />
                            <el-option label="已解决" value="resolved" />
                            <el-option label="已关闭" value="closed" />
                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-button icon="el-icon-search" type="primary" @click="handleQuery">
                            查询
                        </el-button>
                        <el-button icon="el-icon-refresh" type="primary" @click="resetQuery">
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>

            <el-table v-loading="listLoading" border stripe :data="tableData" style="width: 100%"
                :default-sort="{ prop: 'openedDate', order: 'descending' }" @selection-change="handleSelectionChange"
                @sort-change="onSortChange" height="calc(100vh - 350px)" ref="tableList">

                <el-table-column label="ID" width="100" prop="id">
                    <template slot-scope="scope">
                        {{ scope.row.id }}
                    </template>
                </el-table-column>
                <el-table-column label="环境" width="100" prop="env">
                    <template slot-scope="scope">
                        {{ scope.row.env === 'test' ? '测试' : (scope.row.env === 'prod' ? '生产' : scope.row.env) }}
                    </template>
                </el-table-column>
                <el-table-column label="bug标题" width="200" prop="bugTitle">
                    <template slot-scope="scope">
                        <template v-if="scope.row.env !== 'prod'">
                            <el-link type="primary" :href="`https://pm.qmqb.top/bug-view-${scope.row.id}.html`"
                                target="_blank">
                                {{ scope.row.title }}
                            </el-link>
                        </template>
                        <template v-else>
                            {{ scope.row.title }}
                        </template>
                    </template>
                </el-table-column>
                <el-table-column label="bug类型" width="150" prop="typeName">
                    <template slot-scope="scope">
                        {{ scope.row.typeName }}
                    </template>
                </el-table-column>
                <el-table-column label="所属产品" width="150" prop="productName">
                    <template slot-scope="scope">
                        {{ scope.row.productName }}
                    </template>
                </el-table-column>
                <el-table-column label="所属项目" width="150" prop="projectName">
                    <template slot-scope="scope">
                        {{ scope.row.projectName }}
                    </template>
                </el-table-column>
                <el-table-column label="相关需求" width="150" prop="storyName">
                    <template slot-scope="scope">
                        {{ scope.row.storyName }}
                    </template>
                </el-table-column>
                <el-table-column label="影响版本" width="150" prop="openedBuildName">
                    <template slot-scope="scope">
                        {{ scope.row.openedBuildName }}
                    </template>
                </el-table-column>
                <el-table-column label="当前指派人" width="150" prop="assignedToName">
                    <template slot-scope="scope">
                        {{ scope.row.assignedToName }}
                    </template>
                </el-table-column>
                <el-table-column label="是否确认" width="100" prop="confirmed">
                    <template slot-scope="scope">
                        {{ scope.row.confirmed === 1 ? '已确认' : (scope.row.confirmed === 0 ? '未确认' : '') }}
                    </template>
                </el-table-column>
                <el-table-column label="严重程度" width="100" prop="severity" sortable>
                    <template slot-scope="scope">
                        {{ scope.row.severity }}
                    </template>
                </el-table-column>
                <el-table-column label="优先级" width="100" prop="pri" sortable>
                    <template slot-scope="scope">
                        {{ scope.row.pri }}
                    </template>
                </el-table-column>
                <el-table-column label="截止日期" width="150" prop="deadline" sortable>
                    <template slot-scope="scope">
                        {{ scope.row.deadline }}
                    </template>
                </el-table-column>
                <el-table-column label="创建人" width="150" prop="openedByName" sortable>
                    <template slot-scope="scope">
                        {{ scope.row.openedByName }}
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" width="150" prop="openedDate" sortable>
                    <template slot-scope="scope">
                        {{ scope.row.openedDate }}
                    </template>
                </el-table-column>
                <el-table-column label="bug状态" width="100" prop="status" sortable>
                    <template slot-scope="scope">
                        {{ scope.row.status === 'active' ? '激活' : (scope.row.status === 'resolved' ? '已解决' :
                            (scope.row.status === 'closed' ? '已关闭' : scope.row.status)) }}
                    </template>
                </el-table-column>
                <el-table-column label="解决人" width="150" prop="resolvedByName" sortable>
                    <template slot-scope="scope">
                        {{ scope.row.resolvedByName }}
                    </template>
                </el-table-column>
                <el-table-column label="解决方案" width="150" prop="resolutionName" sortable>
                    <template slot-scope="scope">
                        {{ scope.row.resolutionName }}
                    </template>
                </el-table-column>
                <el-table-column label="解决日期" width="150" prop="resolvedDate" sortable>
                    <template slot-scope="scope">
                        {{ scope.row.resolvedDate }}
                    </template>
                </el-table-column>

            </el-table>

            <!-- <el-pagination
          :current-page="1"
          :layout="layout"
          :page-size="50"
          :total="100"
          background
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        /> -->
        <div class="container">
            <el-pagination class="rear-page" :current-page="queryForm.pageNum" :page-size="queryForm.pageSize"
                layout="prev, pager, next, slot, jumper, sizes, total" :total="queryForm.total"
                @size-change="handleSizeChange" @current-change="handleCurrentChange">
                <!-- slot部分，跳转末页 -->
                <button class="lastPage" :disabled="queryForm.lastPageDisabled" @click="toLastPage">
                    <i class="el-icon-d-arrow-right"></i>
                </button>
            </el-pagination>
            <el-pagination class="ahead-page" :current-page="queryForm.pageNum" :page-size="queryForm.pageSize"
                layout="slot" :total="queryForm.total" @size-change="handleSizeChange"
                @current-change="handleCurrentChange">
                <!-- slot部分，跳转首页 -->
                <button class="firstPage" :disabled="queryForm.firstPageDisabled" @click="toFirstPage">
                    <i class="el-icon-d-arrow-left"></i>
                </button>
            </el-pagination>
        </div>
      </div>
    </div>
</template>

<script>
import { listBug } from "@/api/business/bugAttribution";

export default {
    name: 'Bug',
    data() {
        return {
            tableData: [], // 表格数据列表
            listLoading: false,
            groupDict: [],
            queryForm: {
                total: 0,
                pageSize: 50, //  展示数量
                firstPageDisabled: false, //  首页
                lastPageDisabled: true, //  末页
                env: '',
                type: '',
                product: '',
                project: '',
                severity: '',
                pri: '',
                resolution: '',
                confirmed: '',
                openedBy: null,
                status: '',
                orderByColumn: '',
                sort: '',
                openedDateRange: [],
                beginOpenedDate: null,
                endOpenedDate: null
            },
            optionsData: [{
                key: '',
                value: '全部'
            }, {
                key: 'test',
                value: '测试'
            }, {
                key: 'prod',
                value: '生产'
            }],
            bugType: [
                { key: '', value: '全部' },
                { key: '合作方原因', value: '合作方原因' },
                { key: '业务部门原因', value: '业务部门原因' },
                { key: '产品原因-需求未明确定义', value: '产品原因-需求未明确定义' },
                { key: '产品原因-功能缺失', value: '产品原因-功能缺失' },
                { key: '产品原因-交互设计和用户体验问题', value: '产品原因-交互设计和用户体验问题' },
                { key: '产品原因-其他', value: '产品原因-其他' },
                { key: '技术原因-开发原因-功能代码逻辑错误', value: '技术原因-开发原因-功能代码逻辑错误' },
                { key: '技术原因-开发原因-性能设计有问题', value: '技术原因-开发原因-性能设计有问题' },
                { key: '技术原因-开发原因-兼容性设计有问题', value: '技术原因-开发原因-兼容性设计有问题' },
                { key: '技术原因-开发原因-安全设计有问题', value: '技术原因-开发原因-安全设计有问题' },
                { key: '技术原因-开发原因-基础设施规划有问题', value: '技术原因-开发原因-基础设施规划有问题' },
                { key: '技术原因-开发原因-其他', value: '技术原因-开发原因-其他' },
                { key: '技术原因-测试原因-其他', value: '技术原因-测试原因-其他' },
                { key: '技术原因-运维原因-错误操作', value: '技术原因-运维原因-错误操作' },
                { key: '技术原因-运维原因-风险管理不到位', value: '技术原因-运维原因-风险管理不到位' },
                { key: '技术原因-运维原因-数据管理不到位', value: '技术原因-运维原因-数据管理不到位' },
                { key: '技术原因-运维原因-其他', value: '技术原因-运维原因-其他' },
                { key: '技术原因-数据库（DBA）原因-错误操作', value: '技术原因-数据库（DBA）原因-错误操作' },
                { key: '技术原因-数据库（DBA）原因-SQL管理不到位', value: '技术原因-数据库（DBA）原因-SQL管理不到位' },
                { key: '技术原因-数据库（DBA）原因-数据管理不到位', value: '技术原因-数据库（DBA）原因-数据管理不到位' },
                { key: '技术原因-数据库（DBA）原因-风险管理不到位', value: '技术原因-数据库（DBA）原因-风险管理不到位' },
                { key: '技术原因-数据库（DBA）原因-其他', value: '技术原因-数据库（DBA）原因-其他' },
                { key: '项目管理-项目安排不规范', value: '项目管理-项目安排不规范' },
                { key: '项目管理-项目风险管理有问题', value: '项目管理-项目风险管理有问题' },
                { key: '项目管理-项目时间管理有问题', value: '项目管理-项目时间管理有问题' },
                { key: '项目管理-项目沟通管理有问题', value: '项目管理-项目沟通管理有问题' },
                { key: '项目管理-其他', value: '项目管理-其他' }
            ],
            levels: [
                { key: '', value: '全部' },
                { key: '1', value: '1' },
                { key: '2', value: '2' },
                { key: '3', value: '3' },
                { key: '4', value: '4' }
            ],
            resolutions: [
                { key: '', value: '全部' },
                { key: 'bydesign', value: '设计如此' },
                { key: 'duplicate', value: '重复Bug' },
                { key: 'external', value: '外部原因' },
                { key: 'fixed', value: '已解决' },
                { key: 'notrepro', value: '无法重现' },
                { key: 'postponed', value: '延期处理' },
                { key: 'willnotfix', value: '不予解决' }
            ],

        }
    },
    watch: {
        // 分页 计算首页和末页
        queryForm: {
            handler(newVal) {
                let pages = Math.ceil(newVal.total / newVal.pageSize);
                if (pages === 0) {
                    // 数据(totalResult)为0
                    this.queryForm.firstPageDisabled = true; // 首页按钮是否禁用
                    this.queryForm.lastPageDisabled = true; // 末页按钮是否禁用
                } else {
                    this.queryForm.firstPageDisabled = newVal.pageNum === 1;
                    this.queryForm.lastPageDisabled = newVal.pageNum === pages;
                }
            },
            // 一进页面就执行
            immediate: true,
            deep: true,
        },
    },
    methods: {
        // 点击排序
        onSortChange(column) {
            this.queryForm.orderByColumn = 'bug.' + column.prop;
            if (column.prop == 'openedByName') {
                this.queryForm.orderByColumn = column.prop;
            } else if (column.prop == 'resolvedByName') {
                this.queryForm.orderByColumn = bug.resolved_by_name;
            } else if (column.prop == 'resolutionName') {
                this.queryForm.orderByColumn = 'bug.resolution';
            } else if (column.prop == 'resolvedDate') {
                this.queryForm.orderByColumn = 'bug.resolved_date';
            } else if (column.prop == 'openedDate') {
                this.queryForm.orderByColumn = 'bug.opened_date';
            }
            this.queryForm.isAsc = column.order === "ascending" ? 'asc' : 'desc';
            // 获取后台列表数据
            this.getList()
        },
        //  改变页码
        handlePageChange({ pageNum, pageSize }) {
            this.queryForm.pageNum = pageNum;
            this.queryForm.pageSize = pageSize;
        },

        //  前往首页
        toFirstPage() {
            this.handleCurrentChange(1);
        },

        //  前往末页
        toLastPage() {
            let max = Math.ceil(this.queryForm.total / this.queryForm.pageSize);
            this.handleCurrentChange(max);
        },

        // 选择状态时间
        handleStatusSelectChange(val) {
            console.log('val = ', val)
        },

        // 查询
        handleQuery() {
            // 检查日期范围是否为空，如果为空则重置日期值
            if (!this.queryForm.openedDateRange || this.queryForm.openedDateRange.length === 0) {
                this.queryForm.beginOpenedDate = null;
                this.queryForm.endOpenedDate = null;
            }
            this.getList();
        },

        // 重置
        resetQuery() {
            this.resetParam();
            this.handleQuery();
        },
        resetParam() {
            this.queryForm = {
                total: 0,
                pageSize: 50, //  展示数量
                firstPageDisabled: false, //  首页
                lastPageDisabled: true, //  末页
                env: '',
                type: '',
                product: '',
                project: '',
                severity: '',
                pri: '',
                resolution: '',
                confirmed: '',
                openedBy: null,
                status: null,
                orderByColumn: '',
                sort: '',
                openedDateRange: [],
                beginOpenedDate: null,
                endOpenedDate: null
            }
            this.$refs.tableList.clearSort();
            this.initOpenedDateRange();
        },
        initRouterQuery() {
            if (this.$route.query.productName) {
                this.queryForm.product = this.$route.query.productName
                delete this.$route.query.productName
            }
            if (this.$route.query.env) {
                this.queryForm.env = this.$route.query.env
                delete this.$route.query.env
            }
            if (this.$route.query.projectName) {
                this.queryForm.project = this.$route.query.projectName
                delete this.$route.query.projectName
            }
            if (this.$route.query.createTimeStart) {
                this.queryForm.beginOpenedDate = this.$route.query.createTimeStart
                this.queryForm.openedDateRange[0] = this.queryForm.beginOpenedDate
                delete this.$route.query.createTimeStart
            }
            if (this.$route.query.createTimeEnd) {
                this.queryForm.endOpenedDate = this.$route.query.createTimeEnd
                this.queryForm.openedDateRange[1] = this.queryForm.endOpenedDate
                delete this.$route.query.createTimeEnd
            }
            this.queryForm.status = this.$route.query.status
            delete this.$route.query.status

        },

        // 初始化创建时间范围为当前月
        initOpenedDateRange() {
            // 获取当前日期
            const currentDate = new Date();
            // 计算当前月的第一天
            const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
            firstDayOfMonth.setHours(0, 0, 0, 0);
            // 计算当前月的最后一天
            const lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
            lastDayOfMonth.setHours(23, 59, 59, 999);
            // 设置日期范围
            this.queryForm.openedDateRange = [firstDayOfMonth, lastDayOfMonth];
            this.queryForm.beginOpenedDate = this.formatDate(firstDayOfMonth);
            this.queryForm.endOpenedDate = this.formatDate(lastDayOfMonth, true);
        },

        // 格式化日期为字符串
        formatDate(date, isEndOfDay = false) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');

            if (isEndOfDay) {
                return `${year}-${month}-${day} 23:59:59`;
            } else {
                return `${year}-${month}-${day} 00:00:00`;
            }
        },

        // 处理日期变化
        handleDateChange() {
            if (this.queryForm.openedDateRange && this.queryForm.openedDateRange.length === 2) {
                // 设置日期范围当有值时
                this.queryForm.beginOpenedDate = this.formatDate(this.queryForm.openedDateRange[0]);
                this.queryForm.endOpenedDate = this.formatDate(this.queryForm.openedDateRange[1], true);
            } else {
                // 当日期范围被清空时，重置日期值
                this.queryForm.beginOpenedDate = null;
                this.queryForm.endOpenedDate = null;
            }
        },

        // 处理日期清空
        handleDateClear() {
            console.log('日期范围被清空');
            this.queryForm.beginOpenedDate = null;
            this.queryForm.endOpenedDate = null;
        },

        // 当前页面
        handleCurrentChange(val) {
            this.queryForm.pageNum = val
            console.log('handleCurrentChange', val)
            this.getList()
        },

        // 页数
        handleSizeChange(val) {
            this.queryForm.pageSize = val
            console.log('handleSizeChange', val)
            this.getList()
        },

        // 下拉框选择
        handleSelectionChange(val) {

        },
        // 初始化
        async getList() {
            this.listLoading = true
            this.tableData = []
            const resp = await listBug(this.queryForm)
            this.formatResource(resp.data.records)
            this.listLoading = false
            this.queryForm.total = resp.data.total * 1
            this.queryForm.pageNum = resp.data.current
            this.listLoading = false
        },
        formatResource(data) {
            if (data) {
                data.forEach((item) => {
                    let resource = {
                        id: item.id,
                        env: item.env,
                        title: item.title,
                        type: item.type,
                        typeName: item.typeName,
                        product: item.product,
                        productName: item.productName,
                        project: item.project,
                        projectName: item.projectName,
                        story: item.story,
                        storyName: item.storyName,
                        openedBuild: item.openedBuild,
                        openedBuildName: item.openedBuildName,
                        assignedTo: item.assignedTo,
                        assignedToName: item.assignedToName,
                        confirmed: item.confirmed,
                        severity: item.severity,
                        pri: item.pri,
                        deadline: item.deadline,
                        openedBy: item.openedBy,
                        openedByName: item.openedByName,
                        openedDate: item.openedDate,
                        status: item.status,
                        resolvedBy: item.resolvedBy,
                        resolvedByName: item.resolvedByName,
                        resolution: item.resolution,
                        resolutionName: item.resolutionName,
                        resolvedDate: item.resolvedDate
                    }
                    this.tableData.push(resource)
                })
            }
        },

        // 双击table表行
        handleTableRow(row) {
            const url = 'http://pm.qmqb.top/task-view-' + row.id + '.html/'
            console.log('url = ', url)
            window.open(url, "_blank");
        }
    },
    activated() {
        if (this.$route.fullPath !== this.$route.path) {
            this.resetParam();
            this.initRouterQuery();
        }
        this.getList()
    },
    created() {
        this.initOpenedDateRange();
        this.getList();
    },
    mounted() {
    }

}
</script>

<style lang=scss scoped>
.app-container {
    padding: 20px;
}

.table {
    background-color: #fff;

    .tiltle-cn {
        color: #2184d8;
    }

    .control-cell {
        display: flex;
        flex-direction: row;

        .control-cell-item {
            margin-left: 10px;
        }
    }
}

.container {
    float: left;
}

.el-pagination {
    float: right;
    margin-top: 10px;
}

.el-pagination.ahead-page {
    padding-right: 0;
}

.el-pagination.rear-page {
    padding-left: 0;
}

.firstPage,
.lastPage {
    background-color: white;
    cursor: pointer;
}
</style>
