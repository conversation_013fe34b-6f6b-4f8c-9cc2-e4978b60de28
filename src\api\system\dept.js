import request from '@/utils/request'

// 查询部门列表
export function listDept(query) {
  return request({
    url: '/system/dept/list',
    method: 'get',
    params: query
  })
}

// 获取部门列表--拥有全部部门数据权限
export function getListDept(query) {
  return request({
    url: '/system/dept/getlist',
    method: 'get',
    params: query
  })
}

// 查询部门列表（排除节点）
export function listDeptExcludeChild(deptId) {
  return request({
    url: '/system/dept/list/exclude/' + deptId,
    method: 'get'
  })
}

// 查询部门详细
export function getDept(deptId) {
  return request({
    url: '/system/dept/' + deptId,
    method: 'get'
  })
}

// 新增部门
export function addDept(data) {
  return request({
    url: '/system/dept',
    method: 'post',
    data: data
  })
}

// 修改部门
export function updateDept(data) {
  return request({
    url: '/system/dept',
    method: 'put',
    data: data
  })
}

// 删除部门
export function delDept(deptId) {
  return request({
    url: '/system/dept/' + deptId,
    method: 'delete'
  })
}

/**
 * 通过部门类型获取部门列表
 * @param deptType 1开发、2测试、3运维、4项目管理、5数据管理,不传值默认查开发
 * @returns {*}
 */
export function getListByType(deptType) {
  return request({
    url: '/system/dept/listByType',
    method: 'get',
    params: deptType
  })
}
