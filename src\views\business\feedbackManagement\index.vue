<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="项管审核状态" prop="projectManagerAuditStatus">
        <el-select v-model="queryParams.projectManagerAuditStatus" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.performance_enent_audit_status" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="最终审核状态" prop="finalAudit">
        <el-select v-model="queryParams.finalAudit" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.performance_enent_audit_status" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="数据来源" prop="dataSource">
        <el-select v-model="queryParams.dataSource" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.performance_feedback_data_source" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="提交状态" prop="submitStatus">
        <el-select v-model="queryParams.submitStatus" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.performance_feedback_submit_status" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="feedbackTimeRange">
        <el-date-picker v-model="queryParams.feedbackTimeRange" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
          :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="事件发生时间" prop="eventTimeRange">
        <el-date-picker v-model="queryParams.eventTimeRange" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
          :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="提交时间" prop="submitTimeRange">
        <el-date-picker v-model="queryParams.submitTimeRange" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
          :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery(1)">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-button type="primary" size="mini" @click="handleAdd" v-hasPermi="['system:performanceFeedbackMain:add']">添加</el-button>
      <el-button type="primary" size="mini" @click="batchSubmit" :disabled="selectedIds.length === 0" v-hasPermi="['system:performanceFeedbackMain:batchSubmit']">批量提交</el-button>
    </el-row>
    <el-table v-loading="loading" :data="tableData" @selection-change="handleSelectionChange" @sort-change="handleSortChange" height="calc(100vh - 320px)" stripe border>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="#" width="55" align="center" />
      <el-table-column align="center" label="反馈编码" prop="feedbackCode">
        <template slot-scope="scope">
          <span :class="{ 'red-feedback-code': isOnlyLowPerformance(scope.row.performanceDistribution) }">
            {{ scope.row.feedbackCode }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="createByCn" />
      <el-table-column label="创建时间" align="center" prop="feedbackTime" min-width="100" sortable="custom" />
      <el-table-column label="事件标题" align="center" prop="eventTitle" min-width="160" />
      <el-table-column label="事件明细" align="center" prop="eventDetail" min-width="180">
        <template slot-scope="scope">
          <div v-if="!scope.row.eventDetail || scope.row.eventDetail.length < 30">
            {{ scope.row.eventDetail }}
          </div>
          <el-tooltip v-else :content="scope.row.eventDetail" placement="top">
            <div class="ellipsis-multiline">
              {{ scope.row.eventDetail }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="事件发生时间" align="center" min-width="160">
        <template slot-scope="scope">
          <span v-if="scope.row.eventStartTime">{{ scope.row.eventStartTime }}</span>
          <span v-if="scope.row.eventEndTime">-{{ scope.row.eventEndTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="绩效分布情况" align="center" prop="performanceDistribution" min-width="100">
        <template slot-scope="scope">
          <div class="cell-content">{{ scope.row.performanceDistribution }}</div>
        </template>
      </el-table-column>
      <el-table-column label="数据来源" align="center" prop="dataSource">
        <template slot-scope="scope">
          {{ getDictLabel(scope.row.dataSource, dict.type.performance_feedback_data_source) }}
        </template>
      </el-table-column>
      <el-table-column label="提交状态" align="center" prop="submitStatus" min-width="100" sortable="custom">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.submitStatus === 'NOT_SUBMITTED'" type="info" effect="dark">
            {{ getDictLabel(scope.row.submitStatus, dict.type.performance_feedback_submit_status) }}
          </el-tag>
          <el-tag v-if="scope.row.submitStatus === 'SUBMITTED'" type="success" effect="dark">
            {{ getDictLabel(scope.row.submitStatus, dict.type.performance_feedback_submit_status) }}
          </el-tag>
          <el-tag v-if="scope.row.submitStatus === 'PENDING_RESUBMIT'" type="danger" effect="dark">
            {{ getDictLabel(scope.row.submitStatus, dict.type.performance_feedback_submit_status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="提交人" align="center" prop="submitterName" min-width="100" />
      <el-table-column label="提交时间" align="center" prop="submitTime" min-width="100" sortable="custom" />
      <el-table-column label="项管审核状态" align="center" prop="projectManagerAuditStatus" min-width="130" sortable="custom">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.projectManagerAuditStatus === 'AUDITED'" type="warning" effect="dark">
            {{ getDictLabel(scope.row.projectManagerAuditStatus, dict.type.performance_enent_audit_status) }}
          </el-tag>
          <el-tag v-if="scope.row.projectManagerAuditStatus === 'NOT_AUDITED'" type="danger" effect="dark">
            {{ getDictLabel(scope.row.projectManagerAuditStatus, dict.type.performance_enent_audit_status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="最终审核状态" align="center" prop="finalAudit" min-width="130" sortable="custom">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.finalAudit === 'AUDITED'" type="warning" effect="dark">
            {{ getDictLabel(scope.row.finalAudit, dict.type.performance_enent_audit_status) }}
          </el-tag>
          <el-tag v-if="scope.row.finalAudit === 'NOT_AUDITED'" type="danger" effect="dark">
            {{ getDictLabel(scope.row.finalAudit, dict.type.performance_enent_audit_status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120" fixed="right" class-name="small-padding fixed-width">
        <template v-slot:default="scope">
          <el-button size="mini" type="text" @click="handleEdit(scope.row)" v-hasPermi="['system:performanceFeedbackMain:edit']"
            :disabled="scope.row.submitStatus !== 'NOT_SUBMITTED'">编辑</el-button><!--已提交数据不可编辑-->
          <el-button size="mini" type="text" @click="toDetail(scope.row)" :disabled="scope.row.submitStatus === 'NOT_SUBMITTED'">查看</el-button>
          <el-button size="mini" type="text" class="text-danger" @click="handleRemove(scope.row)"  v-hasPermi="['system:performanceFeedbackMain:remove']"
            :disabled="scope.row.submitStatus !== 'NOT_SUBMITTED'">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加编辑反馈弹窗 -->
    <addEditFeedbackDialog :dialogVisible.sync="addEditFeedbackDialog.show" :dialogTitle="addEditFeedbackDialog.title" :feedbackData="addEditFeedbackDialog.feedbackData" @callback="handleQuery(1)"></addEditFeedbackDialog>
  </div>
</template>

<script>
import { performanceEventList, feedbackBatchSubmit, performanceEventDelete, performanceEventDetail } from "@/api/business/performanceFeedback"
import addEditFeedbackDialog from './components/addEditFeedbackDialog.vue'
export default {
  name: "FeedbackManagement",
  dicts: [
    'performance_enent_audit_status',
    'performance_feedback_data_source',
    'performance_feedback_submit_status'
  ],
  components: {
    addEditFeedbackDialog
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 数据库权限表格数据
      tableData: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectManagerAuditStatus: null,
        finalAudit: null,
        dataSource: null,
        submitStatus: null,
        feedbackTimeRange: [], // 创建时间
        eventTimeRange: [], // 事件发生时间
        submitTimeRange: [] // 提交时间
      },
      selectedIds: [], // 选择行id
      addEditFeedbackDialog: { // 添加编辑反馈弹窗
        show: false,
        title: '',
        feedbackData: {}
      }
    }
  },
  created () {
    this.queryParams.feedbackTimeRange = this.getMonthRange()
    this.handleQuery()
  },
  methods: {
    /** 查询数据库权限列表 */
    getList () {
      if (this.queryParams.feedbackTimeRange && this.queryParams.feedbackTimeRange.length !== 0) { // 创建时间
        this.queryParams.feedbackTimeBegin = this.queryParams.feedbackTimeRange[0]
        this.queryParams.feedbackTimeEnd = this.queryParams.feedbackTimeRange[1]
      } else {
        this.queryParams.feedbackTimeBegin = null
        this.queryParams.feedbackTimeEnd = null
      }
      if (this.queryParams.eventTimeRange && this.queryParams.eventTimeRange.length !== 0) { // 事件发生时间
        this.queryParams.eventStartTime = this.queryParams.eventTimeRange[0]
        this.queryParams.eventEndTime = this.queryParams.eventTimeRange[1]
      } else {
        this.queryParams.eventStartTime = null
        this.queryParams.eventEndTime = null
      }
      if (this.queryParams.submitTimeRange && this.queryParams.submitTimeRange.length !== 0) { // 提交时间
        this.queryParams.submitTimeBegin = this.queryParams.submitTimeRange[0]
        this.queryParams.submitTimeEnd = this.queryParams.submitTimeRange[1]
      } else {
        this.queryParams.submitTimeBegin = null
        this.queryParams.submitTimeEnd = null
      }
      let params = {...this.queryParams}
      delete params.feedbackTimeRange
      delete params.eventTimeRange
      delete params.submitTimeRange
      this.loading = true
      performanceEventList(params).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.total = res.total
          this.tableData = res.rows
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    /** 搜索按钮操作 */
    handleQuery (page) {
      this.queryParams.pageNum = page || 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    /** 根据value获取字典label */
    getDictLabel (value, dictData, valueKey, labelKey) {
      let dictValueKey = valueKey || 'value'
      let dictLabelKey = labelKey || 'label'
      const item = dictData.find(item => item[dictValueKey] === value)
      return item ? item[dictLabelKey] : value
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.selectedIds = selection.map(item => item.id)
    },
    /** 添加 */
    handleAdd () {
      this.addEditFeedbackDialog.show = true
      this.addEditFeedbackDialog.title = '添加'
      this.addEditFeedbackDialog.feedbackData = {}
    },
    /** 批量提交 */
    batchSubmit () {
      let selectedRows = this.tableData.filter(item => this.selectedIds.indexOf(item.id) >= 0)
      let flag = true
      selectedRows.forEach(rowItem => {
        if (rowItem.submitStatus === 'SUBMITTED') {
          flag = false
          return
        }
      })
      if (!flag) {
        this.$modal.msgError('仅限于未提交或待重提状态的数据可提交')
        return
      }
      this.$modal.confirm('是否确认批量提交？').then(() => {
        this.loading = true
        return feedbackBatchSubmit({ids: this.selectedIds})
      }).then(() => {
        this.loading = false
        this.handleQuery()
        this.$modal.msgSuccess("提交成功")
      }).catch(() => {
      }).finally(() => {
        this.loading = false
      })
    },
    /** 编辑 */
    handleEdit (row) {
      performanceEventDetail({
        id: row.id
      }).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.addEditFeedbackDialog.show = true
          this.addEditFeedbackDialog.title = '编辑'
          this.addEditFeedbackDialog.feedbackData = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    /** 查看 */
    toDetail (row) {
      this.$router.push({
        name: 'FeedbackManagementDetail',
        params: {
          params: JSON.stringify({
            eventId: row.id
          })
        }
      })
    },
    /** 删除 */
    handleRemove (row) {
      this.$modal.confirm('是否确认删除？').then(() => {
        return performanceEventDelete([row.id])
      }).then(() => {
        this.handleQuery()
        this.$modal.msgSuccess("删除成功")
      })
    },
    /** 排序 */
    handleSortChange (column, prop, order) {
      this.queryParams.orderByColumn = column.prop
      this.queryParams.isAsc = column.order
      this.handleQuery()
    },
    /** 获取当月时间范围 */
    getMonthRange () {
      const now = new Date()
      // 获取当月第一天
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
      // 获取下个月第一天，然后减1毫秒得到当月最后一天的 23:59:59
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1)
      endOfMonth.setTime(endOfMonth.getTime() - 1)
      // 格式化函数
      function formatDate(date) {
        const y = date.getFullYear();
        const m = String(date.getMonth() + 1).padStart(2, '0')
        const d = String(date.getDate()).padStart(2, '0')
        const h = String(date.getHours()).padStart(2, '0')
        const mm = String(date.getMinutes()).padStart(2, '0')
        const s = String(date.getSeconds()).padStart(2, '0')
        return `${y}-${m}-${d} ${h}:${mm}:${s}`
      }
      return [formatDate(startOfMonth), formatDate(endOfMonth)]
    },
    /** 判断绩效分布是否只有C或D级 */
    isOnlyLowPerformance (performanceDistribution) {
      if (!performanceDistribution) {
        return false;
      }
      // 解析绩效分布字符串，提取绩效级别
      const distributionText = performanceDistribution.toString();
      const hasS = distributionText.includes('S');
      const hasA = distributionText.includes('A');
      const hasC = distributionText.includes('C');
      const hasD = distributionText.includes('D');

      // 如果只有C或D级，没有S或A级，则返回true
      return (hasC || hasD) && !hasS && !hasA;
    }
  }
}
</script>
<style scoped>
.cell-content {
  white-space: pre-wrap; /* 或者使用 normal, pre, nowrap, pre-wrap, pre-line, break-spaces */
  word-break: break-all; /* 或者使用 keep-all, break-all */
}

.red-feedback-code {
  color: #f56c6c;
  font-weight: bold;
}
</style>
<style>
/* 多行省略核心样式 */
.ellipsis-multiline {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3; /* 控制显示行数 */
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5; /* 与行高保持一致 */
  max-height: calc(3 * 1.5em); /* 行高*行数 */
  word-break: break-all; /* 允许单词内断行 */
}
.el-tooltip__popper {
  max-width: 600px; /* 可根据实际情况调整 */
}
</style>
