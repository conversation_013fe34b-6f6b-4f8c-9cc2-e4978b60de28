<template>
    <div class="app-container">
        <el-form :model="queryForm" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
            <el-form-item label="">
                <span>创建时间&nbsp;&nbsp;&nbsp;</span>
                <el-date-picker v-model="queryForm.createTimeStart" type="date" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd 00:00:00"></el-date-picker>
                <span>&nbsp;&nbsp;&nbsp;&nbsp;至&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                <el-date-picker v-model="queryForm.createTimeEnd" type="date" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd 23:59:59"></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button icon="el-icon-search" type="primary" @click="handleQuery">查询</el-button>
                <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
                <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
            </el-form-item>
            <el-form-item>
                <el-tag effect="plain" type="info" size="medium"> 最后更新时间：{{ updateTime }}</el-tag>
            </el-form-item>
        </el-form>
        <!-- 新增显示框 -->
        <div style="margin-bottom: 20px; text-align: center;">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-card shadow="hover" style="width: 100%;">
                        <div style="font-size: 18px;text-align: center; margin-top: 35px;">
                            生产环境
                        </div>
                        <div style="font-size: 18px;text-align: center;">bug总量： {{ productionTotal }}</div>
                        <div style="margin-top: 35px;"></div>
                    </el-card>
                </el-col>
                <el-col :span="12">
                    <el-card shadow="hover" style="width: 100%;">
                        <div style="font-size: 18px;text-align: center; margin-top: 35px;">
                            测试环境
                        </div>
                        <div style="font-size: 18px;text-align: center;">bug总量： {{ testingTotal }}</div>
                        <div style="margin-top: 35px;"></div>
                    </el-card>
                </el-col>
            </el-row>
        </div>
        <el-table :data="tableData" sizi="mini" style="width: 100%" v-loading="loading" stripe
            :default-sort="{ prop: 'total', order: 'descending' }">
            <el-table-column prop="typeName" label="bug类型" width="300"></el-table-column>
            <el-table-column prop="total" label="各类型bug总量" sortable
                :sort-method="(a, b) => { return a.total - b.total }">
                <template slot-scope="scope">
                    <span
                        v-if="scope.row.total !== 0">{{ scope.row.total }}</span>
                    <span v-else>0</span>
                </template>
            </el-table-column>
            <el-table-column prop="prodTotal" label="生产环境" sortable
                :sort-method="(a, b) => { return a.prodTotal - b.prodTotal }">
                <template slot-scope="scope">
                    <span
                        v-if="scope.row.prodTotal !== 0">{{ scope.row.prodTotal }}</span>
                    <span v-else>0</span>
                </template>
            </el-table-column>
            <el-table-column prop="testTotal" label="测试环境" sortable
                :sort-method="(a, b) => { return a.testTotal - b.testTotal }">
                <template slot-scope="scope">
                    <span
                        v-if="scope.row.testTotal !== 0">{{ scope.row.testTotal }}</span>
                    <span v-else>0</span>
                </template>
            </el-table-column>

        </el-table>
    </div>
</template>

<script>
import * as echarts from 'echarts';
import { statisticByType } from '@/api/business/bugAttribution';

export default {
    name: 'ByCount',
    data() {
        return {
            chart: null,
            // 遮罩层
            loading: false,
            // 显示搜索条件
            showSearch: true,
            // 查询参数
            queryForm: {
                createTimeStart: '',
                createTimeEnd: ''
            },
            tableData: [],
            updateTime: null,
            productionTotal: 0,
            testingTotal: 0
        };
    },
    mounted() {

    },
    created() {
        this.initTime();
        this.handleQuery();
    },
    methods: {
        // 处理时间
        getFormatDate(date) {
            var month = date.getMonth() + 1
            var strDate = date.getDate()
            if (month >= 1 && month <= 9) {
                month = '0' + month
            }
            if (strDate >= 0 && strDate <= 9) {
                strDate = '0' + strDate
            }
            var currentDate = date.getFullYear() + '-' + month + '-' + strDate + ' ' + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds()
            return currentDate
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.getStatisticsLine();
        },
        async getStatisticsLine() {
            this.loading = true;
            try {
                const response = await statisticByType(this.queryForm);
                const data = response.data;
                this.productionTotal = data.prodTotal;
                this.testingTotal = data.testTotal;
                this.tableData = data.list
                this.updateTime = data.lastUpdateTime;
            } catch (error) {
                console.error('Error fetching bug statistics:', error);
            } finally {
                this.loading = false;
            }
        },
        initTime() {
          const currentDate = new Date();
          const createTimeEnd = this.getFormatDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)).substr(0, 11) + '23:59:59'
          const createTimeStart = this.getFormatDate(new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)).substr(0, 11) + '00:00:00'
          this.queryForm = {
                createTimeStart: createTimeStart,
                createTimeEnd: createTimeEnd
            }
        },
        // 重置
        resetQuery() {
            this.initTime();
            this.queryForm = {
                createTimeStart: '',
                createTimeEnd: ''
            }
            this.handleQuery();
        },
        // 导出按钮操作
        handleExport() {
          this.download('bug/statisticByType/export', {
            ...this.queryForm
          }, `按bug类型统计bug数量_${new Date().getTime()}.xlsx`)
        },
        getChartOption(data = { production: [], testing: [] }) {
            return {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow',
                    },
                },
                legend: {
                    data: ['生产环境', '测试环境'],
                },
                xAxis: {
                    type: 'value',
                },
                yAxis: {
                    type: 'category',
                    data: ['已关闭', '已解决', '激活'],
                },
                series: [
                    {
                        name: '生产环境',
                        type: 'bar',
                        data: data.production,
                    },
                    {
                        name: '测试环境',
                        type: 'bar',
                        data: data.testing,
                    },
                ],
            };
        },
    },
    beforeDestroy() {
        if (this.chart) {
            this.chart.dispose();
        }
    },
};
</script>

<style scoped>
/* ...existing code... */
</style>
