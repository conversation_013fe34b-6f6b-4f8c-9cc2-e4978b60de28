<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="业务类型名称" prop="businessTypeName">
        <el-input v-model="queryParams.businessTypeName" placeholder="请输入业务类型名称" clearable />
      </el-form-item>
      <el-form-item label="所属业务大类" prop="businessCategoryMajor">
        <el-select v-model="queryParams.businessCategoryMajor" clearable filterable >
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.project_outcome_business_category_major" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="所属业务小类" prop="businessCategoryMinor">
        <el-select v-model="queryParams.businessCategoryMinor" clearable filterable >
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.project_outcome_business_category_minor" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="业务负责人" prop="businessManager">
        <el-input v-model="queryParams.businessManager" placeholder="请输入业务负责人" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-button type="primary"  @click="handleAdd">新增</el-button>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="tableData" ref="businessTable" @sort-change="handleSortChange" height="calc(100vh - 320px)" stripe border>
      <el-table-column label="业务类型名称" align="center" prop="businessTypeName" width="350px" />
      <el-table-column label="所属业务大类" align="center" prop="businessCategoryMajor">
        <template slot-scope="scope">
          {{ getDictLabel(scope.row.businessCategoryMajor, dict.type.project_outcome_business_category_major) }}
        </template>
      </el-table-column>
      <el-table-column label="所属业务小类" align="center" prop="businessCategoryMinor">
        <template slot-scope="scope">
          {{ getDictLabel(scope.row.businessCategoryMinor, dict.type.project_outcome_business_category_minor) }}
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sort" />
      <el-table-column label="业务负责人" align="center" prop="businessManager"  width="120px" />
      <el-table-column label="操作" align="center" width="200px">
        <template v-slot:default="scope">
          <el-button size="mini" type="text" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="text" style="color: #F56C6C;" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增编辑弹窗 -->
    <el-dialog :title="addEditDialog.title" :visible.sync="addEditDialog.show" width="600px" @close="handleDialogClose">
      <el-form ref="form" :model="formData" :rules="rules" label-width="120px">
        <el-form-item label="业务类型名称" prop="businessTypeName">
          <el-input v-model="formData.businessTypeName" placeholder="请输入业务类型名称" clearable />
        </el-form-item>
        <el-form-item label="所属业务大类" prop="businessCategoryMajor">
          <el-select v-model="formData.businessCategoryMajor" clearable filterable placeholder="请选择业务大类">
            <el-option v-for="item in dict.type.project_outcome_business_category_major" :key="item.value" :value="item.value" :label="item.label"/>
          </el-select>
        </el-form-item>
        <el-form-item label="所属业务小类" prop="businessCategoryMinor">
          <el-select v-model="formData.businessCategoryMinor" clearable filterable placeholder="请选择业务小类" >
            <el-option v-for="item in dict.type.project_outcome_business_category_minor" :key="item.value" :value="item.value" :label="item.label"/>
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model="formData.sort" placeholder="请输入排序" clearable />
        </el-form-item>
        <el-form-item label="业务负责人" prop="businessManager">
          <el-input v-model="formData.businessManager" placeholder="请输入业务负责人" clearable />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm">保存</el-button>
                 <el-button @click="handleDialogClose">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { businessTypeList, businessTypeDelete, businessTypeAdd, businessTypeEdit } from "@/api/project/businessType"

export default {
  name: "BusinessType",
  dicts: [
    'project_outcome_business_category_major',
    'project_outcome_business_category_minor'
  ],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 业务类型表格数据
      tableData: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 100,
        businessTypeName: '',
        businessCategoryMajor: '',
        businessCategoryMinor: '',
        businessManager: ''
      },
      addEditDialog: {
        show: false,
        title: '新增业务类型'
      },
      // 弹窗表单数据
      formData: {
        id: '',
        businessTypeName: '',
        businessCategoryMajor: '',
        businessCategoryMinor: '',
        sort: '',
        businessManager: ''
      },
      // 表单验证规则
      rules: {
        businessTypeName: [
          { required: true, message: '请输入业务类型名称', trigger: 'blur' }
        ],
        businessCategoryMajor: [
          { required: true, message: '请选择所属业务大类', trigger: 'blur' }
        ],
        businessCategoryMinor: [
          { required: true, message: '请选择所属业务小类', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请选择排序', trigger: 'blur' }
        ],
        businessManager: [
          { required: true, message: '请输入业务负责人', trigger: 'blur' }
        ]
      },
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询业务类型列表 */
    getList() {
      this.loading = true
      businessTypeList(this.queryParams).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.total = res.total
          this.tableData = res.rows
        } else {
          this.$message.error(res.msg)
        }
      }).catch(() => {
        this.loading = false
        this.$message.error('获取数据失败')
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    /** 根据value获取字典label */
    getDictLabel(value, dictData) {
      if (!value || !dictData) return ""
      const item = dictData.find(item => item.value === value)
      return item ? item.label : value
    },
    /** 添加 */
    handleAdd() {
      this.formData = {}
      this.addEditDialog.show = true
      this.addEditDialog.title = '新增业务类型'
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    /** 编辑 */
    handleEdit(row) {
      this.formData = { ...row }
      this.addEditDialog.show = true
      this.addEditDialog.title = '编辑业务类型'
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    /** 删除 */
    handleDelete(row) {
      this.$confirm('确认删除该业务类型吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        businessTypeDelete(row.id).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.handleQuery()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    /** 排序 */
    handleSortChange(column, prop, order) {
      this.queryParams.orderByColumn = column.prop
      this.queryParams.isAsc = column.order
      this.handleQuery()
    },
        /** 弹窗关闭处理 */
    handleDialogClose() {
      this.addEditDialog.show = false
      this.formData = {}
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    /** 保存 */
    confirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          let apiName = businessTypeAdd
          if (this.formData.id) {
            apiName = businessTypeEdit
          }
          apiName(this.formData).then(res => {
            if (res.code === 200) {
              this.$message.success(res.msg)
              this.handleDialogClose()
              this.handleQuery()
            } else {
              this.$message.error(res.msg)
            }
          }).catch(() => {
            this.$message.error('保存失败')
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
