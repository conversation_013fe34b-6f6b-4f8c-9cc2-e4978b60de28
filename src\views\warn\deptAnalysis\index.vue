<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryParams" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="">
        <span>时间（默认一周）&nbsp;&nbsp;&nbsp;</span>
        <el-date-picker
          v-model="queryParams.startTime"
          type="date"

          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd 00:00:00"
        >
        </el-date-picker>
        <span>&nbsp;&nbsp;&nbsp;&nbsp;至&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
      </el-form-item>
      <el-form-item label="">
        <el-date-picker
          v-model="queryParams.endTime"
          type="date"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd 23:59:59"
          >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="预警级别" prop="warnLevel">
        <el-select v-model="queryParams.warnLevel" placeholder="请选择预警级别">
          <el-option :value="null" label="全部"></el-option>
          <el-option value="0" label="P0"></el-option>
          <el-option value="1" label="P1"></el-option>
          <el-option value="2" label="P2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否处理" prop="handleStatus">
        <el-select v-model="queryParams.handleStatus" placeholder="请选择是否处理">
          <el-option :value="null" label="全部"></el-option>
          <el-option value="0" label="否"></el-option>
          <el-option value="2" label="是"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery()">查询</el-button>

      </el-form-item>
    </el-form>
    <!--为echarts准备一个具备大小的容器dom-->
    <div id="chart" style="width: 1650px;height: 600px;"></div>
    <el-header style="weight: 100%;height:500px;">
      <statis-header ref="header" :codeTeamData="codeTeamData" />
    </el-header>
  </div>

</template>

<script>
import {statistics, statisticsDept} from "@/api/gitcode";
import {listHistogram,getWarnGroupSelets} from "@/api/warn/deptAnalysis";
import * as echarts from 'echarts/core';
import {GridComponent, LegendComponent, TitleComponent, ToolboxComponent, TooltipComponent} from 'echarts/components';
import {LineChart} from 'echarts/charts';
import {UniversalTransition} from 'echarts/features';
import {CanvasRenderer} from 'echarts/renderers';
// import StatisHeader from "./StatisHeader";
import StatisHeader from "../../warn/deptAnalysis/StatisHeader";
import StatisFooter from "../../code/statistics/components/StatisFooter";
import StatisMiddle from "../../code/statistics/components/StatisMiddle";
import {getDicts} from "../../../api/system/dict/data";

echarts.use([
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  LineChart,
  CanvasRenderer,
  UniversalTransition
]);

var codeChart;

export default {
  name: "deptAnalysis",
  components: {
    StatisHeader
  },
  data() {
    return {
      // 团队数据
      codeTeamData: [],
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 查询参数
      queryParams: {
        deptName: null,
        warnLevel: null,
        handleStatus: null,
        startTime: '',
        endTime: '',
      },
      // 组别
      optionsGroupData: [],
      // 查询参数
      groupDict:[],
      yearList: [],
      deptList: [],
      changeTimeS: '',
      changeTimeE: ''
    };
  },
  created () {
    this.getYearList();
    this.init();
    this.initTime();
    // this.getDeptList();
    this.getWarnGroupSelets();
    this.getStatisticsLine();
    this.getList()
  },


  methods: {
    /** 查询年份列表 */
    getYearList() {
      this.loading = true;
      var date = new Date();
      var year = date.getFullYear();
      //起始年份
      var startYear = year - 3;
      //结束年份
      var endYear = year;
      var years = [];
      for (var i = startYear; i <= endYear; i++) {
        var obj = {
          label: i,
          value: i,
        }
        years.push(obj);
      }
      this.yearList = years;
      this.queryParams.year = year;

      this.loading = false;
    },
    filterMethod (val) {
      this.queryParams.deptId = val

    },
    /** 查询预警级别下拉框 */
    getWarnGroupSelets(){
      getWarnGroupSelets().then(response => {
        this.optionsGroupData = response.data;
      });
    },
    /** 查询部门列表 */
    // getDeptList() {
    //   this.loading = true;
    //   var deptList = [];
    //   statisticsDept().then(response => {
    //     deptList = response.data;
    //     this.deptList = deptList;
    //     this.queryParams.deptId = deptList[0].deptId;
    //     this.getStatisticsLine()
    //     this.loading = false;
    //   });
    // },
    /** 查询年度数据 */
    getStatisticsLine() {

      this.loading = true;
      listHistogram(this.queryParams).then(response => {
          this.loadChart(response.data.multipleRecords);
          this.codeTeamData = response.data.singleRecords
          this.loading = false;
        }
      );
    },
    async init() {
      await getDicts("group").then(response => {
        this.groupDict = response.data
        this.groupDict.shift()
      })

    },
    getList(){

      listHistogram(this.queryParams).then(response => {

        // this.codeTeamData = response.data.personalRecordList
        // this.codeFrontData = response.data.unHandlerRecordList
        //
        this.$refs.header.parentMsg(this.codeTeamData);
        // this.$refs.footer.parentMsg(this.codeFrontData);
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {



      this.getStatisticsLine();
      this.getList()
    },

    /** 加载折线图 */
    loadChart(data) {
      // const newData = this.reduceData(data)
      let deptNameArr = [] // 组名
      let deptDataArr = [] // 组的数据
      if (data !== undefined) {
        for (let i = 0; i < data.length; i++) {
          deptNameArr.push(data[i].deptName)
          var obj = {};
          obj.deptName = data[i].deptName;
          obj.P0 = data[i].levelRecords[0].num;
          obj.P1 = data[i].levelRecords[1].num;
          obj.P2 = data[i].levelRecords[2].num;
          deptDataArr.push(obj);
        }
      }
      var chartDom = document.getElementById('chart');
      // 如果初始化过 echarts 实例，销毁。
      if (codeChart != null && codeChart != "" && codeChart != undefined) {
        codeChart.dispose();
      }
      // 基于准备好的 dom ，重新初始化 echarts 实例
      codeChart = echarts.init(chartDom);
      var option;
      var series = [];
      var legendData = [];
      var subSeries = [];
      var dimensions = [];

      data.forEach((element) => {
        var obj = {
          name: element.deptName,
        }
        legendData.push(obj);
      })

      dimensions.push('deptName');
      dimensions.push('P0');
      dimensions.push('P1');
      dimensions.push('P2');



      option = {
        title: {
          text: "部门下各组预警情况",
          // left: 'center'
        },
        legend: {
        },
        tooltip: {},
        dataset: {
          dimensions: dimensions,
          source: deptDataArr
        },
        xAxis: {
          type: 'category',
          data: deptNameArr,
          axisLabel: {
            show: true,
            interval: 0,
            formatter: function (value) {
              //x轴的文字改为竖版显示
              if(value.indexOf("Android") > -1){
                var str = value.substring(0,7)
                return str;
              }
              var str = value.split("");
              return str.join("\n");
            }
          }
        },
        yAxis: {
          type: 'value'
        },
        // Declare several bar series, each will be mapped
        // to a column of dataset.source by default.
        series: [{ type: 'bar' }, { type: 'bar' }, { type: 'bar' }]
      };

      option && codeChart.setOption(option, true);
    },
    // 重组数组对象
    reduceData (responseData) {
      return responseData.reduce((total, currentValue) => {
        return {
          ...total,
          ...Object.keys(currentValue).reduce((_r, _c) => { // Object.keys() 返回一个给定对象的属性名
            if (total[_c]) {
              total[_c].push(currentValue[_c])
            } else {
              total[_c] = [currentValue[_c]]
            }
            return total
          }, {})
        }
      }, {})
    },
    // 处理时间
    getFormatDate(date) {
      var month = date.getMonth() + 1
      var strDate = date.getDate()
      if (month >= 1 && month <= 9) {
        month = '0' + month
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = '0' + strDate
      }
      var currentDate = date.getFullYear() + '-' + month + '-' + strDate + ' ' + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds()
      return currentDate
    },
    initTime() {
      const endTime = this.getFormatDate(new Date()).substr(0, 11) + '23:59:59'
      const startTime = this.getFormatDate(new Date(new Date() - 3600 * 1000 * 24 * 6)).substr(0, 11) + '00:00:00'
      this.queryParams = {
        'startTime': startTime,
        'endTime': endTime
      }
    },
  }
};
</script>
