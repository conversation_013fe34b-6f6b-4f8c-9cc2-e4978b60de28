import request from '@/utils/request'

// 查询API参数映射配置列表
export function listApiParamsMappingConf(query) {
  return request({
    url: '/system/apiParamsMappingConf/list',
    method: 'get',
    params: query
  })
}

// 查询API参数映射配置详细
export function getApiParamsMappingConf(id) {
  return request({
    url: '/system/apiParamsMappingConf/' + id,
    method: 'get'
  })
}

// 新增API参数映射配置
export function addApiParamsMappingConf(data) {
  return request({
    url: '/system/apiParamsMappingConf',
    method: 'post',
    data: data
  })
}

// 修改API参数映射配置
export function updateApiParamsMappingConf(data) {
  return request({
    url: '/system/apiParamsMappingConf',
    method: 'put',
    data: data
  })
}

// 删除API参数映射配置
export function delApiParamsMappingConf(id) {
  return request({
    url: '/system/apiParamsMappingConf/' + id,
    method: 'delete'
  })
}
