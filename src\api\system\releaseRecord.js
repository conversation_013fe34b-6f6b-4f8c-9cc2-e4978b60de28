import request from '@/utils/request'

// 查询发布版本记录列表
export function releaseRecordList(query) {
  return request({
    url: '/system/releaseRecord/list',
    method: 'get',
    params: query
  })
}
// 新增发布版本记录
export function releaseRecordAdd(data) {
  return request({
    url: '/system/releaseRecord',
    method: 'post',
    data: data
  })
}
// 修改发布版本记录
export function releaseRecordEdit(data) {
  return request({
    url: '/system/releaseRecord',
    method: 'put',
    data: data
  })
}

