import request from '@/utils/request'

// 查询API字典关系映射配置列表
export function listDictMappingConf(query) {
  return request({
    url: '/system/dictMappingConf/list',
    method: 'get',
    params: query
  })
}

// 查询API字典关系映射配置详细
export function getDictMappingConf(id) {
  return request({
    url: '/system/dictMappingConf/' + id,
    method: 'get'
  })
}

// 新增API字典关系映射配置
export function addDictMappingConf(data) {
  return request({
    url: '/system/dictMappingConf',
    method: 'post',
    data: data
  })
}

// 修改API字典关系映射配置
export function updateDictMappingConf(data) {
  return request({
    url: '/system/dictMappingConf',
    method: 'put',
    data: data
  })
}

// 删除API字典关系映射配置
export function delDictMappingConf(id) {
  return request({
    url: '/system/dictMappingConf/' + id,
    method: 'delete'
  })
}
