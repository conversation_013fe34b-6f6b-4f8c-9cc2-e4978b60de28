<!-- 审核弹窗 -->
<template>
  <div class="editItemDialog">
    <el-dialog :title="dialogTitle" :visible.sync="visible" width="800px" @close="cancel">
      <el-form :model="formData" :rules="rules" ref="form" label-width="110px" inline :disabled="dialogTitle === '查看'">
        <el-form-item label="事件标题">{{ formData.eventTitle }}</el-form-item><br/>
        <el-form-item label="事件发生时间">
          {{ formData.eventStartTime }} - {{ formData.eventEndTime }}
        </el-form-item><br/>
        <el-form-item label="事件明细">{{ formData.eventDetail }}</el-form-item><br/>
        <el-form-item label="审核项目经理">
          {{ getDictLabel(formData.projectManagerAuditor, dict.type.project_outcome_project_manager) }}
        </el-form-item><br/>
        <el-form-item label="人员" prop="nickName" :rules="{ required: true, message: '请选择人员', trigger: 'change' }" class="short-item mr60">
          <el-select v-model="formData.nickName" clearable filterable style="width: 240px">
            <el-option v-for="item in userOptions" :key="item.userId" :value="item.nickName" :label="item.nickName"/>
          </el-select>
        </el-form-item>
        <el-form-item label="一类指标" prop="primaryIndicator" class="short-item">
          <el-select v-model="formData.primaryIndicator" clearable filterable style="width: 240px" @change="changePrimaryIndicator(formData)">
            <el-option v-for="item in primaryIndicatorOptions" :key="item.code" :value="item.code" :label="item.name"/>
          </el-select>
        </el-form-item><br/>
        <el-form-item label="二类指标" prop="secondaryIndicator" class="short-item mr60">
          <el-select v-model="formData.secondaryIndicator" clearable filterable style="width: 240px" :disabled="!formData.primaryIndicator" @change="changeSecondaryIndicator(formData)">
            <template v-for="item2 in secondaryIndicatorOptions">
              <el-option v-if="dialogTitle === '查看' || (item2.primaryIndicator === formData.primaryIndicator && !item2.systemGenerated)" :key="item2.code" :value="item2.code" :label="item2.name"/>
            </template>
          </el-select>
        </el-form-item>
        <el-form-item label="推荐绩效" prop="recommendedLevel" class="short-item">
          <el-select v-model="formData.recommendedLevel" clearable filterable style="width: 240px" :disabled="!formData.secondaryIndicator">
            <el-option v-for="item in dict.type.performance_level" :key="item.value" :value="item.value" :label="item.label" v-if="item.value !== 'B'"/>
          </el-select>
        </el-form-item><br/>
        <el-form-item label="推荐原因" class="short-item" v-if="formData.recommendedLevel && dialogTitle !== '查看'">
          <template v-for="item in indicatorResultIdsOptions">
            <p :key="item.id" class="indicatorResultP"
              v-if="item.firstIndecator === formData.primaryIndicator && // 一类指标
                    item.secondIndecator === formData.secondaryIndicator && // 二类指标
                    item.level === formData.recommendedLevel">
              {{ item.result }}
            </p>
          </template>
        </el-form-item>
        <el-form-item label="推荐理由（请描述具体工作表现）" label-width="240px" prop="recommendedReason" class="subjective-reason">
          <el-input v-if="dialogTitle !== '查看'" v-model="formData.recommendedReason" :placeholder="recommendedReasonPlaceholder" autocomplete="off" type="textarea" :rows="7" :maxlength="500" show-word-limit></el-input>
          <p v-else class="cell-content">{{ formData.recommendedReason }}</p>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm" :loading="btnLoading" v-if="dialogTitle !== '查看'">确认并提交</el-button>
        <el-button @click="cancel">{{dialogTitle !== '查看' ? '取消' : '关闭'}}</el-button>
      </span>
      </el-dialog>
  </div>
</template>
<script>
import { performanceFeedbackEdit, indicatorList, performIndicatorResultList } from "@/api/business/performanceFeedback"
import { listUserAll } from "@/api/system/user"
export default {
  dicts: [
    'project_outcome_project_manager',
    'performance_level'
  ],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    dialogFormData: {
      type: Object
    },
    dialogTitle: {
      type: String
    }
  },
  data() {
    return {
      visible: this.dialogVisible,
      btnLoading: false,
      // 一类指标下拉数据
      primaryIndicatorOptions: [],
      // 二类指标下拉数据
      secondaryIndicatorOptions: [],
      // 全部用户下拉数据
      userOptions: [],
      // 推荐原因下拉数据
      indicatorResultIdsOptions: [],
      formData: {
        nickName: '',
        primaryIndicator: '',
        secondaryIndicator: '',
        recommendedLevel: '',
        recommendedReason: '',
      },
      rules: {
        nickName: [ { required: true, message: '请选择人员', trigger: 'change' } ],
        primaryIndicator: [ { required: true, message: '请选择一类指标', trigger: 'change' } ],
        secondaryIndicator: [ { required: true, message: '请选择二类指标', trigger: 'change' } ],
        recommendedLevel: [ { required: true, message: '请选择推荐绩效', trigger: 'change' } ],
        recommendedReason: [ { required: true, message: '请输入推荐理由', trigger: 'blur' } ],
      },
      recommendedReasonPlaceholder: `推荐理由（S级模板）：小王严守会议纪律，沟通理性，提出创新方案并攻克技术难点，完全符合 S 绩效标准，值得推荐。\n推荐理由（A级模板）：小王按时超量完成开发任务，系统稳定可靠，完全符合 A 绩效标准，值得推荐。\n推荐理由（C级模板）：小王在项目中两次不服从工作任务安排，导致项目出现严重问题，符合 C 绩效评定标准，应评定为 C 绩效。\n推荐理由（D级模板）：小王在上班期间做无关事项，且与同事发生肢体冲突，严重违反工作制度，后果严重，符合 D 绩效评定标准，应评定为 D 绩效。`
    }
  },
  watch: {
    dialogVisible (val) {
      this.visible = val
      if (this.visible) {
        this.formData = Object.assign(this.formData, {...this.dialogFormData})
        this.indicatorList()
        this.getIndicatorResultIdsOptions()
        this.listUserAll()
      }
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    cancel () {
      this.formData = {
        nickName: '',
        primaryIndicator: '',
        secondaryIndicator: '',
        recommendedLevel: '',
        recommendedReason: '',
      }
      this.$nextTick(() => {
        this.$refs.form.clearValidate()
      })
      this.$emit('update:dialogVisible', false)
    },
    // 处理
    confirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.btnLoading = true
          let apiName = null
          performanceFeedbackEdit(this.formData).then((res) => {
            this.btnLoading = false
            if (res.code === 200) {
              this.$message.success('操作成功')
              this.cancel()
              this.$emit('callback')
            } else {
              this.$message.error(res.msg)
            }
          }).catch((err) => {
            this.btnLoading = false
          })
        }
      })
    },
    /** 一类指标、二类指标下拉数据 */
    indicatorList () {
      indicatorList().then(res => {
        if (res.code === 200) {
          this.primaryIndicatorOptions = res.data
          let secondaryIndicatorOptions = []
          this.primaryIndicatorOptions.forEach(item => {
            secondaryIndicatorOptions.push(...item.secondaryIndicators.map(second => {
              return {
                code: second.code,
                name: second.name,
                primaryIndicator: item.code,
                systemGenerated: second.systemGenerated
              }
            }))
          })
          this.secondaryIndicatorOptions = secondaryIndicatorOptions
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    /** 一类指标变更 */
    changePrimaryIndicator (row) {
      this.$set(row, 'secondaryIndicator', '')
      this.$set(row, 'recommendedLevel', '')
    },
    /** 二类指标变更 */
    changeSecondaryIndicator (row) {
      this.$set(row, 'recommendedLevel', '')
    },
    /** 获取推荐理由下拉数据 */
    getIndicatorResultIdsOptions () { 
      performIndicatorResultList({
        pageSize: 10000,
        pageNum: 1
      }).then(res => {
        if (res.code === 200) {
          this.indicatorResultIdsOptions = res.rows
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    /** 获取用户列表 */
    listUserAll () {
      listUserAll().then(res => {
        this.userOptions = res
      })
    },
    /** 根据value获取字典label */
    getDictLabel (value, dictData, valueKey, labelKey) {
      let dictValueKey = valueKey || 'value'
      let dictLabelKey = labelKey || 'label'
      const item = dictData.find(item => item[dictValueKey] === value)
      return item ? item[dictLabelKey] : value
    }
  }
}
</script>
<style lang="scss" scoped>
.editItemDialog {
  ::v-deep .el-dialog__body {
    padding: 10px 20px;
  }
  .el-form-item {
    margin-bottom: 15px;
  }
  ::v-deep .el-form-item__label {
    text-align: left;
  }
  ::v-deep .short-item .el-form-item__label {
    width: 80px!important;
  }
  ::v-deep .el-form-item__content {
    max-width: 620px;
  }
  .mr60 {
    margin-right: 60px;
  }
  ::v-deep .el-input__count {
    bottom: -16px;
    line-height: 12px;
  }
  .subjective-reason {
    .el-textarea, p {
      width: 750px;
    }
    p {
      line-height: 22px;
    }
  }
  .indicatorResultP {
    margin: 0;
    line-height: 22px;
    margin-bottom: 6px;
    max-width: 700px;
    &:first-child {
      margin-top: 8px;
    }
    &:last-child {
      margin-bottom: -5px;
    }
  }
  ::v-deep .el-dialog__footer {
    text-align: center;
  }
  .cell-content {
    white-space: pre-wrap; /* 或者使用 normal, pre, nowrap, pre-wrap, pre-line, break-spaces */
    word-break: break-all; /* 或者使用 keep-all, break-all */
  }
}
</style>
