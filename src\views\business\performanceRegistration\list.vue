<template>
  <div class="app-container">
    <el-tabs  v-model="activeName" @tab-click="handleClick">
      <!-- 绩效明细 -->
      <el-tab-pane name="detail" label="绩效明细">
        <el-form :model="queryParams.detail" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="姓名" prop="userName">
            <el-input
              v-model="queryParams.detail.userName"
              placeholder="请输入姓名"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="评定年" prop="evalYear">
            <el-select
              v-model="queryParams.detail.evalYear"
              placeholder="选择评定年"
              clearable
              size="small"
              style="width: 160px"
            >
              <el-option
                v-for="year in years"
                :key="year"
                :label="year"
                :value="year"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="评定月" prop="evalMonth">
            <el-select
              v-model="queryParams.detail.evalMonth"
              placeholder="选择评定月"
              clearable
              size="small"
              style="width: 160px"
            >
              <el-option
                v-for="dict in monthDict"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="绩效级别" prop="level" v-show="activeName === 'detail'">
            <el-select
              v-model="queryParams.detail.level"
              placeholder="选择级别"
              clearable
              size="small"
              style="width: 160px"
            >
              <el-option
                v-for="dict in levelDict"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table v-loading="loading" :data="detailTableData" height="calc(100vh - 280px)" stripe border @sort-change="handleSortChange">
          <el-table-column label="序号" align="center" type="index" width="80" />
          <el-table-column label="所属组名" align="center" prop="deptName" />
          <el-table-column label="姓名" align="center" prop="userName" />
          <el-table-column label="评定年" align="center" prop="evalYear" sortable="custom" />
          <el-table-column label="评定月" align="center" prop="evalMonth" sortable="custom" />
          <el-table-column label="绩效级别" align="center" prop="level" sortable="custom" />
        </el-table>
      </el-tab-pane>
      <!-- 绩效统计 -->
      <el-tab-pane name="statistics" label="绩效统计">
        <el-form :model="queryParams.statistics" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="姓名" prop="userName">
            <el-input
              v-model="queryParams.statistics.userName"
              placeholder="请输入姓名"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="评定年" prop="evalYear">
            <el-select
              v-model="queryParams.statistics.evalYear"
              placeholder="选择评定年"
              clearable
              size="small"
              style="width: 160px"
            >
              <el-option
                v-for="year in years"
                :key="year"
                :label="year"
                :value="year"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="评定月" prop="evalMonth">
            <el-select
              v-model="queryParams.statistics.evalMonth"
              placeholder="选择评定月"
              clearable
              size="small"
              style="width: 160px"
            >
              <el-option
                v-for="dict in monthDict"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="绩效级别" prop="level" v-show="activeName === 'detail'">
            <el-select
              v-model="queryParams.statistics.level"
              placeholder="选择级别"
              clearable
              size="small"
              style="width: 160px"
            >
              <el-option
                v-for="dict in levelDict"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table v-loading="loading" :data="statisticsTableData" height="calc(100vh - 280px)" stripe border @sort-change="handleSortChange">
          <el-table-column label="序号" align="center" type="index" width="80" />
          <el-table-column label="所属组名" align="center" prop="deptName" />
          <el-table-column label="姓名" align="center" prop="userName" />
          <el-table-column label="S次数" align="center" prop="sCount" sortable="custom" />
          <el-table-column label="A次数" align="center" prop="aCount" sortable="custom" />
          <el-table-column label="C次数" align="center" prop="cCount" sortable="custom" />
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <pagination
      v-show="paginationData[activeName].total>0"
      :total="paginationData[activeName].total"
      :page.sync="paginationData[activeName].pageNum"
      :limit.sync="paginationData[activeName].pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listPerformanceRegistration, performanceRegistrationStatistic } from "@/api/performregistration/performanceRegistration";
import {getDicts} from "../../../api/system/dict/data";
import { active } from "sortablejs";
export default {
  name: "performanceRegistrationQuery",
  data() {
    let month = new Date().getMonth() + 1
    return {
      activeName: "detail",
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 绩效明细表格数据
      detailTableData: [],
      // 绩效统计表格数据
      statisticsTableData: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 组别
      groupDict:[],
      // 级别
      levelDict:[],
      // 月份
      monthDict:[],
      years: [],
      // 查询参数
      queryParams: {
        detail: {
          deptId: '',
          userName: '',
          evalYear: new Date().getFullYear(),
          evalMonth: month < 10 ? ('0' + month) : month,
          level: '',
          orderByColumn: '',
          isAsc: ''
        },
        statistics: {
          deptId: '',
          userName: '',
          evalYear: new Date().getFullYear(),
          evalMonth: '',
          level: '',
          orderByColumn: '',
          isAsc: ''
        }
      },
      // 分页参数
      paginationData: {
        detail: {
          total: 0,
          pageNum: 1,
          pageSize: 20
        },
        statistics: {
          total: 0,
          pageNum: 1,
          pageSize: 20
        }
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        deptId: [
          { required: true, message: "部门名称不能为空", trigger: "change" }
        ],
        userName: [
          { required: true, message: "姓名不能为空", trigger: "blur" }
        ],
        evalYear: [
          { required: true, message: "评定年不能为空", trigger: "blur" }
        ],
        evalMonth: [
          { required: true, message: "评定月不能为空", trigger: "blur" }
        ],
        level: [
          { required: true, message: "绩效级别不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.createYear();
    this.init();
    this.getList();
  },
  methods: {
    /** 切换tab */
    handleClick (tab) {
      if (tab.name !== this.lastActiveName) {
        this.lastActiveName = tab.name
        this.getList()
      }
    },
    /** 查询绩效登记列表 */
    getList() {
      this.loading = true
      let apiName = null
      let params = {...this.queryParams[this.activeName]}
      switch (this.activeName) {
        case 'detail':
          apiName = listPerformanceRegistration
          params.type = 1
          break
        case 'statistics':
          apiName = performanceRegistrationStatistic
          break
      }
      if (this.paginationData[this.activeName]) {
        params.pageNum = this.paginationData[this.activeName].pageNum,
        params.pageSize = this.paginationData[this.activeName].pageSize
      }
      apiName(params).then(response => {
        this[this.activeName + 'TableData'] = response.rows
        this.paginationData[this.activeName].total = response.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.paginationData[this.activeName].pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    createYear(){
      // 获取当前年份和前两年的年份
      const currentYear = new Date().getFullYear();
      const startYear = 2021;

      // 构造年份选择器数据源
      for (let i = currentYear; i >= startYear; i--) {
        this.years.push(i.toString());
      }
      this.evalYear = currentYear;
    },
    async init() {
      await getDicts("group").then(response => {
        this.groupDict = response.data
      })
      await getDicts("level").then(response => {
        this.levelDict = response.data
      })
      await getDicts("month").then(response => {
        this.monthDict = response.data
      })
    },
    // 排序
    handleSortChange (data) {
      debugger;
      let params
      this.queryParams[this.activeName].orderByColumn = data.prop
      if (data.order === 'ascending') {
        this.queryParams[this.activeName].isAsc = 'asc'
      } else if (data.order === 'descending') {
        this.queryParams[this.activeName].isAsc = 'desc'
      } else {
        this.queryParams[this.activeName].isAsc = ''
      }
      this.handleQuery()
    }
  }
}
</script>
