<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="开始时间:">
        <el-date-picker
          v-model="queryParams.dateArr"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="任务名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入任务名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="所属组:">
        <el-select
          v-model="queryParams.groupId"
          placeholder="选择组别"
          clearable
          size="small"
          style="width: 160px"
        >
          <el-option
            v-for="dict in groupDict"
            :key="dict.deptId"
            :label="dict.deptName"
            :value="dict.deptId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="任务状态:">
        <el-select
          v-model="queryParams.taskStatus"
          placeholder="选择状态"
          clearable
          size="small"
          style="width: 160px"
        >
          <el-option label="全部" value="all"/>
          <el-option label="未开始" value="wait"/>
          <el-option label="进行中" value="doing"/>
          <el-option label="已完成" value="done"/>
          <el-option label="暂停" value="pause"/>
          <el-option label="取消" value="cancel"/>
          <el-option label="关闭" value="closed"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSearch">搜索</el-button>
      </el-form-item>
    </el-form>

    <div>
      <el-table v-loading="loading" :data="taskInfoList" style="width: 100%;" class="elTable" height="calc(100vh - 270px)" stripe border>
        <el-table-column label="编号" align="center" width="50">
          <template slot-scope="scop">
            {{scop.$index+1}}
          </template>
        </el-table-column>
        <el-table-column align="center" label="任务id" prop="taskId" width="150"/>
        <el-table-column align="center" label="任务标题" prop="taskName" min-width="450"/>
        <el-table-column align="center" label="用户名" prop="userName" :show-overflow-tooltip="true" width="150"/>
        <el-table-column align="center" label="任务优先级" prop="taskPrimary" :show-overflow-tooltip="true" width="150"/>
        <el-table-column align="center" label="任务状态" prop="status" :show-overflow-tooltip="true" width="150"/>
        <el-table-column align="center" label="实际开始时间" prop="realStarted" :show-overflow-tooltip="true" width="180"/>
        <el-table-column align="center" label="完成时间" prop="finishedDate" :show-overflow-tooltip="true" width="180"/>
        <el-table-column align="center" label="关闭时间" prop="closedDate" :show-overflow-tooltip="true" width="180"/>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <br/>
      <br/>
    </div>

  </div>
</template>

<script>
  import { listTaskByGroup } from "@/api/taskQuery";
  import { getDicts } from "../../../api/system/dict/data";
  import {deptSelect} from "../../../api/commonBiz";


  export default {
    name: "TaskInfo",
    data() {
      return {
        pickerOptions: {
          shortcuts: [{
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }]
        },
        // 按钮loading
        buttonLoading: false,
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 【请填写功能名称】表格数据
        taskList: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 查询参数
        groupDict:[],
        taskInfoList:[],
        queryParams: {
          groupId: null,
          pageNum: 1,
          pageSize: 20,
          dateArr:[],
          taskStatus:"all",
        },
      };
    },
    created() {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      this.queryParams.dateArr = [start, end]
      this.init();
    },
    methods: {
      async handleSearch() {
        if (this.queryParams.dateArr.length === 0) {
          alert("请选择起始时间!");
        }
        this.getList();
      },

      async init() {
        this.getDeptList()
        this.getList()
      },

      getList() {
        this.loading = true;
        listTaskByGroup(this.queryParams).then(response => {
          this.taskInfoList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
      /** 查询部门列表 */
      getDeptList() {
        var deptList = [];
        deptSelect().then(response => {
          deptList = response.data;
          let tecCenter = {deptName: "技术中心", deptId: 101}
          deptList.unshift(tecCenter)
          this.groupDict = deptList;
          this.queryParams.groupId = deptList[0].deptId;
        });
      },
    }
  };
</script>

<style scoped>
</style>
