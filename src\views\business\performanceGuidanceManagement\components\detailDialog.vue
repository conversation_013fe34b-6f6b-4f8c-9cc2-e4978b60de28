<!-- 审核弹窗 -->
<template>
  <div class="detailDialog">
    <el-dialog title="查看" :visible.sync="visible" width="800px" @close="$emit('update:dialogVisible', false)">
      <el-form :model="dialogData" ref="form" label-width="110px" inline>
        <el-row>
          <el-col :span="12">
            <el-form-item label="一类指标">{{ dialogData.primaryIndicatorName }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="二类指标">{{ dialogData.secondaryIndicatorName }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="事件标题">{{ dialogData.eventTitle }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="事件发生时间">{{dialogData.eventStartTime}} - {{dialogData.eventEndTime}}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属组">{{ dialogData.groupName }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="岗位">{{ dialogData.roleName }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="姓名">{{ dialogData.nickName }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="推荐绩效级别">{{ dialogData.recommendedLevel }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="辅导人">{{ dialogData.tutor }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="辅导时间">{{ dialogData.tutoringTime }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="辅导结果">{{ dialogData.tutoringResult }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="辅导概要">{{ dialogData.tutoringSummary }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="总监建议">{{ dialogData.directorSuggest }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="建议时间">{{ dialogData.suggestTime }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="$emit('update:dialogVisible', false)">关闭</el-button>
      </div>
      </el-dialog>
  </div>
</template>
<script>
export default {
  dicts: [
    'project_outcome_project_manager',
    'performance_level'
  ],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    dialogData: {
      type: Object
    }
  },
  data() {
    return {
      visible: this.dialogVisible,
    }
  },
  watch: {
    dialogVisible (val) {
      this.visible = val
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
.detailDialog {
  ::v-deep .el-dialog__body {
    padding: 10px 20px;
  }
  ::v-deep .el-form-item {
    margin-bottom: 10px;
  }
  ::v-deep .el-form-item__content {
    max-width: 600px;
    margin-top: 6px;
    line-height: 24px;
    white-space: pre-line;
  }
  .dialog-footer {
    text-align: center;
  }
}
</style>
