import request from '@/utils/request'

// 数据库权限-列表
export function dataBasePermissionList(query) {
  return request({
    url: '/system/dataBasePermission/list',
    method: 'get',
    params: query
  })
}
// 数据库权限-查询小组和人员
export function dataBasePermissionDeptAndUser(query) {
  return request({
    url: '/system/dataBasePermission/deptAndUser',
    method: 'get',
    params: query
  })
}
// 数据库权限-新增权限
export function dataBasePermissionAdd(data) {
  return request({
    url: '/system/dataBasePermission',
    method: 'post',
    data: data
  })
}
// 数据库权限-修改权限
export function dataBasePermissionEdit(data) {
  return request({
    url: '/system/dataBasePermission',
    method: 'put',
    data: data
  })
}