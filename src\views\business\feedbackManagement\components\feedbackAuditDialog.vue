<!-- 审核弹窗 -->
<template>
  <div>
    <el-dialog :title="title" :visible.sync="visible" width="500px" @close="cancel">
      <el-form :model="formData" :rules="rules" ref="form">
        <el-form-item label="审核状态" prop="status">
          <el-select v-model="formData.status" placeholder="请选择审核状态" style="width: 300px">
            <el-option label="同意" value="APPROVED"></el-option>
            <el-option label="拒绝" value="REJECTED"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核意见" prop="remark">
          <el-input v-model="formData.remark" type="textarea" placeholder="请输入处理原因" rows="4"/>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm" :loading="btnLoading">确认</el-button>
        <el-button @click="cancel">取消</el-button>
      </span>
      </el-dialog>
  </div>
</template>
<script>
import { projectManagerAudit, batchFinalAudit } from "@/api/business/performanceFeedback"
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    ids: {
      type: Array
    },
    dialogType: {
      type: String
    }
  },
  data() {
    return {
      visible: this.dialogVisible,
      btnLoading: false,
      title: '',
      formData: {
        ids: [],
        status: '',
        remark: ''
      },
      rules: {
        status: [
          { required: true, message: '请选择审核结果', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    dialogVisible (val) {
      this.visible = val
      if (this.visible) {
        this.formData.ids = [...this.ids]
        switch (this.dialogType) {
          case 'managerAudit':
            this.title = '项管审核'
            break
          case 'finalAudit':
            this.title = '最终审核'
            break
          case 'batchFinalAudit':
            this.title = '批量最终审核'
            break
        }
      }
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    cancel () {
      this.formData = {
        ids: [],
        status: '',
        remark: ''
      }
      this.$emit('update:dialogVisible', false)
    },
    // 处理
    confirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.btnLoading = true
          let apiName = null
          if (this.dialogType === 'managerAudit') {
            apiName = projectManagerAudit
          } else {
            apiName = batchFinalAudit
          }
          apiName(this.formData).then((res) => {
            this.btnLoading = false
            if (res.code === 200) {
              this.$message.success('操作成功')
              this.cancel()
              this.$emit('callback')
            } else {
              this.$message.error(res.msg)
            }
          }).catch((err) => {
            this.btnLoading = false
          })
        }
      })
    },
  },
};
</script>
<style lang="scss" scoped>
</style>
