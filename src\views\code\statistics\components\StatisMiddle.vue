<template>
    <div class="container">
      <div class="left">
        <div class="title">
          <span>代码里最大的项目</span>
          <div>
          <el-table
              :data="leftTableData"
              height="350"
              stripe
              border
              style="width: 100%">
              <el-table-column
                prop="projectName"
                label="项目"
                width="80">
              </el-table-column>
              <el-table-column
                prop="totalLine"
                label="调整行数"
                width="150">
              </el-table-column>
              <el-table-column
                prop="additonalsLine"
                label="新增行数"
                width="150">
              </el-table-column>
              <el-table-column
                prop="deleteLine"
                label="删除行数"
                width="150">
              </el-table-column>
          </el-table>
          </div>
          <div class="container-page">
            <el-pagination
              class="rear-page"
              :current-page="queryFirstForm.pageNo"
              :page-size="queryFirstForm.pageSize"
              layout="prev, pager, next, slot, jumper, sizes, total"
              :total="queryFirstForm.total"
              @size-change="handleFirstSizeChange"
              @current-change="handleFirstCurrentChange"
            >
              <!-- slot部分，跳转末页 -->
              <button
                class="lastPage"
                :disabled="queryFirstForm.lastPageDisabled"
                @click="toLastPageFirst"
              >
                <i class="el-icon-d-arrow-right"></i>
              </button>
            </el-pagination>
            <el-pagination
              class="ahead-page"
              :current-page="queryFirstForm.pageNo"
              :page-size="queryFirstForm.pageSize"
              layout="slot"
              :total="queryFirstForm.total"
              @size-change="handleFirstSizeChange"
              @current-change="handleFirstCurrentChange"
            >
              <!-- slot部分，跳转首页 -->
              <button
                class="firstPage"
                :disabled="queryFirstForm.firstPageDisabled"
                @click="toFirstPageFirst"
              >
                <i class="el-icon-d-arrow-left"></i>
              </button>
            </el-pagination>
     </div>
        </div>
      </div>
      
      <div class="right">
        <div class="title">
          <span>代码量最大的项目以及贡献人员</span>
          <div>
            <el-table
              :data="rightTableData"
              height="350"
              stripe
              border
              style="width: 100%">
              <el-table-column
                prop="projectName"
                label="项目"
                width="80">
              </el-table-column>
              <el-table-column
                prop="deptName"
                label="所属开发团队"
                width="150">
              </el-table-column>
              <el-table-column
                prop="name"
                label="姓名"
                width="150">
              </el-table-column>
              <el-table-column
                prop="totalLine"
                label="调整行数"
                width="150">
              </el-table-column>
              <el-table-column
                prop="additonalsLine"
                label="新增行数"
                width="150">
              </el-table-column>
              <el-table-column
                prop="deleteLine"
                label="删除行数"
                width="150">
              </el-table-column>
            </el-table>
          </div>

          <div class="container-page">
            <el-pagination
              class="rear-page"
              :current-page="querySecondForm.pageNo"
              :page-size="querySecondForm.pageSize"
              layout="prev, pager, next, slot, jumper, sizes, total"
              :total="querySecondForm.total"
              @size-change="handleSecondSizeChange"
              @current-change="handleSecondCurrentChange"
            >
              <!-- slot部分，跳转末页 -->
              <button
                class="lastPage"
                :disabled="querySecondForm.lastPageDisabled"
                @click="toLastPageSecond"
              >
                <i class="el-icon-d-arrow-right"></i>
              </button>
            </el-pagination>
            <el-pagination
              class="ahead-page"
              :current-page="querySecondForm.pageNo"
              :page-size="querySecondForm.pageSize"
              layout="slot"
              :total="querySecondForm.total"
              @size-change="handleSecondSizeChange"
              @current-change="handleSecondCurrentChange"
            >
              <!-- slot部分，跳转首页 -->
              <button
                class="firstPage"
                :disabled="querySecondForm.firstPageDisabled"
                @click="toFirstPageSecond"
              >
                <i class="el-icon-d-arrow-left"></i>
              </button>
            </el-pagination>
     </div>
        </div>
      </div>

    </div>
  </template>
  
  <script>
  import {listLeftInfoPage,listRightInfoPage} from '@/api/periodStatis'
  export default {
    name: 'Middle',
    props:{
      father:Object
    },
    props: ['leftData','rightData','queryForm'],
    components: {},
    data () {
      return {
          firstLoading: false,
          secondLoading: false,
          leftTableData: [], // 左边表格数据列表
          rightTableData:[], // 右边表格数据列表
          // queryForm:{
          //   total: 0,
          //   pageNo:1,
          //   pageSize: 20, //  展示数量
          //   firstPageDisabled: false, //  首页
          //   lastPageDisabled: true, //  末页
          //   groupBy: ''
          // },
          queryFirstForm: {
            total: 0,
            pageSize: 20, //  展示数量
            firstPageDisabled: false, //  首页
            lastPageDisabled: true, //  末页
            groupBy: 'project',
            beginDate: '',
            endDate: '',
            managered: 0 ,
            pageNo:1
          },
          querySecondForm: {
            total: 0,
            pageSize: 20, //  展示数量
            firstPageDisabled: false, //  首页
            lastPageDisabled: true, //  末页
            groupBy: 'projectAndAuthor',
            beginDate: '',
            endDate: '',
            managered: 0 ,
            pageNo:1
          }
      }
    },
    watch: {
      leftData : function(newValue){
        console.log('left', newValue)
        this.leftTableData = newValue.data.records
        console.log('table',this.leftTableData)
        this.queryFirstForm.total = newValue.data.total
      },
      rightData: function(newValue){
        console.log('right', newValue)
        this.rightTableData = newValue.data.records
        this.querySecondForm.total = newValue.data.total
      },
      queryForm: function(newValue){
        console.log('queryForm=================', newValue)
      },
        // 分页 计算第一个表格首页和末页
        queryFirstForm: {
          handler(newVal) {
            let pages = Math.ceil(newVal.total / newVal.pageSize);
        if (pages === 0) {
          // 数据(totalResult)为0
          this.queryFirstForm.firstPageDisabled = true; // 首页按钮是否禁用
          this.queryFirstForm.lastPageDisabled = true; // 末页按钮是否禁用
        } else {
          this.queryFirstForm.firstPageDisabled = newVal.pageNo === 1;
          this.queryFirstForm.lastPageDisabled = newVal.pageNo === pages;
        }
          },
          // 一进页面就执行
          immediate: false,
          deep: true,
        },
         // 分页 计算第二个表格首页和末页
         querySecondForm: {
          handler(newVal) {
            let pages = Math.ceil(newVal.total / newVal.pageSize);
        if (pages === 0) {
          // 数据(totalResult)为0
          this.querySecondForm.firstPageDisabled = true; // 首页按钮是否禁用
          this.querySecondForm.lastPageDisabled = true; // 末页按钮是否禁用
        } else {
          this.querySecondForm.firstPageDisabled = newVal.pageNo === 1;
          this.querySecondForm.lastPageDisabled = newVal.pageNo === pages;
        }
          },
          // 一进页面就执行
          immediate: false,
          deep: true,
        }, 
    },
    computed: {},
    methods: {
      async getTableLeftList(){
        this.queryFirstForm.beginDate = this.queryForm.beginDate
        this.queryFirstForm.endDate = this.queryForm.endDate
        this.queryFirstForm.managered = this.queryForm.managered
        listLeftInfoPage(this.queryFirstForm).then(response => {
        console.log('LeftList = ',response)
        this.leftTableData = response.data.records
        this.queryFirstForm.total = response.data.total * 1
        });
        },
        
    async getTableRightList(){
        this.querySecondForm.beginDate = this.queryForm.beginDate
        this.querySecondForm.endDate = this.queryForm.endDate
        this.querySecondForm.managered = this.queryForm.managered
        listRightInfoPage(this.querySecondForm).then(response => {
        this.rightTableData = response.data.records
        this.querySecondForm.total = response.data.total * 1
        });
      },
    parentMsg: function () {
          },  
          //  改变页码
    handleFirstPageChange({ pageNo, pageSize }) {
      this.queryFirstForm.pageNo = pageNo;
      this.queryFirstForm.pageSize = pageSize;
    },

    //  改变每页显示数量
    handleFirstSizeChange(pageSize) {
      this.queryFirstForm.pageSize = pageSize;
    },

    //  改变当前页码
    handleFirstCurrentChange(pageNo) {
      this.queryFirstForm.pageNo = pageNo;
    },

    //  前往首页
    toFirstPageFirst() {
      this.handleFirstCurrentChange(1);
    },

    //  前往末页
    toLastPageFirst() {
      let max = Math.ceil(this.queryFirstForm.total / this.queryFirstForm.pageSize);
      this.handleFirstCurrentChange(max);
    },
    // 当前页面
    handleFirstCurrentChange(val) {
        this.queryFirstForm.pageNo = val
        console.log('handleCurrentChange',val)
        this.getTableLeftList()
      },

    // 页数  
    handleFirstSizeChange(val) {
        this.queryFirstForm.pageSize = val
        this.getTableLeftList()
      },
      
       //  改变页码
    handleSecondPageChange({ pageNo, pageSize }) {
      this.querySecondForm.pageNo = pageNo;
      this.querySecondForm.pageSize = pageSize;
    },

    //  改变每页显示数量
    handleSecondSizeChange(pageSize) {
      this.querySecondForm.pageSize = pageSize;
    },

    //  改变当前页码
    handleSecondCurrentChange(pageNo) {
      this.querySecondForm.pageNo = pageNo;
    },

    //  前往首页
    toFirstPageSecond() {
      this.handleSecondCurrentChange(1);
    },

    //  前往末页
    toLastPageSecond() {
      let max = Math.ceil(this.querySecondForm.total / this.querySecondForm.pageSize);
      this.handleSecondCurrentChange(max);
    },
    // 当前页面
    handleSecondCurrentChange(val) {
        this.querySecondForm.pageNo = val
        this.getTableRightList()
      },

    // 页数  
    handleSecondSizeChange(val) {
        this.querySecondForm.pageSize = val
        this.getTableRightList()
      },
    },
    created () { },
    activated () { },
    mounted () { },
    beforeDestroy () { }
  }
  </script>
  
  <style scoped>
.container{
  width: 100%;
  height: 450px; 
}
  .left{
    height: 450px;
    width: 45%;
    float: left;
  }

  .title{
    width: 100%;
    height: 40px;
    background-color: rgb(21, 127, 204);
  }

  .title{
    text-align: center;
    line-height: 40px;
    font-size: 14px;
    color: #ffffff;
  }
  span{

  }
  .right{
    width: 54.5%;
    height: 450px;
    float: left;
    margin-left: 0.5%;
  }

  .container-page{
        float: left;
     } 
      .el-pagination {
      float: right;
      margin-top: 10px;
    }
    .el-pagination.ahead-page {
      padding-right: 0px;
    }
    .el-pagination.rear-page {
      padding-left: 0px;
    }
    .firstPage,
    .lastPage {
      background-color: white;
      cursor: pointer;
    }

::v-deep .el-table th,
::v-deep .el-table td {
  text-align: center !important;
}
  </style>