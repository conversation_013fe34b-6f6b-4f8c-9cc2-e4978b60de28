<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="完成年份:" prop="roleName">
        <el-input
          v-model="queryParams.doneYear"
          placeholder="请输入年份"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="完成月份:" prop="roleKey">
        <el-input
          v-model="queryParams.doneMonth"
          placeholder="请输入月份"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSearch">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" @click="handleDocLibStat">文档库统计</el-button>
      </el-form-item>
    </el-form>

    <div style="display: inline;float: left">
      <el-table  v-loading="loading"  :data="docStatList" style="width: 500px;" class="elTable" @cell-click="refreshDocListByGroup" stripe border>
        <el-table-column   label="编号" align="center" width="50">
          <template slot-scope="scop">
            {{scop.$index+1}}
          </template>
        </el-table-column>
        <el-table-column align="center" label="组名" prop="deptName" width="120"/>
        <el-table-column align="center" label="数量" prop="ct" :show-overflow-tooltip="true" width="75" sortable/>
        <el-table-column align="center" label="人数" prop="pcount" :show-overflow-tooltip="true" width="50"/>
        <el-table-column align="center" label="平均输出" prop="rate" :show-overflow-tooltip="true" width="100" sortable/>
      </el-table>
    </div>

    <div style="display: inline;float: left;margin-left: 60px">
      <el-table  v-loading="loading"  :data="docListByGroup" style="width: 500px;" class="elTable" @cell-dblclick="jumpToDoc" stripe border>
        <el-table-column   label="编号" align="center" width="50">
          <template slot-scope="scop">
            {{scop.$index+1}}
          </template>
        </el-table-column>
        <el-table-column align="center" label="id" prop="id" width="80" />
        <el-table-column align="center" label="标题(双击对应行可跳转文档链接)" prop="title" :show-overflow-tooltip="true" width="400" />
        <el-table-column align="center" label="所属组" prop="groupName" :show-overflow-tooltip="true" width="150" />
        <el-table-column align="center" label="作者" prop="addedBy" :show-overflow-tooltip="true" width="80" />
        <el-table-column align="center" label="添加日期" prop="addedDate" :show-overflow-tooltip="true" width="160" />
      </el-table>
    </div>

    <div style="display: inline;float: left;margin-left: 60px">
      <el-table  v-loading="loading"   :data="docCountList" style="width: 500px;" class="elTable" stripe border>
        <el-table-column   label="编号" align="center" width="50">
          <template slot-scope="scop">
            {{scop.$index+1}}
          </template>
        </el-table-column>
        <el-table-column align="center" label="姓名" prop="username" width="80" />
        <el-table-column align="center" label="数量" prop="docCount" :show-overflow-tooltip="true" width="80" />
      </el-table>
    </div>

    <!-- 弹出文档库统计窗口 -->
    <el-dialog align="center" :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-table align="center" v-loading="loading"  :data="docLibStats" style="width: 500px;" class="elTable" stripe border>
        <el-table-column  type="index" label="序号" align="center" width="100">
          <!--<template slot-scope="scop">-->
            <!--{{scop.$index+1}}-->
          <!--</template>-->
          <template v-slot="scope">
            {{ scope.$index + (queryParams.pageNum - 1) * queryParams.pageSize + 1 }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="产品名称" prop="libName" width="300"/>
        <el-table-column align="center" label="所属库名称" prop="belongToLib" :show-overflow-tooltip="true" width="150" sortable/>
        <el-table-column align="center" label="文档数量" prop="documentCount" :show-overflow-tooltip="true" width="100"/>
        <!--<el-table-column align="center" label="操作" :show-overflow-tooltip="true" width="150" sortable></el-table-column>-->
        <el-table-column align="center" label="操作" prop="link" :show-overflow-tooltip="true" width="150">
          <template slot-scope="scope">
            <!--<a :href="scope.row.link">打开</a>-->
            <el-link :href="scope.row.link" rel="external nofollow"  type="primary" target="_blank">打开</el-link>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getDocLibStat"
      />
    </el-dialog>
  </div>
</template>

<script>
import { listPerfStat, listDocStat, listDocByGroup, listDocCount, listDocLibStatistics } from "@/api/business/performance";

export default {
  name: "Document",
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      taskList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      perfStatList:[],
      docStatList:[],
      docListByGroup:[],
      docCountList:[],
      docLibStats:[],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        doneYear:0,
        doneMonth:0,
        groupId:null,
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    handleDocLibStat() {
      this.open = true;
      this.title = "文档库统计";
      this.getDocLibStat();
    },

    getDocLibStat() {
      this.loading = true;
      listDocLibStatistics(this.queryParams).then(response => {
        this.docLibStats = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    async handleSearch() {
      if(!/^\d{4}$/.test(this.queryParams.doneYear)){
        alert("请输入正确的年份");
        return;
      }
      if (!(/(^[1-9]\d*$)/.test(this.queryParams.doneMonth))) {
        alert("请输入正确的月份");
        return
      }
      if (!(this.queryParams.doneMonth < 13)) {
        alert("请输入正确的月份");
        return
      }
      await listDocStat(this.queryParams).then(response => {
        this.docStatList = response.data
        if (this.docStatList.length == 0) {
          this.docListByGroup = null
          this.queryParams.groupId = null
          return
        }
        this.queryParams.groupId = this.docStatList[0].deptId
      })
      await listDocByGroup(this.queryParams).then(response => {
        this.docListByGroup = response.data
      })
      await listDocCount(this.queryParams).then(response => {
        this.docCountList = response.data
      })
    },

    async init() {
      this.loading = true;
      await this.getNowDate()
      // await listPerfStat(this.queryParams).then()
      await listDocStat(this.queryParams).then(response => {
        this.docStatList = response.data
        if (this.docStatList.length == 0) {
          this.docListByGroup = null
          return
        }
        this.queryParams.groupId = this.docStatList[0].deptId
      })
      await listDocByGroup(this.queryParams).then(response => {
        this.docListByGroup = response.data
      })
      await listDocCount(this.queryParams).then(response => {
        this.docCountList = response.data
      })
      this.loading = false;
    },

    jumpToDoc(row, column, cell, event) {
      window.open(row.jumpUrl)
      console.log(row, event, cell, column)
    },

    refreshDocListByGroup(row, column, cell, event) {
      this.queryParams.groupId = row.deptId
      listDocByGroup(this.queryParams).then(response => {
        this.docListByGroup = response.data
      })
    },

    getNowDate() {
      const timeOne = new Date()
      const year = timeOne.getFullYear()
      let month = timeOne.getMonth() + 1
      this.queryParams.doneYear = year
      this.queryParams.doneMonth = month
    },

    /** 查询【请填写功能名称】列表 */
    getList() {
      this.loading = true;
      listPerfStat(this.queryParams).then(response => {
        this.perfStatList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
  }
};
</script>

<style scoped>
  .elTable {
    display: inline;
    background-color: #1ab394;
  }
</style>
