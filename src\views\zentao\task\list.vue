<template>
    <div >
      <div class="app-container">
        <div style="padding-top: 20px; padding-left: 10px">
              <el-form :inline="true"  @submit.native.prevent :model="queryForm">
                <el-form-item label="当前状态：">
                  <el-select
                    clearable
                    placeholder="请选择"
                    @change="handleStatusSelectChange"
                    v-model="queryForm.status"
                  >
                  <el-option
                  v-for="item in optionsData"
                  :key="item.key"
                  :value="item.value"
                  :label="item.value"
                />
                  </el-select>
                </el-form-item>
                <el-form-item label="创建年份：">
                  <el-input v-model="queryForm.createYear"
                  />
                </el-form-item>
                <el-form-item label="创建月份：">
                  <el-input  v-model="queryForm.createMonth"
                  />
                </el-form-item>
                <el-form-item label="完成人：">
                  <el-input v-model="queryForm.completedBy"
                  />
                </el-form-item>
                <el-form-item label="任务启动人：">
                  <el-input v-model="queryForm.taskStarter"
                  />
                </el-form-item>
                <el-form-item label="完成年份：">
                  <el-input v-model="queryForm.finishYear"
                  />
                </el-form-item>
                <el-form-item label="完成月份：">
                  <el-input v-model="queryForm.finishMonth"
                  />
                </el-form-item>
                <el-form-item label="所属组:">
                  <el-select
                    v-model="queryForm.groupId"
                    placeholder="选择组别"
                    clearable
                    size="small"
                    style="width: 160px"
                  >
                    <el-option
                      v-for="dict in groupDict"
                      :key="dict.deptId"
                      :label="dict.deptName"
                      :value="dict.deptId"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item>
                  <el-button
                    icon="el-icon-search"
                    type="primary"
                    @click="handleQuery"
                  >
                    查询
                  </el-button>
                  <el-button type="warning"
                             plain icon="el-icon-download"
                             @click="handleExport">导出</el-button>

                  <el-button
                    icon="el-icon-refresh"
                    type="primary"
                    @click="resetQuery"
                  >
                  </el-button>
                </el-form-item>
                <el-form-item label="提示：双击某行可打开禅道的对应明细页面">

                </el-form-item>
              </el-form>
        </div>

        <el-table
          v-loading="listLoading"
          border
          stripe
          :data="tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
          @sort-change="onSortChange"
          @row-dblclick="handleTableRow"
          height="calc(100vh - 290px)"
          ref="tableList"
        >
          <el-table-column
            type="selection"
            width="55">
          </el-table-column>
          <el-table-column label="所属项目" width="220" prop="pname" >
            <template slot-scope="scope">
              {{scope.row.pname}}
            </template>
          </el-table-column>
          <el-table-column label="需求标题" width="200" prop="title" sortable>
            <template slot-scope="scope">
              <div v-html="(scope.row.title)"></div>
            </template>
          </el-table-column>
          <el-table-column label="主任务名称" width="200" prop="name" sortable>
            <template slot-scope="scope">
              {{scope.row.name}}
            </template>
          </el-table-column>
          <el-table-column label="子任务名称" width="200" prop="sonName" sortable>
            <template slot-scope="scope">
              {{scope.row.sonName}}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="当前状态" width="100" sortable>
            <template slot-scope="scope">
              {{scope.row.status}}
            </template>
          </el-table-column>
          <el-table-column label="类型" width="100" prop="type" sortable>
            <template slot-scope="scope">
              {{scope.row.type}}
            </template>
          </el-table-column>
          <el-table-column label="任务启动人" width="130" prop="taskStarter" sortable >
            <template slot-scope="scope">
              {{scope.row.taskStarter}}
            </template>
          </el-table-column>
          <el-table-column label="任务启动时间" width="130" prop="realStarted" sortable="custom" :sort-orders="['ascending', 'descending']" >
            <template slot-scope="scope">
              {{scope.row.realStarted}}
            </template>
          </el-table-column>
          <el-table-column label="任务激活时间" width="130" prop="activatedDate" sortable="custom" :sort-orders="['ascending', 'descending']" >
            <template slot-scope="scope">
              {{scope.row.activatedDate}}
            </template>
          </el-table-column>
          <el-table-column label="完成人" width="130" prop="finishedby" sortable >
            <template slot-scope="scope">
              {{scope.row.finishedby}}
            </template>
          </el-table-column>
          <el-table-column label="完成时间" width="130" prop="finishedDate" sortable="custom" :sort-orders="['ascending', 'descending']" >
            <template slot-scope="scope">
              {{scope.row.finishedDate}}
            </template>
          </el-table-column>
          <el-table-column label="消耗时间(h)" width="130" prop="consumed" sortable="custom" :sort-orders="['ascending', 'descending']" >
            <template slot-scope="scope">
              {{scope.row.consumed}}
            </template>
          </el-table-column>
          <el-table-column label="关闭人" width="130" prop="closedby" sortable >
            <template slot-scope="scope">
              {{scope.row.closedby}}
            </template>
          </el-table-column>
          <el-table-column label="关闭时间" width="130" prop="closeddate" sortable >
            <template slot-scope="scope">
              {{scope.row.closeddate}}
            </template>
          </el-table-column>
          <el-table-column label="关闭原因" width="130" prop="closedreason" sortable >
            <template slot-scope="scope">
              {{scope.row.closedreason}}
            </template>
          </el-table-column>

        </el-table>

        <!-- <el-pagination
          :current-page="1"
          :layout="layout"
          :page-size="50"
          :total="100"
          background
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        /> -->
      <div class="container">
    <el-pagination
      class="rear-page"
      :current-page="queryForm.pageNo"
      :page-size="queryForm.pageSize"
      layout="prev, pager, next, slot, jumper, sizes, total"
      :total="queryForm.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
      <!-- slot部分，跳转末页 -->
      <button
        class="lastPage"
        :disabled="queryForm.lastPageDisabled"
        @click="toLastPage"
      >
        <i class="el-icon-d-arrow-right"></i>
      </button>
    </el-pagination>
    <el-pagination
      class="ahead-page"
      :current-page="queryForm.pageNo"
      :page-size="queryForm.pageSize"
      layout="slot"
      :total="queryForm.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
      <!-- slot部分，跳转首页 -->
      <button
        class="firstPage"
        :disabled="queryForm.firstPageDisabled"
        @click="toFirstPage"
      >
        <i class="el-icon-d-arrow-left"></i>
      </button>
    </el-pagination>
    </div>
    </div>
    </div>
    </template>

    <script>
    import {deptSelect} from "../../../api/commonBiz";
    import {
      getTaskPage
    } from '@/api/taskQuery'
      export default {
        name: 'Task',
        data(){
          return{
            tableData: [], // 表格数据列表
            listLoading: false,
            groupDict:[],
            queryForm: {
              status: 'all',
              total: 0,
              pageSize: 50, //  展示数量
              firstPageDisabled: false, //  首页
              lastPageDisabled: true, //  末页
              createYear: '',
              createMonth: '',
              taskStarter: '',
              finishYear:'',
              finishMonth:'',
              completedBy:'',
              groupId:null,
              orderByColumn:'',
              sort:''
            },
            optionsData: [{
              key: '-1',
              value: 'all'
            }, {
              key: '0',
              value: 'wait'
            }, {
              key: '1',
              value: 'doing'
            }, {
              key: '2',
              value: 'done'
            }, {
            key: '3',
            value: 'pause'
            }, {
            key: '4',
            value: 'closed'
            }, {
            key: '5',
            value: 'cancel'
            }]
            }
        },
    watch: {
        // 分页 计算首页和末页
        queryForm: {
          handler(newVal) {
            let pages = Math.ceil(newVal.total / newVal.pageSize);
        if (pages === 0) {
          // 数据(totalResult)为0
          this.queryForm.firstPageDisabled = true; // 首页按钮是否禁用
          this.queryForm.lastPageDisabled = true; // 末页按钮是否禁用
        } else {
          this.queryForm.firstPageDisabled = newVal.pageNo === 1;
          this.queryForm.lastPageDisabled = newVal.pageNo === pages;
        }
          },
          // 一进页面就执行
          immediate: true,
          deep: true,
        },
      },
  methods:{
    // 点击排序
    onSortChange(column) {
      this.queryForm.orderByColumn = column.prop;
      this.queryForm.sort = column.order === "ascending" ? 'asc' : 'desc';
      // 获取后台列表数据
      this.getList()
    },
    //  改变页码
    handlePageChange({ pageNo, pageSize }) {
      this.queryForm.pageNo = pageNo;
      this.queryForm.pageSize = pageSize;
    },

    //  前往首页
    toFirstPage() {
      this.handleCurrentChange(1);
    },

    //  前往末页
    toLastPage() {
      let max = Math.ceil(this.queryForm.total / this.queryForm.pageSize);
      this.handleCurrentChange(max);
    },

    // 选择状态时间
    handleStatusSelectChange(val){
        console.log('val = ',val)
    },

    // 查询
    handleQuery(){
        this.getList();
    },

    // 重置
    resetQuery(){
      this.queryForm = {
          status: 'all',
          pageNo: 1,
          total: 0,
          pageSize: 50, //  展示数量
          firstPageDisabled: false, //  首页
          lastPageDisabled: true, //  末页
          taskStarter: '',
          createYear: '',
          createMonth: '',
          finishYear:'',
          finishMonth:'',
          completedBy:'',
          groupId:null,
          orderByColumn:'',
          sort:''
      }
      this.$refs.tableList.clearSort();
      const nowDate = new Date();
      this.queryForm.createYear = nowDate.getFullYear();
      this.queryForm.createMonth = nowDate.getMonth() + 1;
      this.queryForm.groupId = this.groupDict[0].deptId;
      this.handleQuery();
    },
    // 当前页面
    handleCurrentChange(val) {
        this.queryForm.pageNo = val
        console.log('handleCurrentChange',val)
        this.getList()
      },

    // 页数
    handleSizeChange(val) {
        this.queryForm.pageSize = val
        console.log('handleSizeChange',val)
        this.getList()
      },

    // 下拉框选择
    handleSelectionChange(val){

    },

    async init() {
      this.getDeptList()
    },

        // 初始化
    async getList(){
    this.listLoading = true
    this.tableData = []
    console.log('status = ' ,this.queryForm.status, 'finishYear = ' ,this.queryForm.finishYear, 'finishMonth = ', this.queryForm.finishMonth)
    const resp = await getTaskPage(this.queryForm)
    this.formatResource(resp.data.records)
    this.listLoading = false
    this.queryForm.total = resp.data.total * 1
    this.queryForm.pageNo = resp.data.current
    this.listLoading = false
    },
    formatResource(data){
        if (data) {
          data.forEach((item) => {
            let resource = {
            id: item.id,
            pname: item.pname,
            title: item.title,
            name: item.name,
            sonName: item.sonName,
            status: item.status,
            type: item.type,
            taskStarter: item.taskStarter,
            assigneddate: item.assigneddate,
            finishedby: item.finishedby,
            finishedDate: item.finishedDate,
            closedby: item.closedby,
            closeddate: item.closeddate,
            closedreason: item.closedreason,
            realStarted: item.realStarted,
            consumed: item.consumed,
            activatedDate:item.activatedDate
          }
          this.tableData.push(resource)
        })
      }
    },
    // 获取当前年份月
    getDate(){
      const nowDate = new Date();
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
      };
      this.queryForm.createYear = date.year;
      this.queryForm.createMonth = date.month > 10 ? date.month : date.month;
    },
    /** 查询部门列表 */
    getDeptList() {
      var deptList = [];
      deptSelect().then(response => {
        deptList = response.data;
        let tecCenter = {deptName: "技术中心", deptId: 101}
        deptList.unshift(tecCenter)
        this.groupDict = deptList;
        this.queryForm.groupId = deptList[0].deptId;
      });
    },
    // 导出按钮操作
    handleExport() {
      this.download('task/export', {
        ...this.queryForm
      }, `任务明细_${new Date().getTime()}.csv`)
    },
    // 双击table表行
    handleTableRow(row) {
        const url = 'http://pm.qmqb.top/task-view-'+ row.id  +'.html/'
        console.log('url = ', url)
        window.open(url, "_blank");
  		}
      },
        created(){
          this.getDate();
          this.init();
        },
        mounted(){
        }

    }
    </script>

    <style lang=scss scoped>
    .app-container{
        padding: 20px;
    }
    .table {
        background-color: #fff;
        .tiltle-cn {
          color: #2184d8;
        }
        .control-cell {
          display: flex;
          flex-direction: row;
          .control-cell-item {
            margin-left: 10px;
          }
        }
      }

     .container{
        float: left;
     }
      .el-pagination {
      float: right;
      margin-top: 10px;
    }
    .el-pagination.ahead-page {
      padding-right: 0;
    }
    .el-pagination.rear-page {
      padding-left: 0;
    }
    .firstPage,
    .lastPage {
      background-color: white;
      cursor: pointer;
    }
    </style>
