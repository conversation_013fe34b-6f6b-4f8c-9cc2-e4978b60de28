<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="API渠道基础配置ID" prop="apiBaseConfId">
        <el-input
          v-model="queryParams.apiBaseConfId"
          placeholder="请输入API渠道基础配置ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模板字典值" prop="templateDictValue">
        <el-input
          v-model="queryParams.templateDictValue"
          placeholder="请输入模板字典值"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="API映射字典值" prop="apiDictValue">
        <el-input
          v-model="queryParams.apiDictValue"
          placeholder="请输入API映射字典值"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:dictMappingConf:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:dictMappingConf:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:dictMappingConf:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:dictMappingConf:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dictMappingConfList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" v-if="false"/>
      <el-table-column label="API渠道基础配置ID" align="center" prop="apiBaseConfId" />
      <el-table-column label="字典类型" align="center" prop="dictType" />
      <el-table-column label="模板字典值" align="center" prop="templateDictValue" />
      <el-table-column label="模板字典名称" align="center" prop="templateDictName" />
      <el-table-column label="API映射字典值" align="center" prop="apiDictValue" />
      <el-table-column label="API映射字典名称" align="center" prop="apiDictName" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:dictMappingConf:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:dictMappingConf:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改API字典关系映射配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="API渠道基础配置ID" prop="apiBaseConfId">
          <el-input v-model="form.apiBaseConfId" placeholder="请输入API渠道基础配置ID" />
        </el-form-item>
        <el-form-item label="模板字典值" prop="templateDictValue">
          <el-input v-model="form.templateDictValue" placeholder="请输入模板字典值" />
        </el-form-item>
        <el-form-item label="模板字典名称" prop="templateDictName">
          <el-input v-model="form.templateDictName" placeholder="请输入模板字典名称" />
        </el-form-item>
        <el-form-item label="API映射字典值" prop="apiDictValue">
          <el-input v-model="form.apiDictValue" placeholder="请输入API映射字典值" />
        </el-form-item>
        <el-form-item label="API映射字典名称" prop="apiDictName">
          <el-input v-model="form.apiDictName" placeholder="请输入API映射字典名称" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDictMappingConf, getDictMappingConf, delDictMappingConf, addDictMappingConf, updateDictMappingConf } from "@/api/system/dictMappingConf";

export default {
  name: "DictMappingConf",
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // API字典关系映射配置表格数据
      dictMappingConfList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        apiBaseConfId: undefined,
        dictType: undefined,
        templateDictValue: undefined,
        apiDictValue: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        id: [
          { required: true, message: "主键不能为空", trigger: "blur" }
        ],
        apiBaseConfId: [
          { required: true, message: "API渠道基础配置ID不能为空", trigger: "blur" }
        ],
        dictType: [
          { required: true, message: "字典类型不能为空", trigger: "change" }
        ],
        templateDictValue: [
          { required: true, message: "模板字典值不能为空", trigger: "blur" }
        ],
        templateDictName: [
          { required: true, message: "模板字典名称不能为空", trigger: "blur" }
        ],
        apiDictValue: [
          { required: true, message: "API映射字典值不能为空", trigger: "blur" }
        ],
        apiDictName: [
          { required: true, message: "API映射字典名称不能为空", trigger: "blur" }
        ],
        remark: [
          { required: true, message: "备注不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询API字典关系映射配置列表 */
    getList() {
      this.loading = true;
      listDictMappingConf(this.queryParams).then(response => {
        this.dictMappingConfList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        apiBaseConfId: undefined,
        dictType: undefined,
        templateDictValue: undefined,
        templateDictName: undefined,
        apiDictValue: undefined,
        apiDictName: undefined,
        remark: undefined,
        tenantId: undefined,
        revision: undefined,
        createdBy: undefined,
        createdTime: undefined,
        updatedBy: undefined,
        updatedTime: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加API字典关系映射配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getDictMappingConf(id).then(response => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = "修改API字典关系映射配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          if (this.form.id != null) {
            updateDictMappingConf(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addDictMappingConf(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除API字典关系映射配置编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delDictMappingConf(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/dictMappingConf/export', {
        ...this.queryParams
      }, `dictMappingConf_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
