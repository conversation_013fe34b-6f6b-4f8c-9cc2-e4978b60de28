import request from '@/utils/request'

// 查询业务类型列表
export function businessTypeList(query) {
  return request({
    url: '/system/businessType/list',
    method: 'get',
    params: query
  })
}

// 获取业务类型详细信息
export function businessTypeGet(id) {
  return request({
    url: '/system/businessType/' + id,
    method: 'get'
  })
}

// 新增业务类型
export function businessTypeAdd(data) {
  return request({
    url: '/system/businessType',
    method: 'post',
    data: data
  })
}

// 修改业务类型
export function businessTypeEdit(data) {
  return request({
    url: '/system/businessType',
    method: 'put',
    data: data
  })
}

// 删除业务类型
export function businessTypeDelete(id) {
  return request({
    url: '/system/businessType/' + id,
    method: 'delete'
  })
}
