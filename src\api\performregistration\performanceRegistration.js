import request from '@/utils/request'

// 查询绩效登记列表
export function listPerformanceRegistration(query) {
  return request({
    url: '/system/performanceRegistration/list',
    method: 'get',
    params: query
  })
}
// 查询绩效登记统计数据
export function performanceRegistrationStatistic(query) {
  return request({
    url: '/system/performanceRegistration/statistic',
    method: 'get',
    params: query
  })
}

// 查询绩效登记详细
export function getPerformanceRegistration(id) {
  return request({
    url: '/system/performanceRegistration/' + id,
    method: 'get'
  })
}

// 新增绩效登记
export function addPerformanceRegistration(data) {
  return request({
    url: '/system/performanceRegistration',
    method: 'post',
    data: data
  })
}

// 修改绩效登记
export function updatePerformanceRegistration(data) {
  return request({
    url: '/system/performanceRegistration',
    method: 'put',
    data: data
  })
}

// 删除绩效登记
export function delPerformanceRegistration(id) {
  return request({
    url: '/system/performanceRegistration/' + id,
    method: 'delete'
  })
}
