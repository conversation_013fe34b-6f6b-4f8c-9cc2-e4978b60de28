import request from '@/utils/request'

// 查询月度部门合作访谈列表
export function getInterviewList(query) {
  return request({
    url: '/process/monthlyDepartmentCooperationInterview/list',
    method: 'get',
    params: query
  })
}

// 查询月度部门合作访谈详细
export function getInterview(id) {
  return request({
    url: '/process/monthlyDepartmentCooperationInterview/' + id,
    method: 'get'
  })
}

// 新增月度部门合作访谈
export function addInterview(data) {
  return request({
    url: '/process/monthlyDepartmentCooperationInterview',
    method: 'post',
    data: data
  })
}

// 修改月度部门合作访谈
export function updateInterview(data) {
  return request({
    url: '/process/monthlyDepartmentCooperationInterview',
    method: 'put',
    data: data
  })
}

// 删除月度部门合作访谈
export function delInterview(id) {
  return request({
    url: '/process/monthlyDepartmentCooperationInterview/' + id,
    method: 'delete'
  })
}

// 导出月度部门合作访谈
export function exportInterview(query) {
  return request({
    url: '/process/monthlyDepartmentCooperationInterview/export',
    method: 'get',
    params: query
  })
}

// 批量删除月度部门合作访谈
export function delInterviews(ids) {
  return request({
    url: '/process/monthlyDepartmentCooperationInterview/' + ids,
    method: 'delete'
  })
}

// 更新访谈状态
export function updateInterviewStatus(data) {
  return request({
    url: '/process/monthlyDepartmentCooperationInterview/status',
    method: 'put',
    data: data
  })
}
