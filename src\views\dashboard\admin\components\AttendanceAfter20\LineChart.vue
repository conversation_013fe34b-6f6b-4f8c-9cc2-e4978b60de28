<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts';

export default {
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    chartData: {
      type: Array, // 接收当前展示的数据数组
      required: true
    },
    columns: {
      type: Array,
      required: true
    },
    title: { // 新增标题的 Prop
      type: String,
      default: '' // 默认为空，如果不传递，则不会显示标题
    },
    ordinateName: { // 纵坐标名称
      type: String,
      default: '' // 默认为空，如果不传递，则不会显示
    }
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val,this.ordinateName,this.columns);
      }
    }
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el);
      this.setOptions(this.chartData,this.ordinateName,this.columns);
    },
    setOptions(data,ordinateName,columns) {
      const seriesData = Array.isArray(data)
        ? data.map(v => (Number(v) === 0 ? null : v))
        : [];
      this.chart.setOption({
        title: {
          text: this.title, // 添加图表标题
          left: 'center', // 标题居中
          top: 10, // 距离顶部的距离
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '3%',
          top: '18%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: columns
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: ordinateName,
            type: 'bar',
            barWidth: '60%',
            data: seriesData,
            itemStyle: {
              color: '#3888fa',
              normal: {
                label: {
                  show: true,
                  position: 'top',
                  distance: 6,
                  textStyle: { color: 'black', fontSize: 12 }
                }
              }
            }
          }
        ]
      });
    }
  }
};
</script>
