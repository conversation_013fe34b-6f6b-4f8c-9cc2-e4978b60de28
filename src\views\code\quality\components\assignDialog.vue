<!-- 处理弹窗 -->
<template>
  <div>
    <el-dialog title="代码质量问题指派" :visible.sync="visible" width="400px" @close="cancel">
      <el-form>
        <el-form-item label="指派给" prop="handleUserId">
          <el-select v-model="formData.handleUserId" placeholder="请选择处理人" style="width: 300px" filterable>
            <el-option v-for="(item, index) in processPersonList" :key="index" :value="item.id" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm" :loading="btnLoading">确认</el-button>
        <el-button @click="cancel">取消</el-button>
      </span>
      </el-dialog>
  </div>
</template>
<script>
import {
  listAllUserInGroup
} from "@/api/system/user"
import {
  scanProjectFileAssign
} from "@/api/Project"
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    assignData: {
      type: Object
    }
  },
  data() {
    return {
      visible: this.dialogVisible,
      btnLoading: false,
      processPersonList: [],
      formData: {
        fileIds: [],
        handleUserId: ''
      }
    }
  },
  watch: {
    dialogVisible (val) {
      this.visible = val
      if (this.visible) {
        this.listAllUserInGroup()
        this.formData = Object.assign(this.formData, this.assignData)
      }
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    // 取消
    cancel () {
      this.formData = {
        fileIds: [],
        handleUserId: ''
      }
      this.$emit('update:dialogVisible', false)
    },
    // 指派
    confirm () {
      this.btnLoading = true
      scanProjectFileAssign(this.formData).then((res) => {
        this.btnLoading = false
        if (res.code === 200) {
          this.$message.success('操作成功')
          this.cancel()
          this.$emit('callback')
        } else {
          this.$message.error(res.msg)
        }
      }).catch((err) => {
        this.btnLoading = false
      })
    },
    // 获取处理人列表
    listAllUserInGroup () {
      listAllUserInGroup().then(res => {
        if (res.code === 200) {
          this.processPersonList = res.data.map(item => {
            const { userId, nickName } = item
            return { id: userId, name: nickName }
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  },
};
</script>
<style lang="scss" scoped>
</style>
