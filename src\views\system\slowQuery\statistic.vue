<template>
  <div class="app-container">
    <el-form :inline="true" :model="queryParams" style="margin-left: 3%;">
      <el-form-item label="年份">
        <el-select v-model="queryParams.year" placeholder="选择年份" >
          <el-option
            v-for="year in yearOptions"
            :key="year"
            :label="year"
            :value="year">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" clearable>
          <el-option v-for="item in dict.type.code_quality_file_status"
            :key="item.value"
            :value="item.value"
            :label="item.label"
            style="width: 240px"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="fetchData" size="small">查询</el-button>
        <el-button @click="resetQuery" size="small">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-tag type="info" size="large">
          最新更新时间：{{parseTime(updateTime)}}
        </el-tag>
      </el-form-item>
    </el-form>
    <div ref="chartDom" style="width: 100%; height: 70vh;"></div>
  </div>
</template>

<script>
import { slowSqlStat } from '@/api/system/slowQuery'
import * as echarts from 'echarts';

export default {
  name: 'SlowSqlStatistic',
  dicts: ['code_quality_file_status'],
  data() {
    return {
      barChart: null,
      queryParams: {
        year: new Date().getFullYear(),
        status: null
      },
      updateTime: '',
      yearOptions: [],
      chartData: [],
    }
  },
  mounted() {
    const currentYear = new Date().getFullYear();
    for (let i = 0; i < 5; i++) {
      this.yearOptions.push(currentYear - i);
    }
    this.initChart()
  },
  beforeDestroy() {
    if (this.barChart) {
      this.barChart.dispose();
    }
  },
  methods: {
    initChart() {
      this.barChart = echarts.init(this.$refs.chartDom);
      window.addEventListener('resize', this.resizeChart);
      this.fetchData()
    },
    resizeChart() {
      if (this.barChart) {
        this.barChart.resize();
      }
    },
    fetchData() {
      slowSqlStat(this.queryParams).then(response => {
        this.chartData = response.data;
        this.updateChart();
        this.updateTime = new Date();
      }).catch(error => {
        console.error('获取慢SQL统计失败:', error);
        this.$message.error('获取数据失败');
      });
    },
    resetQuery() {
      this.queryParams.year = new Date().getFullYear()
      this.queryParams.status = null
      this.fetchData()
    },
    updateChart() {
      if (!this.barChart || !this.chartData.length) return;
      const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
      const series = this.chartData.map(db => {
        return {
          name: db.dbName,
          type: 'bar',
          stack: 'total',
          barWidth: '60%',
          label: {
            show: true,
            position: 'inside',
            formatter: params => {
              const value = params.value;
              return value > 0 ? value : '';
            }
          },
          data: db.monthOfSlowSqlCount
        };
      });
      const option = {
        title: {
          text: `慢SQL综合统计`,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function (params) {
            let result = params[0].axisValue + '<br/>';
            params.forEach(param => {
              result += `${param.seriesName}: ${param.value}<br/>`;
            });
            return result;
          }
        },
        legend: {
          data: this.chartData.map(db => db.dbName),
          bottom: 10
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: months
        },
        yAxis: {
          type: 'value'
        },
        series: series
      };
      this.barChart.setOption(option);
    }
  }
}
</script>
<style scoped>

</style>
