<template>
  <div>
    <div className="header-charts">
      <div id="myEcharts" style="width:100%;height:500px"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
var codeChart;
export default {
  name: 'Header',
  props: ['codeTeamData'],
  components: {},
  data() {
    return {
      myEcharts: {},
      nameData: [],
      additonalsLineData: [],
      deleteLineData: [],
      codeFrontData: [],
      queryForm: {
        beginDate: '',
        endDate: '',
        managered: false,
      }
    }
  },
  watch: {},
  computed: {},
  methods: {
    parentMsg: function (data) {
      this.codeFrontData = data
      this.drawLine(data)
    },
    drawLine (data) {
      let nickNameArr = []
      let p0 = []
      let p1 = []
      let p2 = []
      if (data !== undefined) {
        data.forEach((item, index) => {
          nickNameArr.push(item.month)

          p0.push(item.levelRecords[0].num)
          p1.push(item.levelRecords[1].num)
          p2.push(item.levelRecords[2].num)
        })
      }
      const newData = this.reduceData(data)
      var chartDom = document.getElementById('myEcharts');
      // 如果初始化过 echarts 实例，销毁。
      if (codeChart != null && codeChart != "" && codeChart != undefined) {
        codeChart.dispose();
      }
      // 基于准备好的 dom ，重新初始化 echarts 实例
      codeChart = echarts.init(chartDom);
      var option;
      var text = '组内预警级别趋势图';
      var series = [];
      var legendData = [];
      data.forEach((element) => {
        var obj = {
          name: element.nickName,
        }
        legendData.push(obj);
      })
      data.forEach(element => {
        var obj = {
          label: {
            show: true,
            fontWeight: 'bold'
          },
          name: element.name,
          type: 'line',
          data: element.data
        }
        series.push(obj)
      })
      option = {
        title: {
          left: 'center',
          text: '个人预警级别趋势情况'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          left: 'left',
          data: ['P0', 'P1', 'P2']
        },
        // legend: {
        //   top: 'bottom',
        //   data: legendData
        // },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: newData.month
        },
        yAxis: {
          name: '预警个数',
          type: 'value'
        },
        series: [
          {
            name: 'P0',
            type: 'line',
            data: p0
          },
          {
            name: 'P1',
            type: 'line',
            data: p1
          },
          {
            name: 'P2',
            type: 'line',
            data: p2
          }
        ]
      };


      option && codeChart.setOption(option);


    },
    // 重组数组对象
    reduceData (responseData) {
      return responseData.reduce((total, currentValue) => {
        return {
          ...total,
          ...Object.keys(currentValue).reduce((_r, _c) => { // Object.keys() 返回一个给定对象的属性名
            if (total[_c]) {
              total[_c].push(currentValue[_c])
            } else {
              total[_c] = [currentValue[_c]]
            }
            return total
          }, {})
        }
      }, {})
    },
  },
  created() {
  },
  activated() {
  },
  mounted() {
    this.drawLine(this.codeFrontData)
  },
  beforeDestroy() {
  }
}
</script>

<style scoped>
</style>
