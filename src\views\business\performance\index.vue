<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="人员类型:">
        <el-select
          v-model="queryParams.personType"
          placeholder="选择人员类型"
          clearable
          size="small"
          style="width: 160px"
        >
          <el-option
            v-for="dict in personTypeDict"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属组:">
        <el-select
          v-model="queryParams.groupId"
          placeholder="选择组别"
          clearable
          size="small"
          style="width: 160px"
        >
          <el-option
            v-for="dict in groupDict"
            :key="dict.deptId"
            :label="dict.deptName"
            :value="dict.deptId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="完成年份:" prop="roleName">
        <el-input
          v-model="queryParams.year"
          placeholder="请输入年份"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="完成月份:" prop="roleKey">
        <el-input
          v-model="queryParams.month"
          placeholder="请输入月份"
          clearable
          style="width: 120px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSearch">查询</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSearchBest">最优绩效查询</el-button>
        <el-button type="warning" plain icon="el-icon-download" @click="handleExport">导出</el-button>
      </el-form-item>
    </el-form>

    <el-table   :data="perfStatList" stripe border height="calc(100vh - 230px )" :cell-style="changeCellStyle">
      <el-table-column label="编号" align="center" width="50" fixed>
        <template slot-scope="scop">
          {{scop.$index+1}}
        </template>
      </el-table-column>
      <el-table-column align="center" label="年份" prop="workYear" width="80" fixed/>
      <el-table-column align="center" label="月份" prop="workMonth" :show-overflow-tooltip="true" width="80" fixed/>
      <el-table-column align="center" label="姓名" prop="workUsername" :show-overflow-tooltip="true" width="80" fixed/>
      <el-table-column align="center" label="工作时长(小时)" prop="kqAttendanceWorkTime" :show-overflow-tooltip="true" width="150" sortable :sort-method="(a,b)=>{return a.kqAttendanceWorkTime - b.kqAttendanceWorkTime}" fixed/>
      <el-table-column align="center" label="每日工时(工作日)" prop="kqDayAttendanceWorkTime" :show-overflow-tooltip="true" width="155" sortable :sort-method="(a,b)=>{return a.kqDayAttendanceWorkTime - b.kqDayAttendanceWorkTime}" fixed/>
      <el-table-column align="center" label="文档数" prop="docCount" :show-overflow-tooltip="true" width="120" sortable fixed/>
      <el-table-column align="center" label="完成的任务" prop="workDoneClosedTaskCount" :show-overflow-tooltip="true" width="130" sortable/>
      <el-table-column align="center" label="开发人员任务总数" prop="allWorkTaskCount" :show-overflow-tooltip="true" width="160" sortable/>
      <el-table-column align="center" label="测试人员任务总数" prop="testerAllWorkCount" :show-overflow-tooltip="true" width="160" sortable/>
      <el-table-column align="center" label="进行中的任务" prop="workDoingTaskCount" :show-overflow-tooltip="true" width="130" sortable/>
      <el-table-column align="center" label="解决bug数" prop="workResolveBugCount" :show-overflow-tooltip="true" width="130" sortable/>
      <el-table-column align="center" label="小时/每任务" prop="timePerTask" :show-overflow-tooltip="true" width="130" sortable :sort-method="(a,b)=>{return a.timePerTask - b.timePerTask}"/>
      <el-table-column align="center" label="完成执行用例数量" prop="workCaseCount" :show-overflow-tooltip="true" width="160" sortable />
      <el-table-column align="center" label="关闭bug数" prop="workCloseBugCount" :show-overflow-tooltip="true" width="130" sortable/>
      <el-table-column align="center" label="小时/每测试任务" prop="timePerTestTask" :show-overflow-tooltip="true" width="160" sortable :sort-method="(a,b)=>{return a.timePerTestTask - b.timePerTestTask}"/>
      <el-table-column align="center" label="旷工天数" prop="kqAbsenteeismDays" :show-overflow-tooltip="true" width="110" sortable/>
      <el-table-column align="center" label="出勤天数" prop="kqAttendanceDays" :show-overflow-tooltip="true" width="130" sortable/>
      <el-table-column align="center" label="迟到时长(分钟)" prop="kqLateMinute" :show-overflow-tooltip="true" width="150" sortable/>
      <el-table-column align="center" label="加班时长(小时)" prop="kqOvertimeApproveCount" :show-overflow-tooltip="true" width="160" sortable/>
      <el-table-column align="center" label="迟到次数" prop="kqLateCount" :show-overflow-tooltip="true" width="110" sortable/>
      <el-table-column align="center" label="严重迟到时长" prop="kqYzLateTimes" :show-overflow-tooltip="true" width="130" sortable/>
      <el-table-column align="center" label="严重迟到次数" prop="kqYzLateCount" :show-overflow-tooltip="true" width="130" sortable/>
      <el-table-column align="center" label="早退次数" prop="kqLeaveEarlyCount" :show-overflow-tooltip="true" width="130" sortable/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listPerfStat } from "@/api/business/performance";
import {getDicts} from "../../../api/system/dict/data";
import {deptSelect} from "../../../api/commonBiz";

export default {
  name: "Performance",
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      taskList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      personTypeDict:[],
      groupDict:[],
      perfStatList:[],
      queryParams: {
        pageNum: 1,
        pageSize: 200,
        year:0,
        month:0,
        personType:"1",
        groupId:null,
        bestQuery:0,
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    handleSearch() {
      if(this.queryParams.personType === '') {
        alert("请选择人员类型!")
        return
      }
      if(this.queryParams.groupName === '') {
        alert("请选择组别!")
        return
      }
      if(!/^\d{4}$/.test(this.queryParams.year)){
        alert("请输入正确的年份");
        return;
      }
      if (!(/(^[1-9]\d*$)/.test(this.queryParams.month))) {
        alert("请输入正确的月份");
        return
      }
      if (!(this.queryParams.month < 13)) {
        alert("请输入正确的月份");
        return
      }
      this.queryParams.bestQuery = 0
      listPerfStat(this.queryParams).then(response => {
        this.perfStatList = response.rows
        this.total = response.total;
      })
    },
    handleSearchBest() {
      if(this.queryParams.personType === '') {
        alert("请选择人员类型!")
        return
      }
      if(this.queryParams.groupName === '') {
        alert("请选择组别!")
        return
      }
      if(!/^\d{4}$/.test(this.queryParams.year)){
        alert("请输入正确的年份");
        return;
      }
      if (!(/(^[1-9]\d*$)/.test(this.queryParams.month))) {
        alert("请输入正确的月份");
        return
      }
      if (!(this.queryParams.month < 13)) {
        alert("请输入正确的月份");
        return
      }
      this.queryParams.bestQuery = 1
      listPerfStat(this.queryParams).then(response => {
        this.perfStatList = response.rows
        this.total = response.total;
      })
    },
    async init() {
      await getDicts("person_type").then(response => {
        this.personTypeDict = response.data
      })
      this.getDeptList()
      this.getNowDate()
      await listPerfStat(this.queryParams)
    },
    getNowDate() {
      const timeOne = new Date()
      const year = timeOne.getFullYear()
      let month = timeOne.getMonth() + 1
      this.queryParams.year = year
      this.queryParams.month = month
    },
    changeCellStyle({ row, column, rowIndex, columnIndex }) {
      if (row.workDoneClosedTaskCount > 20 && columnIndex == 7) {//设置单个
        return 'background: #0eb904'
      }
      if (row.workDoneClosedTaskCount <= 20 && row.workDoneClosedTaskCount > 10 && columnIndex == 7) {//设置单个
        return 'background: #a0f79b'
      }
      if (row.workDoneClosedTaskCount >= 0 && row.workDoneClosedTaskCount <= 10 && columnIndex == 7) {//设置单个
        return 'background: #ee5656'
      }
      if (columnIndex == 8) {//设置单个
        return 'background: #a0f79b'
      }
      if (row.testerAllWorkCount > 20 && columnIndex == 9) {//设置单个
        return 'background: #0eb904'
      }
      if (row.testerAllWorkCount <= 20 && row.testerAllWorkCount > 10 && columnIndex == 9) {//设置单个
        return 'background: #a0f79b'
      }
      if (row.testerAllWorkCount >= 0 && row.testerAllWorkCount <= 10 && columnIndex == 9) {//设置单个
        return 'background: #ee5656'
      }
    },

    /** 查询【请填写功能名称】列表 */
    getList() {
      this.loading = true;
      listPerfStat(this.queryParams).then(response => {
        this.perfStatList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询部门列表 */
    getDeptList() {
      var deptList = [];
      deptSelect().then(response => {
        deptList = response.data;
        let tecCenter = {deptName: "技术中心", deptId: 101}
        deptList.unshift(tecCenter)
        this.groupDict = deptList;
        this.queryParams.groupId = deptList[0].deptId;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('business/imsPerformStat/exportPerfStatList', {
        ...this.queryParams
      }, `综合绩效统计_${new Date().getTime()}.csv`)
    }
  }
};
</script>

