<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryParams" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="年份" prop="year">
        <el-select v-model="queryParams.year" placeholder="请选择年份" value-key="value">
          <el-option
            v-for="item in yearList"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="预警级别" prop="warnLevel">
        <el-select v-model="queryParams.warnLevel" placeholder="请选择预警级别">
          <el-option :value="null" label="全部"></el-option>
          <el-option value="0" label="P0"></el-option>
          <el-option value="1" label="P1"></el-option>
          <el-option value="2" label="P2"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="组别" prop="deptId">
        <el-select
          filterable
          allow-create
          :filter-method="filterMethod"
          v-model="queryParams.deptId"
          placeholder="请输入"
          clearable
          @change="getUserSelects(queryParams.deptId)"
        >
          <el-option
            v-for="item in optionsGroupData"
            :key="item.value"
            :value="item.value"
            :label="item.name"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="姓名" prop="userId">
        <el-select
          filterable
          allow-create
          :filter-method="filterMethod"
          v-model="queryParams.userId"
          placeholder="请输入"
          clearable
        >
          <el-option
            v-for="item in optionsMemberData"
            :key="item.userId"
            :value="item.userId"
            :label="item.nickName"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery()">查询</el-button>

      </el-form-item>
    </el-form>
    <!--为echarts准备一个具备大小的容器dom-->
    <div id="chart" style="width: 1650px;height: 600px;"></div>
    <el-header style="weight: 100%;height:500px;">
      <statis-header ref="header" :codeTeamData="codeTeamData" />
    </el-header>
  </div>

</template>

<script>
import {listHistogram,getWarnGroupSelets,getUserSelects} from "@/api/warn/personalAnalysis";
import {listUser} from "@/api/system/user";
import * as echarts from 'echarts/core';
import {GridComponent, LegendComponent, TitleComponent, ToolboxComponent, TooltipComponent} from 'echarts/components';
import {LineChart} from 'echarts/charts';
import {UniversalTransition} from 'echarts/features';
import {CanvasRenderer} from 'echarts/renderers';
// import StatisHeader from "./StatisHeader";
import StatisHeader from "../../warn/personalAnalysis/StatisHeader";
import StatisFooter from "../../code/statistics/components/StatisFooter";
import StatisMiddle from "../../code/statistics/components/StatisMiddle";
import {getDicts} from "../../../api/system/dict/data";

echarts.use([
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  LineChart,
  CanvasRenderer,
  UniversalTransition
]);

var codeChart;

export default {
  name: "personalAnalysis",
  components: {
    StatisHeader
  },
  data() {
    return {
      // 团队数据
      codeTeamData: [],
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 查询参数
      queryParams: {
        deptId: 103,
        deptName: null,
        warnLevel: null,
        userId: '',
        year: ''
      },
      // 组别
      optionsGroupData: [],
      // 组员
      optionsMemberData: [],
      // 查询参数
      groupDict:[],
      yearList: [],
      deptList: [],
      changeTimeS: '',
      changeTimeE: ''
    };
  },
  async created () {
    this.getYearList();
    this.init();
    // this.getDeptList();
    await this.getWarnGroupSelets();
    await this.getUserSelects(this.queryParams.deptId)
    this.handleQuery()
  },


  methods: {
    /** 查询年份列表 */
    getYearList() {
      this.loading = true;
      var date = new Date();
      var year = date.getFullYear();
      //起始年份
      var startYear = year - 3;
      //结束年份
      var endYear = year;
      var years = [];
      for (var i = startYear; i <= endYear; i++) {
        var obj = {
          label: i,
          value: i,
        }
        years.push(obj);
      }
      this.yearList = years;
      this.queryParams.year = year;

      this.loading = false;
    },
    filterMethod (val) {
      this.queryParams.deptId = val
    },
    /** 查询预警级别下拉框 */
    getWarnGroupSelets(){
      getWarnGroupSelets().then(response => {
        this.optionsGroupData = response.data;
      });
    },
    /** 查询组员 */
    async getUserSelects(deptId){
      await getUserSelects({deptId}).then(response => {
        if (response.rows.length > 0) {
          this.optionsMemberData = response.rows;
          this.queryParams.userId = this.optionsMemberData[0].userId
        } else {
          this.queryParams.userId = '';
          this.optionsMemberData = [];
        }
      });
    },
    /** 查询部门列表 */
    // getDeptList() {
    //   this.loading = true;
    //   var deptList = [];
    //   statisticsDept().then(response => {
    //     deptList = response.data;
    //     this.deptList = deptList;
    //     this.queryParams.deptId = deptList[0].deptId;
    //     this.getStatisticsLine()
    //     this.loading = false;
    //   });
    // },
    /** 查询年度数据 */
    getStatisticsLine() {

      this.loading = true;
      listHistogram(this.queryParams).then(response => {
        console.log(1111)
          this.loadChart(response.data.singleRecords);
          this.codeTeamData = response.data.multipleRecords
          this.$refs.header.parentMsg(this.codeTeamData);
          this.loading = false;
        }
      );
    },
    async init() {
      await getDicts("group").then(response => {
        this.groupDict = response.data
        this.groupDict.shift()
      })

    },
    // getList(){
    //   listHistogram(this.queryParams).then(response => {
    //     // this.$refs.footer.parentMsg(this.codeFrontData);
    //   });
    // },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getStatisticsLine();
      // this.getList()
    },

    /** 加载折线图 */
    loadChart(data) {

      const newData = this.reduceData(data)
      var chartDom = document.getElementById('chart');
      // 如果初始化过 echarts 实例，销毁。
      if (codeChart != null && codeChart != "" && codeChart != undefined) {
        codeChart.dispose();
      }
      // 基于准备好的 dom ，重新初始化 echarts 实例
      codeChart = echarts.init(chartDom);
      var option;
      option = {
        title: {
          left: 'center',
          text: '个人预警总数趋势情况'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          left: 'left',
          data: JSON.parse(JSON.stringify(newData.month))
        },
        xAxis: {
          type: 'category',
          data: JSON.parse(JSON.stringify(newData.month))
        },
        yAxis: {
          name: '预警个数',
          type: 'value'
        },
        series: [
          {
            data: JSON.parse(JSON.stringify(newData.num)),
            type: 'line'
          }
        ]
      };

      option && codeChart.setOption(option, true);
    },
    // 重组数组对象
    reduceData (responseData) {
      return responseData.reduce((total, currentValue) => {
        return {
          ...total,
          ...Object.keys(currentValue).reduce((_r, _c) => { // Object.keys() 返回一个给定对象的属性名
            if (total[_c]) {
              total[_c].push(currentValue[_c])
            } else {
              total[_c] = [currentValue[_c]]
            }
            return total
          }, {})
        }
      }, {})
    },

  }
};
</script>
