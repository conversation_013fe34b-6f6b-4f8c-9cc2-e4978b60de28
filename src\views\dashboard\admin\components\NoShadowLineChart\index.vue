<template>
  <div class="chart-root">
    <!-- 添加最后更新时间 -->
    <el-alert
      :title="updateTime"
      :closable=false
      type="info">
    </el-alert>
    <div slot="header" class="chart-header">
      <el-button-group style="min-height: 40px;">
        <template v-if="tabs && tabs.length > 0">
          <el-button
            v-for="tab in tabs"
            :key="tab.value"
            :type="tab.value === activeTab ? 'primary' : 'default'"
            size="mini"
            @click="switchTab(tab.value)"
          >
            {{ tab.label }}
          </el-button>
        </template>
        <el-button v-else size="mini" style="min-width: 48px;" type="primary">本年</el-button>
      </el-button-group>
    </div>
    <LineChart :chartData="currentData" :columns="columns" :title="title" :ordinateName="ordinateName"/>
  </div>
</template>

<script>
import LineChart from './LineChart.vue';

export default {
  components: { LineChart },
  data() {
    return {
      activeTab: '0', // 默认选中"开发"标签
    };
  },
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    updateTime: {
      type: String
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      required: true
    },
    columns: {
      type: Array,
      required: true
    },
    title: { // 新增标题的 Prop
      type: String,
      default: '' // 默认为空，如果不传递，则不会显示标题
    },
    tabs: {
      type: Array
    },
    ordinateName: { // 纵坐标名称
      type: String,
      default: '' // 默认为空，如果不传递，则不会显示
    }
  },
  computed: {
    currentData() {
      // 根据当前选中的标签返回对应数据
      return this.chartData;
    }
  },
  methods: {
    switchTab(tab) {
      this.activeTab = tab;
    }
  }
};
</script>

<style scoped>
.chart-root {
  width: 100%;
  height: 100%;
  min-height: 0;
  display: flex;
  flex-direction: column;
}
.chart-header {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
  margin-top: 10px;
}
.placeholder-text {
  color: #909399;
  padding: 4px 8px;
}
</style>
