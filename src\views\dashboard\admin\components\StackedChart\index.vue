<template>
  <div class="chart-root">
    <el-alert
      :title="updateTime"
      :closable="false"
      type="info">
    </el-alert>
    <StackedChartComponent :chart-data="chartData" :title="title" :x-axis="monthList" />
  </div>
</template>

<script>
import StackedChartComponent from './StackedChartComponent.vue'
import { getHomeTrend } from '@/api/system/homeTrend'

export default {
  name: 'StackedChart',
  components: {
    StackedChartComponent
  },
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      monthList: [],
      chartData: {},
      updateTime: ''
    }
  },
  created() {
    this.getAllData()
  },
  methods: {
    getAllData() {
      // 获取所有类型的数据
      const promises = [1, 2, 3, 4].map(type => getHomeTrend(type))
      Promise.all(promises).then(responses => {
        // 使用第一个响应的月份列表
        this.monthList = responses[0].data.monthList
        this.updateTime = '最后更新时间：' + responses[0].data.updateTime
        
        // 合并所有数据
        this.chartData = {
          1: responses[0].data.trendList,
          2: responses[1].data.trendList,
          3: responses[2].data.trendList,
          4: responses[3].data.trendList
        }
      })
    }
  }
}
</script>

<style scoped>
.chart-root {
  width: 100%;
  height: 100%;
  min-height: 0;
  display: flex;
  flex-direction: column;
}
.el-alert {
  margin-bottom: 10px;
}
</style> 